import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, AlertCircle, Search, TrendingUp } from 'lucide-react';
import { calculateSEOScore } from '../../utils/seoUtils';

const SEOAudit = ({ pageData, onClose }) => {
  const [auditResult, setAuditResult] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate audit process
    setTimeout(() => {
      const result = calculateSEOScore(pageData);
      setAuditResult(result);
      setLoading(false);
    }, 1500);
  }, [pageData]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'fail':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getGradeColor = (grade) => {
    switch (grade) {
      case 'A':
        return 'text-green-600 bg-green-100';
      case 'B':
        return 'text-blue-600 bg-blue-100';
      case 'C':
        return 'text-yellow-600 bg-yellow-100';
      case 'D':
        return 'text-orange-600 bg-orange-100';
      case 'F':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <Search className="w-12 h-12 text-dexin-primary mx-auto mb-4 animate-spin" />
            <h3 className="text-xl font-semibold mb-2">Đang phân tích SEO...</h3>
            <p className="text-gray-600">Vui lòng đợi trong giây lát</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-6 h-6 text-dexin-primary" />
            <h2 className="text-xl font-semibold">SEO Audit Report</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XCircle className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Score Overview */}
          <div className="bg-gradient-to-r from-dexin-primary to-dexin-secondary rounded-lg p-6 text-white mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-2xl font-bold mb-2">SEO Score</h3>
                <p className="opacity-90">Trang: {pageData.title || 'Unknown Page'}</p>
              </div>
              <div className="text-right">
                <div className="text-4xl font-bold mb-2">
                  {auditResult.score}/{auditResult.maxScore}
                </div>
                <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${getGradeColor(auditResult.grade)}`}>
                  Grade {auditResult.grade}
                </div>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="mt-4">
              <div className="bg-white bg-opacity-20 rounded-full h-2">
                <div 
                  className="bg-white rounded-full h-2 transition-all duration-1000"
                  style={{ width: `${(auditResult.score / auditResult.maxScore) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Detailed Checks */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold mb-4">Chi tiết kiểm tra</h4>
            
            {auditResult.checks.map((check, index) => (
              <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    {getStatusIcon(check.status)}
                    <div>
                      <h5 className="font-medium text-gray-900">{check.name}</h5>
                      {check.suggestion && (
                        <p className="text-sm text-gray-600 mt-1">{check.suggestion}</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`text-sm font-medium ${
                      check.status === 'pass' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {check.points}/{check.status === 'pass' ? check.points : 
                        check.name === 'Title Length' ? 20 :
                        check.name === 'Meta Description' ? 15 :
                        check.name === 'Image Alt Text' ? 15 :
                        10
                      } điểm
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Recommendations */}
          <div className="mt-8 bg-blue-50 rounded-lg p-6">
            <h4 className="text-lg font-semibold text-blue-900 mb-4">Khuyến nghị cải thiện</h4>
            <div className="space-y-3">
              {auditResult.score < 90 && (
                <div className="flex items-start space-x-3">
                  <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-blue-800 font-medium">Tối ưu hóa cơ bản</p>
                    <p className="text-blue-700 text-sm">
                      Hoàn thiện các yếu tố SEO cơ bản như title, meta description và alt text cho hình ảnh.
                    </p>
                  </div>
                </div>
              )}
              
              {auditResult.score < 80 && (
                <div className="flex items-start space-x-3">
                  <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-blue-800 font-medium">Cải thiện nội dung</p>
                    <p className="text-blue-700 text-sm">
                      Tăng độ dài nội dung và thêm liên kết nội bộ để cải thiện trải nghiệm người dùng.
                    </p>
                  </div>
                </div>
              )}
              
              {auditResult.score < 70 && (
                <div className="flex items-start space-x-3">
                  <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-blue-800 font-medium">Structured Data</p>
                    <p className="text-blue-700 text-sm">
                      Thêm structured data để giúp Google hiểu rõ hơn về nội dung trang web.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex justify-end space-x-4">
            <button
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Đóng
            </button>
            <button
              onClick={() => window.open('https://developers.google.com/search/docs/fundamentals/seo-starter-guide', '_blank')}
              className="px-6 py-2 bg-dexin-primary text-white rounded-lg hover:bg-dexin-primary/90 transition-colors"
            >
              Tìm hiểu thêm
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SEOAudit;
