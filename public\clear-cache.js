// DEXIN Cache Clearing Script
// Chạy script này trong Console của Chrome để clear cache

(function() {
  'use strict';
  
  console.log('🧹 Bắt đầu dọn dẹp cache DEXIN...');
  
  // Clear localStorage
  try {
    localStorage.clear();
    console.log('✅ Đã xóa localStorage');
  } catch (e) {
    console.warn('⚠️ Không thể xóa localStorage:', e);
  }
  
  // Clear sessionStorage
  try {
    sessionStorage.clear();
    console.log('✅ Đã xóa sessionStorage');
  } catch (e) {
    console.warn('⚠️ Không thể xóa sessionStorage:', e);
  }
  
  // Clear IndexedDB (if any)
  if ('indexedDB' in window) {
    try {
      indexedDB.databases().then(databases => {
        databases.forEach(db => {
          indexedDB.deleteDatabase(db.name);
        });
      });
      console.log('✅ Đã xóa IndexedDB');
    } catch (e) {
      console.warn('⚠️ Không thể xóa IndexedDB:', e);
    }
  }
  
  // Unregister Service Workers
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(registrations => {
      registrations.forEach(registration => {
        registration.unregister().then(success => {
          if (success) {
            console.log('✅ Đã hủy Service Worker');
          }
        });
      });
    });
  }
  
  // Clear Caches API
  if ('caches' in window) {
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          return caches.delete(cacheName);
        })
      );
    }).then(() => {
      console.log('✅ Đã xóa tất cả cache');
    }).catch(e => {
      console.warn('⚠️ Không thể xóa cache:', e);
    });
  }
  
  console.log('🎉 Hoàn thành! Hãy reload trang (Ctrl+F5 hoặc Cmd+Shift+R)');
  console.log('📝 Nếu vẫn gặp lỗi, hãy thử:');
  console.log('   1. Mở DevTools (F12)');
  console.log('   2. Vào tab Application/Storage');
  console.log('   3. Click "Clear storage" và chọn "Clear site data"');
  console.log('   4. Hard refresh (Ctrl+Shift+F5)');
  
})(); 