import React, { useState } from 'react';
import { motion } from 'motion/react';
import { 
  Package, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Truck,
  Eye,
  Calendar,
  CreditCard
} from 'lucide-react';

const OrderHistory = () => {
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Mock data cho đơn hàng
  const orders = [
    {
      id: 'DH001',
      date: '2024-01-15',
      status: 'delivered',
      total: 2450000,
      items: [
        { name: 'Bàn gỗ sồi', quantity: 1, price: 1200000 },
        { name: 'Ghế văn phòng', quantity: 2, price: 625000 }
      ],
      shippingAddress: '123 Nguyễn Văn A, Quận 1, TP.HCM'
    },
    {
      id: 'DH002',
      date: '2024-01-20',
      status: 'shipping',
      total: 890000,
      items: [
        { name: 'Đèn bàn LED', quantity: 1, price: 450000 },
        { name: 'Tủ sách mini', quantity: 1, price: 440000 }
      ],
      shippingAddress: '456 Lê Văn B, Quận 3, TP.HCM'
    },
    {
      id: 'DH003',
      date: '2024-01-25',
      status: 'processing',
      total: 3200000,
      items: [
        { name: 'Sofa 3 chỗ', quantity: 1, price: 3200000 }
      ],
      shippingAddress: '789 Trần Văn C, Quận 7, TP.HCM'
    },
    {
      id: 'DH004',
      date: '2024-01-28',
      status: 'cancelled',
      total: 750000,
      items: [
        { name: 'Bàn cà phê', quantity: 1, price: 750000 }
      ],
      shippingAddress: '321 Phạm Văn D, Quận 5, TP.HCM'
    }
  ];

  const statusConfig = {
    all: { label: 'Tất cả', color: 'gray', icon: Package },
    processing: { label: 'Đang xử lý', color: 'yellow', icon: Clock },
    shipping: { label: 'Đang giao', color: 'blue', icon: Truck },
    delivered: { label: 'Đã giao', color: 'green', icon: CheckCircle },
    cancelled: { label: 'Đã hủy', color: 'red', icon: XCircle }
  };

  const filteredOrders = selectedStatus === 'all' 
    ? orders 
    : orders.filter(order => order.status === selectedStatus);

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status) => {
    const config = statusConfig[status];
    const Icon = config.icon;
    
    return (
      <span className={`
        inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
        ${status === 'processing' ? 'bg-yellow-100 text-yellow-800' : ''}
        ${status === 'shipping' ? 'bg-blue-100 text-blue-800' : ''}
        ${status === 'delivered' ? 'bg-green-100 text-green-800' : ''}
        ${status === 'cancelled' ? 'bg-red-100 text-red-800' : ''}
      `}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </span>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-lg shadow-lg overflow-hidden"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4">
        <h2 className="text-xl font-bold text-white">Lịch sử đơn hàng</h2>
        <p className="text-dexin-bg text-sm mt-1">Theo dõi tất cả đơn hàng của bạn</p>
      </div>

      {/* Filter tabs */}
      <div className="border-b border-gray-200">
        <div className="flex overflow-x-auto">
          {Object.entries(statusConfig).map(([status, config]) => {
            const Icon = config.icon;
            const isActive = selectedStatus === status;
            const count = status === 'all' ? orders.length : orders.filter(o => o.status === status).length;
            
            return (
              <button
                key={status}
                onClick={() => setSelectedStatus(status)}
                className={`
                  flex items-center space-x-2 px-4 py-3 border-b-2 transition-colors whitespace-nowrap
                  ${isActive 
                    ? 'border-dexin-primary text-dexin-primary bg-dexin-light-10' 
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                <span className="font-medium">{config.label}</span>
                <span className={`
                  px-2 py-1 rounded-full text-xs
                  ${isActive ? 'bg-dexin-primary text-white' : 'bg-gray-200 text-gray-600'}
                `}>
                  {count}
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Orders list */}
      <div className="p-6">
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Không có đơn hàng</h3>
            <p className="text-gray-500">
              {selectedStatus === 'all' 
                ? 'Bạn chưa có đơn hàng nào.' 
                : `Không có đơn hàng ${statusConfig[selectedStatus].label.toLowerCase()}.`
              }
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {filteredOrders.map((order) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
              >
                {/* Order header */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                  <div className="flex items-center space-x-4 mb-2 sm:mb-0">
                    <h3 className="text-lg font-semibold text-gray-900">#{order.id}</h3>
                    {getStatusBadge(order.status)}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(order.date)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <CreditCard className="w-4 h-4" />
                      <span className="font-semibold text-dexin-primary">{formatPrice(order.total)}</span>
                    </div>
                  </div>
                </div>

                {/* Order items */}
                <div className="space-y-2 mb-4">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <div>
                        <span className="font-medium text-gray-900">{item.name}</span>
                        <span className="text-gray-500 ml-2">x{item.quantity}</span>
                      </div>
                      <span className="font-medium text-gray-900">{formatPrice(item.price)}</span>
                    </div>
                  ))}
                </div>

                {/* Shipping address */}
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Địa chỉ giao hàng:</span> {order.shippingAddress}
                  </p>
                </div>

                {/* Actions */}
                <div className="flex justify-end">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="flex items-center space-x-2 px-4 py-2 bg-dexin-light text-white rounded-lg hover:bg-dexin-primary transition-colors"
                  >
                    <Eye className="w-4 h-4" />
                    <span>Xem chi tiết</span>
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default OrderHistory;
