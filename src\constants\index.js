/**
 * File chứa các hằng số được sử dụng trong toàn bộ ứng dụng
 */

// Gi<PERSON> trị tối thiểu và tối đa cho thanh trượt giá
export const PRICE_RANGE = {
  MIN: 0,
  MAX: 10000000, // 10 triệu
};

// <PERSON><PERSON><PERSON> tùy chọn sắp xếp sản phẩm
export const SORT_OPTIONS = {
  NEWEST: 'newest',
  PRICE_LOW_TO_HIGH: 'price_low_to_high',
  PRICE_HIGH_TO_LOW: 'price_high_to_low',
};

// Tên hiển thị cho các tùy chọn sắp xếp
export const SORT_LABELS = {
  [SORT_OPTIONS.NEWEST]: 'Mới nhất',
  [SORT_OPTIONS.PRICE_LOW_TO_HIGH]: 'Giá: Thấp đến cao',
  [SORT_OPTIONS.PRICE_HIGH_TO_LOW]: 'Gi<PERSON>: <PERSON> đến thấp',
};
