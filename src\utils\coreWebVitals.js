// Core Web Vitals optimization utilities

// Track and report Core Web Vitals
export const trackCoreWebVitals = () => {
  if (typeof window === 'undefined') return;

  // Import web-vitals dynamically with error handling
  import('web-vitals').then(({ onCLS, onFID, onFCP, onLCP, onTTFB }) => {
    // Track all Core Web Vitals with error handling
    try {
      onCLS((metric) => {
        reportToAnalytics('CLS', metric);
      });
    } catch (e) {
      // CLS tracking not available
    }

    try {
      onFID((metric) => {
        reportToAnalytics('FID', metric);
      });
    } catch (e) {
      // FID tracking not available
    }

    try {
      onFCP((metric) => {
        reportToAnalytics('FCP', metric);
      });
    } catch (e) {
      // FCP tracking not available
    }

    try {
      onLCP((metric) => {
        reportToAnalytics('LCP', metric);
      });
    } catch (e) {
      // LCP tracking not available
    }

    try {
      onTTFB((metric) => {
        reportToAnalytics('TTFB', metric);
      });
    } catch (e) {
      // TTFB tracking not available
    }
  }).catch(error => {
    // Web Vitals not available
  });
};

// Report metrics to analytics
const reportToAnalytics = (metricName, metric) => {
  // Send to Google Analytics
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metricName, {
      value: Math.round(metric.value),
      metric_id: metric.id,
      metric_value: metric.value,
      metric_delta: metric.delta,
      custom_parameter: 'core_web_vitals'
    });
  }
};

// Optimize Largest Contentful Paint (LCP)
export const optimizeLCP = () => {
  // Preload critical resources
  const criticalResources = [
    '/images/Logo DEXIN finall 2.png',
    '/images/hometile hero.jpg',
    '/images/main-bacground.png'
  ];

  criticalResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = resource;
    document.head.appendChild(link);
  });

  // Optimize font loading
  const fontLink = document.createElement('link');
  fontLink.rel = 'preload';
  fontLink.as = 'font';
  fontLink.type = 'font/woff2';
  fontLink.href = 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2';
  fontLink.crossOrigin = 'anonymous';
  document.head.appendChild(fontLink);
};

// Optimize First Input Delay (FID)
export const optimizeFID = () => {
  // Break up long tasks
  const breakUpLongTasks = (callback) => {
    if ('scheduler' in window && 'postTask' in window.scheduler) {
      window.scheduler.postTask(callback, { priority: 'user-blocking' });
    } else {
      setTimeout(callback, 0);
    }
  };

  // Use passive event listeners
  const addPassiveEventListener = (element, event, handler) => {
    element.addEventListener(event, handler, { passive: true });
  };

  return { breakUpLongTasks, addPassiveEventListener };
};

// Optimize Cumulative Layout Shift (CLS)
export const optimizeCLS = () => {
  // Set explicit dimensions for images
  const setImageDimensions = () => {
    const images = document.querySelectorAll('img:not([width]):not([height])');
    images.forEach(img => {
      // Set aspect ratio to prevent layout shift
      img.style.aspectRatio = '16/9'; // Default aspect ratio
      img.style.width = '100%';
      img.style.height = 'auto';
    });
  };

  // Reserve space for dynamic content
  const reserveSpace = (element, minHeight = '200px') => {
    if (element) {
      element.style.minHeight = minHeight;
    }
  };

  // Avoid inserting content above existing content
  const insertContentSafely = (container, content, position = 'append') => {
    if (!container) return;

    if (position === 'prepend') {
      // Use transform instead of changing DOM structure
      const wrapper = document.createElement('div');
      wrapper.innerHTML = content;
      wrapper.style.transform = 'translateY(-100%)';
      container.prepend(wrapper);

      // Animate in
      requestAnimationFrame(() => {
        wrapper.style.transition = 'transform 0.3s ease';
        wrapper.style.transform = 'translateY(0)';
      });
    } else {
      container.insertAdjacentHTML('beforeend', content);
    }
  };

  return { setImageDimensions, reserveSpace, insertContentSafely };
};

// Optimize First Contentful Paint (FCP)
export const optimizeFCP = () => {
  // Inline critical CSS
  const inlineCriticalCSS = (css) => {
    const style = document.createElement('style');
    style.textContent = css;
    document.head.appendChild(style);
  };

  // Remove render-blocking resources
  const loadNonCriticalCSS = (href) => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.media = 'print';
    link.onload = function() {
      this.media = 'all';
    };
    document.head.appendChild(link);
  };

  return { inlineCriticalCSS, loadNonCriticalCSS };
};

// Optimize Time to First Byte (TTFB)
export const optimizeTTFB = () => {
  // Use service worker for caching
  const registerServiceWorker = () => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          // SW registered successfully
        })
        .catch(error => {
          // SW registration failed
        });
    }
  };

  // Prefetch critical resources
  const prefetchResources = (urls) => {
    urls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = url;
      document.head.appendChild(link);
    });
  };

  return { registerServiceWorker, prefetchResources };
};

// Performance observer for monitoring
export const setupPerformanceObserver = () => {
  if ('PerformanceObserver' in window) {
    // Monitor Long Tasks
    const longTaskObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 50) {
          reportToAnalytics('long_task', {
            value: entry.duration,
            startTime: entry.startTime
          });
        }
      });
    });

    try {
      longTaskObserver.observe({ entryTypes: ['longtask'] });
    } catch (e) {
      // Long task observer not supported
    }

    // Monitor Layout Shifts
    const layoutShiftObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (!entry.hadRecentInput) {
          // Layout shift detected - send to analytics only
          reportToAnalytics('layout_shift', {
            value: entry.value,
            startTime: entry.startTime
          });
        }
      });
    });

    try {
      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (e) {
      // Layout shift observer not supported
    }
  }
};

// Initialize all Core Web Vitals optimizations
export const initializeCoreWebVitals = () => {
  // Track metrics
  trackCoreWebVitals();

  // Setup performance monitoring
  setupPerformanceObserver();

  // Apply optimizations
  optimizeLCP();
  optimizeFCP();
  optimizeTTFB();

  const { setImageDimensions } = optimizeCLS();
  const { addPassiveEventListener } = optimizeFID();

  // Apply optimizations when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setImageDimensions();
    });
  } else {
    setImageDimensions();
  }

  // Return utilities for use in components
  return {
    optimizeLCP,
    optimizeFID,
    optimizeCLS,
    optimizeFCP,
    optimizeTTFB,
    addPassiveEventListener
  };
};

// Performance budget checker
export const checkPerformanceBudget = () => {
  const budgets = {
    FCP: 1800, // 1.8s
    LCP: 2500, // 2.5s
    FID: 100,  // 100ms
    CLS: 0.1,  // 0.1
    TTFB: 800  // 800ms
  };

  return new Promise((resolve) => {
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      const results = {};

      Promise.all([
        new Promise(resolve => getFCP(resolve)),
        new Promise(resolve => getLCP(resolve)),
        new Promise(resolve => getFID(resolve)),
        new Promise(resolve => getCLS(resolve)),
        new Promise(resolve => getTTFB(resolve))
      ]).then(([fcp, lcp, fid, cls, ttfb]) => {
        results.FCP = { value: fcp.value, budget: budgets.FCP, pass: fcp.value <= budgets.FCP };
        results.LCP = { value: lcp.value, budget: budgets.LCP, pass: lcp.value <= budgets.LCP };
        results.FID = { value: fid.value, budget: budgets.FID, pass: fid.value <= budgets.FID };
        results.CLS = { value: cls.value, budget: budgets.CLS, pass: cls.value <= budgets.CLS };
        results.TTFB = { value: ttfb.value, budget: budgets.TTFB, pass: ttfb.value <= budgets.TTFB };

        resolve(results);
      });
    });
  });
};
