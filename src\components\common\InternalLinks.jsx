import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, ExternalLink } from 'lucide-react';
import { generateInternalLinks, pagesSEO } from '../../utils/seoUtils';

const InternalLinks = ({ 
  currentPage, 
  className = '',
  title = 'Bài viết liên quan',
  maxLinks = 3,
  showDescription = true 
}) => {
  // Tạo danh sách tất cả các trang
  const allPages = Object.entries(pagesSEO).map(([key, page]) => ({
    ...page,
    id: key
  }));

  // Tìm trang hiện tại
  const current = allPages.find(page => page.url === currentPage) || allPages[0];
  
  // Tạo gợi ý liên kết nội bộ
  const suggestions = generateInternalLinks(current, allPages).slice(0, maxLinks);

  if (suggestions.length === 0) {
    return null;
  }

  return (
    <div className={`bg-gray-50 rounded-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <ExternalLink className="w-5 h-5 mr-2 text-dexin-primary" />
        {title}
      </h3>
      
      <div className="space-y-4">
        {suggestions.map((suggestion, index) => (
          <Link
            key={index}
            to={suggestion.url}
            className="block group hover:bg-white rounded-lg p-4 transition-all duration-200 border border-transparent hover:border-gray-200 hover:shadow-sm"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 group-hover:text-dexin-primary transition-colors line-clamp-2">
                  {suggestion.suggestedAnchorText}
                </h4>
                
                {showDescription && (
                  <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                    {getPageDescription(suggestion.url)}
                  </p>
                )}
                
                <div className="flex items-center mt-2 text-xs text-gray-500">
                  <span>Độ liên quan: {suggestion.relevanceScore}/5</span>
                  <span className="mx-2">•</span>
                  <span>{getPageCategory(suggestion.url)}</span>
                </div>
              </div>
              
              <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-dexin-primary transition-colors ml-3 flex-shrink-0" />
            </div>
          </Link>
        ))}
      </div>
      
      {/* SEO Schema markup cho internal links */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ItemList",
            "name": title,
            "description": "Danh sách các trang liên quan trên DEXIN",
            "itemListElement": suggestions.map((suggestion, index) => ({
              "@type": "ListItem",
              "position": index + 1,
              "name": suggestion.suggestedAnchorText,
              "url": `https://dexin.vercel.app${suggestion.url}`
            }))
          })
        }}
      />
    </div>
  );
};

// Hàm lấy mô tả trang
const getPageDescription = (url) => {
  const descriptions = {
    '/': 'Khám phá DEXIN - nền tảng thiết kế nội thất thông minh với AI. Thiết kế 2D, cửa hàng nội thất, tư vấn chuyên nghiệp.',
    '/store': 'Cửa hàng nội thất DEXIN với hàng ngàn sản phẩm chất lượng cao. Ghế, giường, cây xanh và nhiều món đồ nội thất khác.',
    '/phac-y': 'Công cụ Phác Ý của DEXIN để thiết kế nội thất 2D miễn phí. Kéo thả đồ nội thất, tạo bản vẽ chuyên nghiệp.',
    '/chung-nhip': 'Cộng đồng Chung Nhịp của DEXIN để chia sẻ ý tưởng thiết kế, học hỏi kinh nghiệm và kết nối.',
    '/chuyen-nha': 'Dịch vụ Chuyển Nhà của DEXIN cung cấp tư vấn thiết kế, lên kế hoạch và hỗ trợ chuyển nhà hoàn hảo.',
    '/wishlist': 'Quản lý danh sách sản phẩm nội thất yêu thích của bạn trên DEXIN.',
    '/cart': 'Xem lại các sản phẩm nội thất trong giỏ hàng của bạn. Thanh toán nhanh chóng và an toàn.',
    '/login': 'Đăng nhập vào tài khoản DEXIN để trải nghiệm đầy đủ các tính năng.',
    '/signup': 'Tạo tài khoản DEXIN miễn phí để sử dụng công cụ thiết kế 2D và mua sắm nội thất.'
  };
  
  return descriptions[url] || 'Trang web DEXIN - Nền tảng thiết kế nội thất với AI';
};

// Hàm lấy danh mục trang
const getPageCategory = (url) => {
  const categories = {
    '/': 'Trang chủ',
    '/store': 'Cửa hàng',
    '/phac-y': 'Công cụ thiết kế',
    '/chung-nhip': 'Cộng đồng',
    '/chuyen-nha': 'Dịch vụ',
    '/wishlist': 'Yêu thích',
    '/cart': 'Giỏ hàng',
    '/login': 'Đăng nhập',
    '/signup': 'Đăng ký'
  };
  
  return categories[url] || 'Khác';
};

// Component cho breadcrumb với SEO
export const BreadcrumbSEO = ({ items, className = '' }) => {
  return (
    <nav className={`flex items-center space-x-2 text-sm text-gray-600 ${className}`}>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && <span className="text-gray-400">/</span>}
          {item.url ? (
            <Link 
              to={item.url} 
              className="hover:text-dexin-primary transition-colors"
            >
              {item.name}
            </Link>
          ) : (
            <span className="text-gray-900 font-medium">{item.name}</span>
          )}
        </React.Fragment>
      ))}
      
      {/* SEO Schema markup cho breadcrumb */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": items.map((item, index) => ({
              "@type": "ListItem",
              "position": index + 1,
              "name": item.name,
              "item": item.url ? `https://dexin.vercel.app${item.url}` : undefined
            }))
          })
        }}
      />
    </nav>
  );
};

// Component cho related products
export const RelatedProducts = ({ currentProduct, allProducts, maxItems = 4 }) => {
  if (!currentProduct || !allProducts) return null;

  // Lọc sản phẩm liên quan dựa trên category
  const relatedProducts = allProducts
    .filter(product => 
      product.id !== currentProduct.id && 
      product.category === currentProduct.category
    )
    .slice(0, maxItems);

  if (relatedProducts.length === 0) return null;

  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Sản phẩm liên quan
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {relatedProducts.map((product) => (
          <Link
            key={product.id}
            to={`/store/product/${product.id}`}
            className="group block"
          >
            <div className="aspect-square bg-white rounded-lg overflow-hidden mb-2">
              <img
                src={product.image}
                alt={`${product.name} - Sản phẩm nội thất tại DEXIN`}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
              />
            </div>
            <h4 className="text-sm font-medium text-gray-900 group-hover:text-dexin-primary transition-colors line-clamp-2">
              {product.name}
            </h4>
            <p className="text-sm text-dexin-primary font-semibold mt-1">
              {product.price?.toLocaleString('vi-VN')}đ
            </p>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default InternalLinks;
