import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Trash2, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';
import { exportAsPNG, exportAsPDF } from '../../utils/exportUtils';
import SEOHead from '../../components/common/SEOHead';
import { pagesSEO } from '../../utils/seoUtils';

// Import các component con
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Canvas from './components/Canvas';
import Toolbar from './components/Toolbar';

// Component chính
const PhacY = () => {
  // Ref cho Konva Stage
  const stageRef = useRef(null);

  // State cho các đồ vật trên canvas
  const [items, setItems] = useState([]);
  const [selectedId, setSelectedId] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');

  // State cho các tính năng mới
  const [isLocked, setIsLocked] = useState(false);
  const [viewMode, setViewMode] = useState('edit'); // 'edit', 'hand', 'layers'
  const [isSidebarVisible, setIsSidebarVisible] = useState(true);

  // State cho thông báo cảnh báo
  const [showWarning, setShowWarning] = useState(true);
  const [canDismiss, setCanDismiss] = useState(false);

  // State cho thông báo khi thêm item phòng
  const [showRoomAlert, setShowRoomAlert] = useState(false);
  const [roomAlertMessage, setRoomAlertMessage] = useState('');

  // State cho thông báo kết quả xuất file
  const [showExportResult, setShowExportResult] = useState(false);
  const [exportResult, setExportResult] = useState({ success: false, message: '', fileName: '' });

  // State cho lịch sử thao tác
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Danh sách các danh mục
  const categories = [
    { id: 'all', name: 'Tất cả' },
    { id: 'giuong', name: 'Giường' },
    { id: 'ghe', name: 'Ghế' },
    { id: 'cay-xanh', name: 'Cây xanh' },
    { id: 'phong', name: 'Phòng' },
  ];

  // Danh sách hình ảnh theo danh mục
  const [furnitureItems] = useState({
    'giuong': [
      { id: 'giuong-1', src: '/images/Giuong/********************************.png', name: 'Giường 1' },
      { id: 'giuong-2', src: '/images/Giuong/39d04fb2dc51f637019c3a136780ad42.png', name: 'Giường 2' },
      { id: 'giuong-3', src: '/images/Giuong/4528b0f99f98857e5fb9a52067d2aac7.png', name: 'Giường 3' },
      { id: 'giuong-4', src: '/images/Giuong/46eda73b98547c2da72f0d8e1c7e8a89.png', name: 'Giường 4' },
      { id: 'giuong-5', src: '/images/Giuong/54e8322cb186c60cad69a7babcd73096.png', name: 'Giường 5' },
      { id: 'giuong-6', src: '/images/Giuong/5f9b92d3698f795d9fda808f4d2645dd.png', name: 'Giường 6' },
    ],
    'ghe': [
      { id: 'ghe-1', src: '/images/Ghe/00fa875a4ff99e8164a539e47e14a328.png', name: 'Ghế 1' },
      { id: 'ghe-2', src: '/images/Ghe/241706-001_TOP_1_1200x1800 (1).png', name: 'Ghế 2' },
      { id: 'ghe-3', src: '/images/Ghe/29318b3828a40337745376764f256798.png', name: 'Ghế 3' },
      { id: 'ghe-4', src: '/images/Ghe/51c761a20e86ecd2a9882fafbb59544b.png', name: 'Ghế 4' },
      { id: 'ghe-5', src: '/images/Ghe/5de4f6d00a16caab5f2c85dff5e24551.png', name: 'Ghế 5' },
      { id: 'ghe-6', src: '/images/Ghe/63e7499a908c7940a1fa2731f62c27fe.png', name: 'Ghế 6' },
    ],
    'cay-xanh': [
      { id: 'cay-1', src: '/images/Cay xanh/23058bfe97e6471a067bb9741ad6f2ed.png', name: 'Cây 1' },
      { id: 'cay-2', src: '/images/Cay xanh/295efad9b7573cc59edc8edeb4f995bf.png', name: 'Cây 2' },
      { id: 'cay-3', src: '/images/Cay xanh/2fc6023e08b3d7f5ac35064e03d8faa8.png', name: 'Cây 3' },
      { id: 'cay-4', src: '/images/Cay xanh/34183147df28782e633ce420215a8984.png', name: 'Cây 4' },
      { id: 'cay-5', src: '/images/Cay xanh/48ea92b30ec683b0e82bead3d42f09f4.png', name: 'Cây 5' },
      { id: 'cay-6', src: '/images/Cay xanh/537b6f5768ce527c0418541ccee0c885.png', name: 'Cây 6' },
    ],
    'phong': [
      { id: 'phong-1', src: '/images/main-bacground.png', name: 'Phòng mặc định' },
    ],
  });

  // Lọc các mục theo tìm kiếm và danh mục
  const filteredItems = () => {
    let result = [];

    // Lọc theo danh mục
    if (activeCategory === 'all') {
      Object.values(furnitureItems).forEach(categoryItems => {
        result = [...result, ...categoryItems];
      });
    } else if (furnitureItems[activeCategory]) {
      result = furnitureItems[activeCategory];
    }

    // Lọc theo từ khóa tìm kiếm
    if (searchTerm) {
      result = result.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return result;
  };

  // Xử lý khi kéo thả đồ vật từ sidebar vào canvas
  const handleDragStart = (e, item) => {
    e.dataTransfer.setData('furniture', JSON.stringify(item));
  };

  // Hàm kiểm tra xem đã có item từ category "phòng" trong canvas chưa
  const hasRoomBackground = () => {
    return items.some(item => item.isRoomBackground);
  };

  // Handle dropping furniture onto canvas
  const handleDrop = (e) => {
    if (isLocked) return;

    e.preventDefault();

    try {
      // Get furniture item data
      const itemData = JSON.parse(e.dataTransfer.getData('furniture'));

      // Get canvas position
      const canvasElement = e.currentTarget.querySelector('canvas');
      const canvasRect = canvasElement.getBoundingClientRect();

      // Kiểm tra xem item có thuộc category 'phong' không
      const isRoomBackground = itemData.id.startsWith('phong-');

      // Kiểm tra nếu đã có item phòng trong canvas và người dùng đang cố thêm một item phòng khác
      if (isRoomBackground && hasRoomBackground()) {
        setRoomAlertMessage('Đã có một khung phòng trong canvas! Hãy xóa khung phòng cũ trước khi thêm mới.');
        setShowRoomAlert(true);

        // Tự động ẩn thông báo sau 3 giây
        setTimeout(() => {
          setShowRoomAlert(false);
        }, 3000);

        return;
      }

      // Calculate drop position - handle both desktop and mobile
      let dropX, dropY;

      if (e.stageX !== undefined && e.stageY !== undefined) {
        // Mobile drop - coordinates already converted to stage coordinates
        dropX = e.stageX;
        dropY = e.stageY;
      } else {
        // Desktop drop - convert client coordinates
        dropX = e.clientX - canvasRect.left;
        dropY = e.clientY - canvasRect.top;
      }

      const position = {
        x: isRoomBackground ? canvasRect.width / 2 : dropX,
        y: isRoomBackground ? canvasRect.height / 2 : dropY
      };

      // Create a temporary image to get natural dimensions
      const img = new Image();
      img.src = itemData.src;

      // Default dimensions if we can't get natural size
      let itemWidth = 150;
      let itemHeight = 150;

      // Xử lý kích thước của item
      // Maintain aspect ratio but limit maximum size
      const aspectRatio = img.naturalWidth / img.naturalHeight || 1; // Đảm bảo luôn có tỷ lệ khung hình

      if (isRoomBackground) {
        // Nếu là phòng, phóng to để vừa với canvas
        const canvasWidth = canvasRect.width;
        const canvasHeight = canvasRect.height;

        // Tính toán kích thước để vừa với canvas và giữ tỷ lệ
        // Sử dụng 80% kích thước canvas để đảm bảo có khoảng trống xung quanh
        const maxWidth = canvasWidth * 0.8;
        const maxHeight = canvasHeight * 0.8;

        if (aspectRatio >= 1) {
          // Nếu ảnh rộng hơn cao
          if (maxWidth / aspectRatio <= maxHeight) {
            // Nếu chiều rộng là giới hạn
            itemWidth = maxWidth;
            itemHeight = itemWidth / aspectRatio;
          } else {
            // Nếu chiều cao là giới hạn
            itemHeight = maxHeight;
            itemWidth = itemHeight * aspectRatio;
          }
        } else {
          // Nếu ảnh cao hơn rộng
          if (maxHeight * aspectRatio <= maxWidth) {
            // Nếu chiều cao là giới hạn
            itemHeight = maxHeight;
            itemWidth = itemHeight * aspectRatio;
          } else {
            // Nếu chiều rộng là giới hạn
            itemWidth = maxWidth;
            itemHeight = itemWidth / aspectRatio;
          }
        }

        // Đặt vị trí ở giữa canvas
        position.x = (canvasWidth - itemWidth) / 2;
        position.y = (canvasHeight - itemHeight) / 2;
      } else {
        // Đối với các item thông thường, giữ nguyên logic cũ
        const maxDimension = 200;

        if (aspectRatio >= 1) {
          // Wider than tall
          itemWidth = Math.min(maxDimension, img.naturalWidth || maxDimension);
          itemHeight = itemWidth / aspectRatio;
        } else {
          // Taller than wide
          itemHeight = Math.min(maxDimension, img.naturalHeight || maxDimension);
          itemWidth = itemHeight * aspectRatio;
        }
      }

      // Add item to canvas with proper dimensions
      const newItem = {
        ...itemData,
        x: position.x,
        y: position.y,
        width: itemWidth,
        height: itemHeight,
        id: `${itemData.id}-${Date.now()}`,
        rotation: 0,
        isRoomBackground: isRoomBackground // Thêm thuộc tính để đánh dấu đây là background phòng
      };

      setItems(prevItems => {
        const newItems = [...prevItems, newItem];

        // Add to history
        addToHistory(newItems);

        return newItems;
      });

      // Select the newly added item
      setSelectedId(newItem.id);
    } catch (error) {
      console.error("Error handling drop:", error);
    }
  };

  // Handle item property updates (position, size, rotation) or full item list updates
  const handleItemChange = (updatedItemOrItems) => {
    if (isLocked) return;

    setItems(prevItems => {
      let newItems;

      // Kiểm tra xem đầu vào là một item hay một mảng items
      if (Array.isArray(updatedItemOrItems)) {
        // Nếu là mảng, sử dụng trực tiếp
        newItems = updatedItemOrItems;
      } else {
        // Nếu là một item đơn lẻ
        const updatedItem = updatedItemOrItems;

        // Ensure we have all required properties
        if (!updatedItem.id || !updatedItem.src) {
          console.error("Missing required properties in updated item:", updatedItem);
          return prevItems; // Return previous state unchanged
        }

        // Create a new items array with the updated item
        newItems = prevItems.map(item => {
          if (item.id === updatedItem.id) {
            // Ensure we preserve all necessary properties
            return {
              ...item,           // Start with all original properties
              ...updatedItem,    // Apply all updates
              id: item.id,       // Ensure ID is preserved
              src: item.src      // Ensure source image is preserved
            };
          }
          return item;
        });
      }

      // Add to history
      addToHistory(newItems);

      return newItems;
    });
  };

  // Thêm vào lịch sử
  const addToHistory = useCallback((newItems) => {
    setHistoryIndex(prevIndex => {
      setHistory(prevHistory => {
        // Cắt bỏ lịch sử phía trước nếu đang ở giữa
        const newHistory = prevHistory.slice(0, prevIndex + 1);
        newHistory.push(newItems);
        return newHistory;
      });
      return prevIndex + 1;
    });
  }, []);

  // Hoàn tác
  const handleUndo = useCallback(() => {
    setHistoryIndex(prevIndex => {
      if (prevIndex > 0) {
        const newIndex = prevIndex - 1;
        setHistory(prevHistory => {
          setItems(prevHistory[newIndex]);
          return prevHistory;
        });
        return newIndex;
      }
      return prevIndex;
    });
  }, []);

  // Làm lại
  const handleRedo = useCallback(() => {
    setHistoryIndex(prevIndex => {
      setHistory(prevHistory => {
        if (prevIndex < prevHistory.length - 1) {
          const newIndex = prevIndex + 1;
          setItems(prevHistory[newIndex]);
          return prevHistory;
        }
        return prevHistory;
      });
      return prevIndex < history.length - 1 ? prevIndex + 1 : prevIndex;
    });
  }, [history.length]);

  // Hàm xử lý xuất file
  const handleExport = useCallback(async (type, customFileName) => {
    if (!stageRef.current) {
      console.error('Stage reference not available');
      setExportResult({
        success: false,
        message: 'Không thể xuất file: Không tìm thấy canvas',
        fileName: null
      });
      setShowExportResult(true);

      // Tự động ẩn thông báo sau 3 giây
      setTimeout(() => {
        setShowExportResult(false);
      }, 3000);

      return;
    }

    // Sử dụng tên file tùy chỉnh nếu có, nếu không thì dùng tên mặc định
    const fileName = customFileName || 'phac-y-design';

    try {
      let result;

      if (type === 'png') {
        result = await exportAsPNG(stageRef.current, fileName);
      } else if (type === 'pdf') {
        result = await exportAsPDF(stageRef.current, fileName);
      }

      // Hiển thị thông báo kết quả
      setExportResult(result);
      setShowExportResult(true);

      // Tự động ẩn thông báo sau 3 giây
      setTimeout(() => {
        setShowExportResult(false);
      }, 3000);
    } catch (error) {
      console.error('Error in handleExport:', error);
      setExportResult({
        success: false,
        message: `Lỗi khi xuất file: ${error.message || 'Lỗi không xác định'}`,
        fileName: null
      });
      setShowExportResult(true);

      // Tự động ẩn thông báo sau 3 giây
      setTimeout(() => {
        setShowExportResult(false);
      }, 3000);
    }
  }, []);

  // Khởi tạo lịch sử - chỉ chạy một lần khi component mount
  useEffect(() => {
    if (history.length === 0) {
      setHistory([[]]);
      setHistoryIndex(0);
    }
  }, []); // Chỉ chạy một lần khi mount

  // Hàm xử lý xóa đối tượng đang được chọn
  const handleDeleteSelectedItem = useCallback(() => {
    // Cho phép xóa trong cả chế độ edit và layers, miễn là không bị khóa
    if (selectedId && !isLocked && (viewMode === 'edit' || viewMode === 'layers')) {
      setItems(prevItems => {
        const newItems = prevItems.filter(item => item.id !== selectedId);

        // Add to history
        addToHistory(newItems);

        return newItems;
      });
      setSelectedId(null);

      // Hiển thị thông báo nhỏ (có thể thêm toast notification ở đây)
      console.log('Đã xóa đối tượng');
    }
  }, [selectedId, isLocked, viewMode, addToHistory]);

  // Xử lý sự kiện phím Delete để xóa đối tượng đang được chọn
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Kiểm tra nếu phím Delete được nhấn và không bị khóa
      // Cho phép xóa trong cả chế độ edit và layers
      if (e.key === 'Delete' && !isLocked && (viewMode === 'edit' || viewMode === 'layers')) {
        handleDeleteSelectedItem();
      }
    };

    // Thêm event listener
    window.addEventListener('keydown', handleKeyDown);

    // Cleanup function
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleDeleteSelectedItem, isLocked, viewMode]);

  // Không cần đếm ngược bằng useEffect nữa vì đã sử dụng animation của Motion

  // Xử lý đóng thông báo cảnh báo
  const handleCloseWarning = () => {
    if (canDismiss) {
      setShowWarning(false);
    }
  };

  return (
    <>
      <SEOHead
        title={pagesSEO.phacy.title}
        description={pagesSEO.phacy.description}
        keywords={pagesSEO.phacy.keywords}
        url={pagesSEO.phacy.url}
      />
      <div className="h-screen w-full relative overflow-hidden">
        {/* Thông báo alert khi thêm item phòng */}
      <AnimatePresence>
        {showRoomAlert && (
          <motion.div
            className="fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 sm:px-5 py-2 sm:py-3 rounded-lg shadow-xl z-[9999] opacity-95 transition-opacity duration-300 flex items-center max-w-[90vw] sm:max-w-md"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <AlertCircle className="mr-2 sm:mr-3 flex-shrink-0" size={20} />
            <span className="font-medium text-sm sm:text-base">{roomAlertMessage}</span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Thông báo kết quả xuất file */}
      <AnimatePresence>
        {showExportResult && (
          <motion.div
            className={`fixed top-20 left-1/2 transform -translate-x-1/2 ${
              exportResult.success ? 'bg-green-600' : 'bg-red-600'
            } text-white px-3 sm:px-5 py-2 sm:py-3 rounded-lg shadow-xl z-[9999] opacity-95 transition-opacity duration-300 flex items-center max-w-[90vw] sm:max-w-md`}
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {exportResult.success ? (
              <CheckCircle className="mr-2 sm:mr-3 flex-shrink-0" size={20} />
            ) : (
              <XCircle className="mr-2 sm:mr-3 flex-shrink-0" size={20} />
            )}
            <span className="font-medium text-sm sm:text-base">{exportResult.message}</span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Thông báo cảnh báo */}
      <AnimatePresence>
        {showWarning && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              className="bg-white rounded-lg shadow-xl p-4 sm:p-6 md:p-8 max-w-[90vw] sm:max-w-md w-full mx-4 relative"
              initial={{ scale: 0.9, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 20 }}
              transition={{
                type: "spring",
                damping: 20,
                stiffness: 300,
                delay: 0.1
              }}
            >
              <div className="text-center">
                <motion.div
                  className="flex justify-center mb-3 sm:mb-4"
                  initial={{ scale: 0.5 }}
                  animate={{ scale: 1 }}
                  transition={{
                    type: "spring",
                    damping: 10,
                    stiffness: 200,
                    delay: 0.2
                  }}
                >
                  <motion.svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 sm:h-16 sm:w-16 text-yellow-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [1, 0.8, 1]
                    }}
                    transition={{
                      repeat: Infinity,
                      duration: 2,
                      ease: "easeInOut"
                    }}
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </motion.svg>
                </motion.div>
                <motion.h2
                  className="text-xl sm:text-2xl font-bold text-gray-800 mb-2 sm:mb-4"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  Cảnh báo
                </motion.h2>
                <motion.p
                  className="text-gray-600 mb-4 sm:mb-6 text-base sm:text-lg"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  Nếu reload trang hoặc nhấn F5 sẽ mất đi toàn bộ bố cục đang chỉnh sửa.
                </motion.p>
                <div className="mb-3 sm:mb-4 h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-dexin-primary"
                    initial={{ width: "100%" }}
                    animate={{ width: "0%" }}
                    transition={{
                      duration: 3,
                      ease: "linear"
                    }}
                    onAnimationComplete={() => setCanDismiss(true)}
                  />
                </div>
                <motion.button
                  className={`w-full py-2 sm:py-3 px-3 sm:px-4 rounded-lg font-medium text-white ${
                    canDismiss
                      ? 'bg-dexin-primary hover:bg-dexin-primary-dark'
                      : 'bg-gray-400 cursor-not-allowed'
                  }`}
                  onClick={handleCloseWarning}
                  disabled={!canDismiss}
                  whileHover={canDismiss ? { scale: 1.03 } : {}}
                  whileTap={canDismiss ? { scale: 0.97 } : {}}
                  initial={{ opacity: 0.8 }}
                  animate={{
                    opacity: 1,
                    transition: { delay: 0.5 }
                  }}
                >
                  {canDismiss ? 'OK' : 'Vui lòng đợi...'}
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Canvas - takes full height and width */}
      <div className="absolute inset-0">
        <Canvas
          items={items}
          selectedId={selectedId}
          setSelectedId={setSelectedId}
          handleItemChange={handleItemChange}
          handleDrop={handleDrop}
          isLocked={isLocked}
          viewMode={viewMode}
          stageRef={stageRef}
        />
      </div>

      {/* Header - floating on top */}
      <Header
        title="Untitled_1"
        toggleSidebar={() => setIsSidebarVisible(!isSidebarVisible)}
        handleExport={handleExport}
      />

      {/* Sidebar - positioned absolutely with responsive adjustments */}
      <Sidebar
        isVisible={isSidebarVisible}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        categories={categories}
        activeCategory={activeCategory}
        setActiveCategory={setActiveCategory}
        filteredItems={filteredItems}
        handleDragStart={handleDragStart}
      />

      {/* Toolbar - floating at bottom */}
      <Toolbar
        isLocked={isLocked}
        setIsLocked={setIsLocked}
        viewMode={viewMode}
        setViewMode={setViewMode}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < history.length - 1}
        handleUndo={handleUndo}
        handleRedo={handleRedo}
      />

      {/* Nút xóa đối tượng - hiển thị trong cả chế độ edit và layers */}
      {selectedId && !isLocked && (viewMode === 'edit' || viewMode === 'layers') && (
        <button
          className="fixed bottom-[120px] sm:bottom-24 right-4 sm:right-6 p-2 sm:p-3 bg-white rounded-full shadow-xl text-red-500 hover:bg-red-50 transition-all duration-200 transform hover:scale-105 active:scale-95 z-50"
          onClick={handleDeleteSelectedItem}
          title="Xóa đối tượng (hoặc nhấn phím Delete)"
        >
          <Trash2 size={20} className="sm:hidden" />
          <Trash2 size={22} className="hidden sm:block" />
        </button>
      )}


      </div>
    </>
  );
};

export default PhacY;
