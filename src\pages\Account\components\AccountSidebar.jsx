import React from 'react';
import { motion } from 'motion/react';
import { useNavigate } from 'react-router-dom';
import {
  User,
  MapPin,
  ShoppingBag,
  Palette,
  LogOut,
  X,
  Menu
} from 'lucide-react';
import { useAuth } from '../../../context/AuthContext';
import { toast } from 'react-toastify';
import { Avatar, AvatarImage, AvatarFallback } from '../../../components/ui/avatar';

const AccountSidebar = ({
  activeSection,
  setActiveSection,
  isSidebarOpen,
  setIsSidebarOpen
}) => {
  const { user, logout, getDisplayName } = useAuth();
  const navigate = useNavigate();

  // Tạo initials từ tên user để làm fallback
  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const menuItems = [
    {
      id: 'profile',
      label: 'Thông tin cá nhân',
      icon: User,
      description: 'Quản lý thông tin tài khoản'
    },
    {
      id: 'address',
      label: 'Địa chỉ',
      icon: MapPin,
      description: 'Quản lý địa chỉ giao hàng'
    },
    {
      id: 'orders',
      label: 'Đơn hàng',
      icon: ShoppingBag,
      description: 'Lịch sử mua hàng'
    },
    {
      id: 'designs',
      label: 'Bản thiết kế',
      icon: Palette,
      description: 'Các thiết kế đã lưu'
    }
  ];

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Đăng xuất thành công!');
      // Redirect về trang chủ sau khi đăng xuất thành công
      navigate('/');
    } catch (error) {
      toast.error('Có lỗi xảy ra khi đăng xuất');
    }
  };

  const handleMenuClick = (sectionId) => {
    setActiveSection(sectionId);
    // Đóng sidebar trên mobile sau khi chọn
    if (window.innerWidth < 768) {
      setIsSidebarOpen(false);
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <motion.div
        initial={{ x: -300 }}
        animate={{ x: isSidebarOpen ? 0 : -300 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className={`
          fixed md:relative top-0 left-0 h-full md:h-auto
          w-80 md:w-full bg-white shadow-lg md:shadow-none
          z-50 md:z-auto overflow-y-auto
          ${isSidebarOpen ? 'block' : 'hidden md:block'}
        `}
      >
        <div className="p-6">
          {/* Mobile header */}
          <div className="flex items-center justify-between mb-6 md:hidden">
            <h2 className="text-lg font-semibold text-gray-900">Menu tài khoản</h2>
            <button
              onClick={() => setIsSidebarOpen(false)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* User info */}
          <div className="bg-gradient-to-r from-dexin-primary to-dexin-light rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
              <Avatar className="w-12 h-12 border-2 border-white">
                <AvatarImage
                  src={user?.avatar && user.avatar !== 'default_avatar.png' ? user.avatar : undefined}
                  alt={getDisplayName() || 'User Avatar'}
                />
                <AvatarFallback className="bg-white text-dexin-primary text-sm font-medium">
                  {getInitials(getDisplayName())}
                </AvatarFallback>
              </Avatar>
              <div className="text-white">
                <h3 className="font-semibold text-sm">
                  {getDisplayName() || `${user?.firstName} ${user?.lastName}`}
                </h3>
                <p className="text-xs text-dexin-bg">@{user?.userName}</p>
              </div>
            </div>
          </div>

          {/* Menu items */}
          <nav className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeSection === item.id;
              
              return (
                <motion.button
                  key={item.id}
                  onClick={() => handleMenuClick(item.id)}
                  className={`
                    w-full text-left p-3 rounded-lg transition-all duration-200
                    flex items-center space-x-3 group
                    ${isActive 
                      ? 'bg-dexin-light-20 text-dexin-primary border-l-4 border-dexin-primary' 
                      : 'hover:bg-gray-50 text-gray-700 hover:text-dexin-primary'
                    }
                  `}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Icon className={`w-5 h-5 ${isActive ? 'text-dexin-primary' : 'text-gray-400 group-hover:text-dexin-primary'}`} />
                  <div className="flex-1">
                    <div className={`font-medium ${isActive ? 'text-dexin-primary' : 'text-gray-900'}`}>
                      {item.label}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {item.description}
                    </div>
                  </div>
                </motion.button>
              );
            })}
          </nav>

          {/* Logout button */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <motion.button
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 p-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors group"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <LogOut className="w-5 h-5" />
              <span className="font-medium">Đăng xuất</span>
            </motion.button>
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default AccountSidebar;
