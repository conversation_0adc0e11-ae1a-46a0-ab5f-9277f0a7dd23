import{_ as t}from"./index-IS1Q89nd.js";import{e,g as r}from"./index-DdBL2cja.js";import"./file-text-DkNzifT1.js";import"./pen-C65N3AwY.js";import"./chevron-up-A3k7AcMu.js";import"./trash-2-1tdRbsHn.js";function i(t,e,r,i,s,a,n){try{var o=t[a](n),h=o.value}catch(l){return void r(l)}o.done?e(h):Promise.resolve(h).then(i,s)}function s(t){return function(){var e=this,r=arguments;return new Promise((function(s,a){var n=t.apply(e,r);function o(t){i(n,s,a,o,h,"next",t)}function h(t){i(n,s,a,o,h,"throw",t)}o(void 0)}))}}function a(e,r,i){return(r=function(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,r);if("object"!=t(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==t(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[r]=i,e}var n,o,h={exports:{}},l={exports:{}},u=l.exports;function c(){return n||(n=1,function(){var t,e,r,i,s,a;"undefined"!=typeof performance&&null!==performance&&performance.now?l.exports=function(){return performance.now()}:"undefined"!=typeof process&&null!==process&&process.hrtime?(l.exports=function(){return(t()-s)/1e6},e=process.hrtime,i=(t=function(){var t;return 1e9*(t=e())[0]+t[1]})(),a=1e9*process.uptime(),s=i-a):Date.now?(l.exports=function(){return Date.now()-r},r=Date.now()):(l.exports=function(){return(new Date).getTime()-r},r=(new Date).getTime())}.call(u)),l.exports}function g(){if(o)return h.exports;o=1;for(var t=c(),r="undefined"==typeof window?e:window,i=["moz","webkit"],s="AnimationFrame",a=r["request"+s],n=r["cancel"+s]||r["cancelRequest"+s],l=0;!a&&l<i.length;l++)a=r[i[l]+"Request"+s],n=r[i[l]+"Cancel"+s]||r[i[l]+"CancelRequest"+s];if(!a||!n){var u=0,g=0,d=[],p=1e3/60;a=function(e){if(0===d.length){var r=t(),i=Math.max(0,p-(r-u));u=i+r,setTimeout((function(){var t=d.slice(0);d.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(u)}catch(r){setTimeout((function(){throw r}),0)}}),Math.round(i))}return d.push({handle:++g,callback:e,cancelled:!1}),g},n=function(t){for(var e=0;e<d.length;e++)d[e].handle===t&&(d[e].cancelled=!0)}}return h.exports=function(t){return a.call(r,t)},h.exports.cancel=function(){n.apply(r,arguments)},h.exports.polyfill=function(t){t||(t=r),t.requestAnimationFrame=a,t.cancelAnimationFrame=n},h.exports}const d=r(g());var p,f;function y(){return f?p:(f=1,p=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<r.length;i++){var s=r[i].re,a=r[i].process,n=s.exec(t);if(n){var o=a(n);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,i=0;i<r.length;i++)for(var s=r[i].example,a=0;a<s.length;a++)t[t.length]=s[a];for(var n in e)t[t.length]=n;var o=document.createElement("ul");for(o.setAttribute("id","rgbcolor-examples"),i=0;i<t.length;i++)try{var h=document.createElement("li"),l=new RGBColor(t[i]),u=document.createElement("div");u.style.cssText="margin: 3px; border: 1px solid black; background:"+l.toHex()+"; color:"+l.toHex(),u.appendChild(document.createTextNode("test"));var c=document.createTextNode(" "+t[i]+" -> "+l.toRGB()+" -> "+l.toHex());h.appendChild(u),h.appendChild(c),o.appendChild(h)}catch(g){}return o}})}const m=r(y());
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var v=function(t,e){return(v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function x(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}v(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function b(t,e){var r=t[0],i=t[1];return[r*Math.cos(e)-i*Math.sin(e),r*Math.sin(e)+i*Math.cos(e)]}function S(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var w=Math.PI;function T(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var i=t.rX,s=t.rY,a=t.x,n=t.y;i=Math.abs(t.rX),s=Math.abs(t.rY);var o=b([(e-a)/2,(r-n)/2],-t.xRot/180*w),h=o[0],l=o[1],u=Math.pow(h,2)/Math.pow(i,2)+Math.pow(l,2)/Math.pow(s,2);1<u&&(i*=Math.sqrt(u),s*=Math.sqrt(u)),t.rX=i,t.rY=s;var c=Math.pow(i,2)*Math.pow(l,2)+Math.pow(s,2)*Math.pow(h,2),g=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(i,2)*Math.pow(s,2)-c)/c)),d=i*l/s*g,p=-s*h/i*g,f=b([d,p],t.xRot/180*w);t.cX=f[0]+(e+a)/2,t.cY=f[1]+(r+n)/2,t.phi1=Math.atan2((l-p)/s,(h-d)/i),t.phi2=Math.atan2((-l-p)/s,(-h-d)/i),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*w),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*w),t.phi1*=180/w,t.phi2*=180/w}function A(t,e,r){S(t,e,r);var i=t*t+e*e-r*r;if(0>i)return[];if(0===i)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var s=Math.sqrt(i);return[[(t*r+e*s)/(t*t+e*e),(e*r-t*s)/(t*t+e*e)],[(t*r-e*s)/(t*t+e*e),(e*r+t*s)/(t*t+e*e)]]}var C,O=Math.PI/180;function P(t,e,r){return(1-r)*t+r*e}function N(t,e,r,i){return t+Math.cos(i/180*w)*e+Math.sin(i/180*w)*r}function M(t,e,r,i){var s=1e-6,a=e-t,n=r-e,o=3*a+3*(i-r)-6*n,h=6*(n-a),l=3*a;return Math.abs(o)<s?[-l/h]:function(t,e,r){var i=t*t/4-e;if(i<-1e-6)return[];if(i<=r)return[-t/2];var s=Math.sqrt(i);return[-t/2-s,-t/2+s]}(h/o,l/o,s)}function E(t,e,r,i,s){var a=1-s;return t*(a*a*a)+e*(3*a*a*s)+r*(3*a*s*s)+i*(s*s*s)}!function(t){function e(){return s((function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t}))}function r(){var t=NaN,e=NaN,r=NaN,i=NaN;return s((function(s,a,n){return s.type&L.SMOOTH_CURVE_TO&&(s.type=L.CURVE_TO,t=isNaN(t)?a:t,e=isNaN(e)?n:e,s.x1=s.relative?a-t:2*a-t,s.y1=s.relative?n-e:2*n-e),s.type&L.CURVE_TO?(t=s.relative?a+s.x2:s.x2,e=s.relative?n+s.y2:s.y2):(t=NaN,e=NaN),s.type&L.SMOOTH_QUAD_TO&&(s.type=L.QUAD_TO,r=isNaN(r)?a:r,i=isNaN(i)?n:i,s.x1=s.relative?a-r:2*a-r,s.y1=s.relative?n-i:2*n-i),s.type&L.QUAD_TO?(r=s.relative?a+s.x1:s.x1,i=s.relative?n+s.y1:s.y1):(r=NaN,i=NaN),s}))}function i(){var t=NaN,e=NaN;return s((function(r,i,s){if(r.type&L.SMOOTH_QUAD_TO&&(r.type=L.QUAD_TO,t=isNaN(t)?i:t,e=isNaN(e)?s:e,r.x1=r.relative?i-t:2*i-t,r.y1=r.relative?s-e:2*s-e),r.type&L.QUAD_TO){t=r.relative?i+r.x1:r.x1,e=r.relative?s+r.y1:r.y1;var a=r.x1,n=r.y1;r.type=L.CURVE_TO,r.x1=((r.relative?0:i)+2*a)/3,r.y1=((r.relative?0:s)+2*n)/3,r.x2=(r.x+2*a)/3,r.y2=(r.y+2*n)/3}else t=NaN,e=NaN;return r}))}function s(t){var e=0,r=0,i=NaN,s=NaN;return function(a){if(isNaN(i)&&!(a.type&L.MOVE_TO))throw new Error("path must start with moveto");var n=t(a,e,r,i,s);return a.type&L.CLOSE_PATH&&(e=i,r=s),void 0!==a.x&&(e=a.relative?e+a.x:a.x),void 0!==a.y&&(r=a.relative?r+a.y:a.y),a.type&L.MOVE_TO&&(i=e,s=r),n}}function a(t,e,r,i,a,n){return S(t,e,r,i,a,n),s((function(s,o,h,l){var u=s.x1,c=s.x2,g=s.relative&&!isNaN(l),d=void 0!==s.x?s.x:g?0:o,p=void 0!==s.y?s.y:g?0:h;function f(t){return t*t}s.type&L.HORIZ_LINE_TO&&0!==e&&(s.type=L.LINE_TO,s.y=s.relative?0:h),s.type&L.VERT_LINE_TO&&0!==r&&(s.type=L.LINE_TO,s.x=s.relative?0:o),void 0!==s.x&&(s.x=s.x*t+p*r+(g?0:a)),void 0!==s.y&&(s.y=d*e+s.y*i+(g?0:n)),void 0!==s.x1&&(s.x1=s.x1*t+s.y1*r+(g?0:a)),void 0!==s.y1&&(s.y1=u*e+s.y1*i+(g?0:n)),void 0!==s.x2&&(s.x2=s.x2*t+s.y2*r+(g?0:a)),void 0!==s.y2&&(s.y2=c*e+s.y2*i+(g?0:n));var y=t*i-e*r;if(void 0!==s.xRot&&(1!==t||0!==e||0!==r||1!==i))if(0===y)delete s.rX,delete s.rY,delete s.xRot,delete s.lArcFlag,delete s.sweepFlag,s.type=L.LINE_TO;else{var m=s.xRot*Math.PI/180,v=Math.sin(m),x=Math.cos(m),b=1/f(s.rX),S=1/f(s.rY),w=f(x)*b+f(v)*S,T=2*v*x*(b-S),A=f(v)*b+f(x)*S,C=w*i*i-T*e*i+A*e*e,O=T*(t*i+e*r)-2*(w*r*i+A*t*e),P=w*r*r-T*t*r+A*t*t,N=(Math.atan2(O,C-P)+Math.PI)%Math.PI/2,M=Math.sin(N),E=Math.cos(N);s.rX=Math.abs(y)/Math.sqrt(C*f(E)+O*M*E+P*f(M)),s.rY=Math.abs(y)/Math.sqrt(C*f(M)-O*M*E+P*f(E)),s.xRot=180*N/Math.PI}return void 0!==s.sweepFlag&&0>y&&(s.sweepFlag=+!s.sweepFlag),s}))}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),S(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return s((function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t}))},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),s((function(i,s,a,n,o){if(isNaN(n)&&!(i.type&L.MOVE_TO))throw new Error("path must start with moveto");return e&&i.type&L.HORIZ_LINE_TO&&(i.type=L.LINE_TO,i.y=i.relative?0:a),r&&i.type&L.VERT_LINE_TO&&(i.type=L.LINE_TO,i.x=i.relative?0:s),t&&i.type&L.CLOSE_PATH&&(i.type=L.LINE_TO,i.x=i.relative?n-s:n,i.y=i.relative?o-a:o),i.type&L.ARC&&(0===i.rX||0===i.rY)&&(i.type=L.LINE_TO,delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag),i}))},t.NORMALIZE_ST=r,t.QT_TO_C=i,t.INFO=s,t.SANITIZE=function(t){void 0===t&&(t=0),S(t);var e=NaN,r=NaN,i=NaN,a=NaN;return s((function(s,n,o,h,l){var u=Math.abs,c=!1,g=0,d=0;if(s.type&L.SMOOTH_CURVE_TO&&(g=isNaN(e)?0:n-e,d=isNaN(r)?0:o-r),s.type&(L.CURVE_TO|L.SMOOTH_CURVE_TO)?(e=s.relative?n+s.x2:s.x2,r=s.relative?o+s.y2:s.y2):(e=NaN,r=NaN),s.type&L.SMOOTH_QUAD_TO?(i=isNaN(i)?n:2*n-i,a=isNaN(a)?o:2*o-a):s.type&L.QUAD_TO?(i=s.relative?n+s.x1:s.x1,a=s.relative?o+s.y1:s.y2):(i=NaN,a=NaN),s.type&L.LINE_COMMANDS||s.type&L.ARC&&(0===s.rX||0===s.rY||!s.lArcFlag)||s.type&L.CURVE_TO||s.type&L.SMOOTH_CURVE_TO||s.type&L.QUAD_TO||s.type&L.SMOOTH_QUAD_TO){var p=void 0===s.x?0:s.relative?s.x:s.x-n,f=void 0===s.y?0:s.relative?s.y:s.y-o;g=isNaN(i)?void 0===s.x1?g:s.relative?s.x:s.x1-n:i-n,d=isNaN(a)?void 0===s.y1?d:s.relative?s.y:s.y1-o:a-o;var y=void 0===s.x2?0:s.relative?s.x:s.x2-n,m=void 0===s.y2?0:s.relative?s.y:s.y2-o;u(p)<=t&&u(f)<=t&&u(g)<=t&&u(d)<=t&&u(y)<=t&&u(m)<=t&&(c=!0)}return s.type&L.CLOSE_PATH&&u(n-h)<=t&&u(o-l)<=t&&(c=!0),c?[]:s}))},t.MATRIX=a,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),S(t,e,r);var i=Math.sin(t),s=Math.cos(t);return a(s,i,-i,s,e-e*s+r*i,r-e*i-r*s)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),S(t,e),a(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),S(t,e),a(t,0,0,e,0,0)},t.SKEW_X=function(t){return S(t),a(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return S(t),a(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),S(t),a(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),S(t),a(1,0,0,-1,0,t)},t.A_TO_C=function(){return s((function(t,e,r){return L.ARC===t.type?function(t,e,r){var i,s,a,n;t.cX||T(t,e,r);for(var o=Math.min(t.phi1,t.phi2),h=Math.max(t.phi1,t.phi2)-o,l=Math.ceil(h/90),u=new Array(l),c=e,g=r,d=0;d<l;d++){var p=P(t.phi1,t.phi2,d/l),f=P(t.phi1,t.phi2,(d+1)/l),y=f-p,m=4/3*Math.tan(y*O/4),v=[Math.cos(p*O)-m*Math.sin(p*O),Math.sin(p*O)+m*Math.cos(p*O)],x=v[0],S=v[1],w=[Math.cos(f*O),Math.sin(f*O)],A=w[0],C=w[1],N=[A+m*Math.sin(f*O),C-m*Math.cos(f*O)],M=N[0],E=N[1];u[d]={relative:t.relative,type:L.CURVE_TO};var V=function(e,r){var i=b([e*t.rX,r*t.rY],t.xRot),s=i[0],a=i[1];return[t.cX+s,t.cY+a]};i=V(x,S),u[d].x1=i[0],u[d].y1=i[1],s=V(M,E),u[d].x2=s[0],u[d].y2=s[1],a=V(A,C),u[d].x=a[0],u[d].y=a[1],t.relative&&(u[d].x1-=c,u[d].y1-=g,u[d].x2-=c,u[d].y2-=g,u[d].x-=c,u[d].y-=g),c=(n=[u[d].x,u[d].y])[0],g=n[1]}return u}(t,t.relative?0:e,t.relative?0:r):t}))},t.ANNOTATE_ARCS=function(){return s((function(t,e,r){return t.relative&&(e=0,r=0),L.ARC===t.type&&T(t,e,r),t}))},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=e(),a=i(),n=r(),o=s((function(e,r,i){var s=n(a(t(function(t){var e={};for(var r in t)e[r]=t[r];return e}(e))));function h(t){t>o.maxX&&(o.maxX=t),t<o.minX&&(o.minX=t)}function l(t){t>o.maxY&&(o.maxY=t),t<o.minY&&(o.minY=t)}if(s.type&L.DRAWING_COMMANDS&&(h(r),l(i)),s.type&L.HORIZ_LINE_TO&&h(s.x),s.type&L.VERT_LINE_TO&&l(s.y),s.type&L.LINE_TO&&(h(s.x),l(s.y)),s.type&L.CURVE_TO){h(s.x),l(s.y);for(var u=0,c=M(r,s.x1,s.x2,s.x);u<c.length;u++)0<(_=c[u])&&1>_&&h(E(r,s.x1,s.x2,s.x,_));for(var g=0,d=M(i,s.y1,s.y2,s.y);g<d.length;g++)0<(_=d[g])&&1>_&&l(E(i,s.y1,s.y2,s.y,_))}if(s.type&L.ARC){h(s.x),l(s.y),T(s,r,i);for(var p=s.xRot/180*Math.PI,f=Math.cos(p)*s.rX,y=Math.sin(p)*s.rX,m=-Math.sin(p)*s.rY,v=Math.cos(p)*s.rY,x=s.phi1<s.phi2?[s.phi1,s.phi2]:-180>s.phi2?[s.phi2+360,s.phi1+360]:[s.phi2,s.phi1],b=x[0],S=x[1],w=function(t){var e=t[0],r=t[1],i=180*Math.atan2(r,e)/Math.PI;return i<b?i+360:i},C=0,O=A(m,-f,0).map(w);C<O.length;C++)(_=O[C])>b&&_<S&&h(N(s.cX,f,m,_));for(var P=0,V=A(v,-y,0).map(w);P<V.length;P++){var _;(_=V[P])>b&&_<S&&l(N(s.cY,y,v,_))}}return e}));return o.minX=1/0,o.maxX=-1/0,o.minY=1/0,o.maxY=-1/0,o}}(C||(C={}));var V,_=function(){function t(){}return t.prototype.round=function(t){return this.transform(C.ROUND(t))},t.prototype.toAbs=function(){return this.transform(C.TO_ABS())},t.prototype.toRel=function(){return this.transform(C.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(C.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(C.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(C.QT_TO_C())},t.prototype.aToC=function(){return this.transform(C.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(C.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(C.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(C.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(C.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,i,s,a){return this.transform(C.MATRIX(t,e,r,i,s,a))},t.prototype.skewX=function(t){return this.transform(C.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(C.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(C.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(C.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(C.ANNOTATE_ARCS())},t}(),k=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},R=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},I=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return x(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var i=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},s=0;s<t.length;s++){var a=t[s],n=!(this.curCommandType!==L.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),o=R(a)&&("0"===this.curNumber&&"0"===a||n);if(!R(a)||o)if("e"!==a&&"E"!==a)if("-"!==a&&"+"!==a||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==a||this.curNumberHasExp||this.curNumberHasDecimal||n){if(this.curNumber&&-1!==this.curCommandType){var h=Number(this.curNumber);if(isNaN(h))throw new SyntaxError("Invalid number ending at "+s);if(this.curCommandType===L.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>h)throw new SyntaxError('Expected positive number, got "'+h+'" at index "'+s+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+s+'"');this.curArgs.push(h),this.curArgs.length===D[this.curCommandType]&&(L.HORIZ_LINE_TO===this.curCommandType?i({type:L.HORIZ_LINE_TO,relative:this.curCommandRelative,x:h}):L.VERT_LINE_TO===this.curCommandType?i({type:L.VERT_LINE_TO,relative:this.curCommandRelative,y:h}):this.curCommandType===L.MOVE_TO||this.curCommandType===L.LINE_TO||this.curCommandType===L.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),L.MOVE_TO===this.curCommandType&&(this.curCommandType=L.LINE_TO)):this.curCommandType===L.CURVE_TO?i({type:L.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===L.SMOOTH_CURVE_TO?i({type:L.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===L.QUAD_TO?i({type:L.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===L.ARC&&i({type:L.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!k(a))if(","===a&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==a&&"-"!==a&&"."!==a)if(o)this.curNumber=a,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+s+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+a+'" at index '+s+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==a&&"Z"!==a)if("h"===a||"H"===a)this.curCommandType=L.HORIZ_LINE_TO,this.curCommandRelative="h"===a;else if("v"===a||"V"===a)this.curCommandType=L.VERT_LINE_TO,this.curCommandRelative="v"===a;else if("m"===a||"M"===a)this.curCommandType=L.MOVE_TO,this.curCommandRelative="m"===a;else if("l"===a||"L"===a)this.curCommandType=L.LINE_TO,this.curCommandRelative="l"===a;else if("c"===a||"C"===a)this.curCommandType=L.CURVE_TO,this.curCommandRelative="c"===a;else if("s"===a||"S"===a)this.curCommandType=L.SMOOTH_CURVE_TO,this.curCommandRelative="s"===a;else if("q"===a||"Q"===a)this.curCommandType=L.QUAD_TO,this.curCommandRelative="q"===a;else if("t"===a||"T"===a)this.curCommandType=L.SMOOTH_QUAD_TO,this.curCommandRelative="t"===a;else{if("a"!==a&&"A"!==a)throw new SyntaxError('Unexpected character "'+a+'" at index '+s+".");this.curCommandType=L.ARC,this.curCommandRelative="a"===a}else e.push({type:L.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=a,this.curNumberHasDecimal="."===a}else this.curNumber+=a,this.curNumberHasDecimal=!0;else this.curNumber+=a;else this.curNumber+=a,this.curNumberHasExp=!0;else this.curNumber+=a,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var i=0,s=Object.getPrototypeOf(this).parse.call(this,e);i<s.length;i++){var a=s[i],n=t(a);Array.isArray(n)?r.push.apply(r,n):r.push(n)}return r}}})},e}(_),L=function(t){function e(r){var i=t.call(this)||this;return i.commands="string"==typeof r?e.parse(r):r,i}return x(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=C.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,i=this.commands;r<i.length;r++){var s=t(i[r]);Array.isArray(s)?e.push.apply(e,s):e.push(s)}return this.commands=e,this},e.encode=function(t){return function(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var i=t[r];if(i.type===L.CLOSE_PATH)e+="z";else if(i.type===L.HORIZ_LINE_TO)e+=(i.relative?"h":"H")+i.x;else if(i.type===L.VERT_LINE_TO)e+=(i.relative?"v":"V")+i.y;else if(i.type===L.MOVE_TO)e+=(i.relative?"m":"M")+i.x+" "+i.y;else if(i.type===L.LINE_TO)e+=(i.relative?"l":"L")+i.x+" "+i.y;else if(i.type===L.CURVE_TO)e+=(i.relative?"c":"C")+i.x1+" "+i.y1+" "+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===L.SMOOTH_CURVE_TO)e+=(i.relative?"s":"S")+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===L.QUAD_TO)e+=(i.relative?"q":"Q")+i.x1+" "+i.y1+" "+i.x+" "+i.y;else if(i.type===L.SMOOTH_QUAD_TO)e+=(i.relative?"t":"T")+i.x+" "+i.y;else{if(i.type!==L.ARC)throw new Error('Unexpected command type "'+i.type+'" at index '+r+".");e+=(i.relative?"a":"A")+i.rX+" "+i.rY+" "+i.xRot+" "+ +i.lArcFlag+" "+ +i.sweepFlag+" "+i.x+" "+i.y}}return e}(t)},e.parse=function(t){var e=new I,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(_),D=((V={})[L.MOVE_TO]=2,V[L.LINE_TO]=2,V[L.HORIZ_LINE_TO]=1,V[L.VERT_LINE_TO]=1,V[L.CLOSE_PATH]=0,V[L.QUAD_TO]=4,V[L.SMOOTH_QUAD_TO]=2,V[L.CURVE_TO]=6,V[L.SMOOTH_CURVE_TO]=4,V[L.ARC]=7,V);function z(t){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var B=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],H=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];var U=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null},X=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>s((function*(){var e=yield fetch(t),r=yield e.blob();return yield createImageBitmap(r)}))()};return"undefined"==typeof DOMParser&&void 0!==t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:i}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:i,createCanvas:r.createCanvas,createImage:r.loadImage}}});function F(t){return t.replace(/(?!\u3000)\s+/gm," ")}function Y(t){return t.replace(/^[\n \t]+/,"")}function j(t){return t.replace(/[\n \t]+$/,"")}function q(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var Q=/^[A-Z-]+$/;function W(t){return Q.test(t)?t.toLowerCase():t}function $(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function G(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,((t,r)=>e--&&r?String(Math.round(parseFloat(t))):t))}var Z=/(\[[^\]]+\])/g,K=/(#[^\s+>~.[:]+)/g,J=/(\.[^\s+>~.[:]+)/g,tt=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,et=/(:[\w-]+\([^)]*\))/gi,rt=/(:[^\s+>~.[:]+)/g,it=/([^\s+>~.[:]+)/g;function st(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function at(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),i=0;return[r,i]=st(r,Z),e[1]+=i,[r,i]=st(r,K),e[0]+=i,[r,i]=st(r,J),e[1]+=i,[r,i]=st(r,tt),e[2]+=i,[r,i]=st(r,et),e[1]+=i,[r,i]=st(r,rt),e[1]+=i,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,i]=st(r,it),e[2]+=i,e.join("")}var nt=1e-8;function ot(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function ht(t,e){return(t[0]*e[0]+t[1]*e[1])/(ot(t)*ot(e))}function lt(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(ht(t,e))}function ut(t){return t*t*t}function ct(t){return 3*t*t*(1-t)}function gt(t){return 3*t*(1-t)*(1-t)}function dt(t){return(1-t)*(1-t)*(1-t)}function pt(t){return t*t}function ft(t){return 2*t*(1-t)}function yt(t){return(1-t)*(1-t)}class mt{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new mt(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return F(this.getString()).trim().split(t).map((t=>new mt(e,r,t)))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,r="string"==typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=G(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,i]="boolean"==typeof t?[void 0,t]:[t],{viewPort:s}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(s.computeSize("x"),s.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(s.computeSize("x"),s.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*s.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*s.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*s.computeSize(r);default:var a=this.getNumber();return e&&a<1?a*s.computeSize(r):a}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?mt.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,i=0,s=0;s<r&&(","===e[s]&&i++,3!==i);s++);if(t.hasValue()&&this.isString()&&3!==i){var a=new m(e);a.ok&&(a.alpha=t.getNumber(),e=a.toRGBA())}return new mt(this.document,this.name,e)}}mt.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class vt{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class xt{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,i=e]=q(t);return new xt(r,i)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,i=r]=q(t);return new xt(r,i)}static parsePath(t){for(var e=q(t),r=e.length,i=[],s=0;s<r;s+=2)i.push(new xt(e[s],e[s+1]));return i}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[0]+r*t[2]+t[4],s=e*t[1]+r*t[3]+t[5];this.x=i,this.y=s}}class bt{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,i=t.ctx.canvas;i.onclick=e,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:i}=t.ctx.canvas;i&&(i.cursor=""),e.forEach(((t,e)=>{for(var{run:i}=t,s=r[e];s;)i(s),s=s.parent})),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach(((r,s)=>{var{x:a,y:n}=r;!i[s]&&e.isPointInPath&&e.isPointInPath(a,n)&&(i[s]=t)}))}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach(((r,s)=>{var{x:a,y:n}=r;!i[s]&&e.isPointInBox(a,n)&&(i[s]=t)}))}}mapXY(t,e){for(var{window:r,ctx:i}=this.screen,s=new xt(t,e),a=i.canvas;a;)s.x-=a.offsetLeft,s.y-=a.offsetTop,a=a.offsetParent;return r.scrollX&&(s.x+=r.scrollX),r.scrollY&&(s.y+=r.scrollY),s}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var St="undefined"!=typeof window?window:null,wt="undefined"!=typeof fetch?fetch.bind(void 0):null;class Tt{constructor(t){var{fetch:e=wt,window:r=St}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new vt,this.mouse=new bt(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every((t=>t()));return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:i,width:s,desiredWidth:a,height:n,desiredHeight:o,minX:h=0,minY:l=0,refX:u,refY:c,clip:g=!1,clipX:d=0,clipY:p=0}=t,f=F(i).replace(/^defer\s/,""),[y,m]=f.split(" "),v=y||"xMidYMid",x=m||"meet",b=s/a,S=n/o,w=Math.min(b,S),T=Math.max(b,S),A=a,C=o;"meet"===x&&(A*=w,C*=w),"slice"===x&&(A*=T,C*=T);var O=new mt(e,"refX",u),P=new mt(e,"refY",c),N=O.hasValue()&&P.hasValue();if(N&&r.translate(-w*O.getPixels("x"),-w*P.getPixels("y")),g){var M=w*d,E=w*p;r.beginPath(),r.moveTo(M,E),r.lineTo(s,E),r.lineTo(s,n),r.lineTo(M,n),r.closePath(),r.clip()}if(!N){var V="meet"===x&&w===S,_="slice"===x&&T===S,k="meet"===x&&w===b,R="slice"===x&&T===b;v.startsWith("xMid")&&(V||_)&&r.translate(s/2-A/2,0),v.endsWith("YMid")&&(k||R)&&r.translate(0,n/2-C/2),v.startsWith("xMax")&&(V||_)&&r.translate(s-A,0),v.endsWith("YMax")&&(k||R)&&r.translate(0,n-C)}switch(!0){case"none"===v:r.scale(b,S);break;case"meet"===x:r.scale(w,w);break;case"slice"===x:r.scale(T,T)}r.translate(-h,-l)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:s=!1,ignoreClear:a=!1,forceRedraw:n,scaleWidth:o,scaleHeight:h,offsetX:l,offsetY:u}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:c,mouse:g}=this,p=1e3/c;if(this.frameDuration=p,this.readyPromise=new Promise((t=>{this.resolveReady=t})),this.isReady()&&this.render(t,s,a,o,h,l,u),e){var f=Date.now(),y=f,m=0,v=()=>{f=Date.now(),(m=f-y)>=p&&(y=f-m%p,this.shouldUpdate(i,n)&&(this.render(t,s,a,o,h,l,u),g.runEvents())),this.intervalId=d(v)};r||g.start(),this.intervalId=d(v)}}stop(){this.intervalId&&(d.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce(((t,e)=>e.update(r)||t),!1))return!0}return!("function"!=typeof e||!e())||!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents()}render(t,e,r,i,s,a,n){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:h,viewPort:l,ctx:u,isFirstRender:c}=this,g=u.canvas;l.clear(),g.width&&g.height?l.setCurrent(g.width,g.height):l.setCurrent(o,h);var d=t.getStyle("width"),p=t.getStyle("height");!e&&(c||"number"!=typeof i&&"number"!=typeof s)&&(d.hasValue()&&(g.width=d.getPixels("x"),g.style&&(g.style.width="".concat(g.width,"px"))),p.hasValue()&&(g.height=p.getPixels("y"),g.style&&(g.style.height="".concat(g.height,"px"))));var f=g.clientWidth||g.width,y=g.clientHeight||g.height;if(e&&d.hasValue()&&p.hasValue()&&(f=d.getPixels("x"),y=p.getPixels("y")),l.setCurrent(f,y),"number"==typeof a&&t.getAttribute("x",!0).setValue(a),"number"==typeof n&&t.getAttribute("y",!0).setValue(n),"number"==typeof i||"number"==typeof s){var m=q(t.getAttribute("viewBox").getString()),v=0,x=0;if("number"==typeof i){var b=t.getStyle("width");b.hasValue()?v=b.getPixels("x")/i:isNaN(m[2])||(v=m[2]/i)}if("number"==typeof s){var S=t.getStyle("height");S.hasValue()?x=S.getPixels("y")/s:isNaN(m[3])||(x=m[3]/s)}v||(v=x),x||(x=v),t.getAttribute("width",!0).setValue(i),t.getAttribute("height",!0).setValue(s);var w=t.getStyle("transform",!0,!0);w.setValue("".concat(w.getString()," scale(").concat(1/v,", ").concat(1/x,")"))}r||u.clearRect(0,0,f,y),t.render(u),c&&(this.isFirstRender=!1)}}Tt.defaultWindow=St,Tt.defaultFetch=wt;var{defaultFetch:At}=Tt,Ct="undefined"!=typeof DOMParser?DOMParser:null;class Ot{constructor(){var{fetch:t=At,DOMParser:e=Ct}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return s((function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)}))()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return s((function*(){var r=yield e.fetch(t),i=yield r.text();return e.parseFromString(i)}))()}}class Pt{constructor(t,e){this.type="translate",this.point=null,this.point=xt.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class Nt{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=q(e);this.angle=new mt(t,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(t){var{cx:e,cy:r,originX:i,originY:s,angle:a}=this,n=e+i.getPixels("x"),o=r+s.getPixels("y");t.translate(n,o),t.rotate(a.getRadians()),t.translate(-n,-o)}unapply(t){var{cx:e,cy:r,originX:i,originY:s,angle:a}=this,n=e+i.getPixels("x"),o=r+s.getPixels("y");t.translate(n,o),t.rotate(-1*a.getRadians()),t.translate(-n,-o)}applyToPoint(t){var{cx:e,cy:r,angle:i}=this,s=i.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(s),Math.sin(s),-Math.sin(s),Math.cos(s),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class Mt{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=xt.parseScale(e);0!==i.x&&0!==i.y||(i.x=nt,i.y=nt),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:i,originY:s}=this,a=i.getPixels("x"),n=s.getPixels("y");t.translate(a,n),t.scale(e,r||e),t.translate(-a,-n)}unapply(t){var{scale:{x:e,y:r},originX:i,originY:s}=this,a=i.getPixels("x"),n=s.getPixels("y");t.translate(a,n),t.scale(1/e,1/r||e),t.translate(-a,-n)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class Et{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=q(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:i}=this,s=e.getPixels("x"),a=r.getPixels("y");t.translate(s,a),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t.translate(-s,-a)}unapply(t){var{originX:e,originY:r,matrix:i}=this,s=i[0],a=i[2],n=i[4],o=i[1],h=i[3],l=i[5],u=1/(s*(1*h-0*l)-a*(1*o-0*l)+n*(0*o-0*h)),c=e.getPixels("x"),g=r.getPixels("y");t.translate(c,g),t.transform(u*(1*h-0*l),u*(0*l-1*o),u*(0*n-1*a),u*(1*s-0*n),u*(a*l-n*h),u*(n*o-s*l)),t.translate(-c,-g)}applyToPoint(t){t.applyTransform(this.matrix)}}class Vt extends Et{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new mt(t,"angle",e)}}class _t extends Vt{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class kt extends Vt{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class Rt{constructor(t,e,r){this.document=t,this.transforms=[];var i=function(t){return F(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e);i.forEach((t=>{if("none"!==t){var[e,i]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),s=Rt.transformTypes[e];void 0!==s&&this.transforms.push(new s(this.document,i,r))}}))}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[i,s=i]=e.getStyle("transform-origin",!1,!0).split(),a=[i,s];return r.hasValue()?new Rt(t,r.getString(),a):null}apply(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].applyToPoint(t)}}Rt.transformTypes={translate:Pt,rotate:Nt,scale:Mt,matrix:Et,skewX:_t,skewY:kt};class It{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){Array.from(e.attributes).forEach((e=>{var r=W(e.nodeName);this.attributes[r]=new mt(t,r,e.value)})),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()&&this.getAttribute("style").getString().split(";").map((t=>t.trim())).forEach((e=>{if(e){var[r,i]=e.split(":").map((t=>t.trim()));this.styles[r]=new mt(t,r,i)}}));var{definitions:i}=t,s=this.getAttribute("id");s.hasValue()&&(i[s.getString()]||(i[s.getString()]=this)),Array.from(e.childNodes).forEach((e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}}))}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var i=new mt(this.document,t,"");return this.attributes[t]=i,i}return r||mt.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return mt.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[t];if(i)return i;var s=this.getAttribute(t);if(null!=s&&s.hasValue())return this.styles[t]=s,s;if(!r){var{parent:a}=this;if(a){var n=a.getStyle(t);if(null!=n&&n.hasValue())return n}}if(e){var o=new mt(this.document,t,"");return this.styles[t]=o,o}return i||mt.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=Rt.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach((e=>{e.render(t)}))}addChild(t){var e=t instanceof It?t:this.document.createElement(t);e.parent=this,It.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"==typeof r.matches)return r.matches(t);var i=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!i||""===i)&&i.split(" ").some((e=>".".concat(e)===t))}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=t[r],s=e[r];if(i)for(var a in i){var n=this.stylesSpecificity[a];void 0===n&&(n="000"),s>=n&&(this.styles[a]=i[a],this.stylesSpecificity[a]=s)}}}removeStyles(t,e){return e.reduce(((e,r)=>{var i=t.getStyle(r);if(!i.hasValue())return e;var s=i.getString();return i.setValue(""),[...e,[r,s]]}),[])}restoreStyles(t,e){e.forEach((e=>{var[r,i]=e;t.getStyle(r,!0).setValue(i)}))}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}It.ignoreChildTypes=["title"];class Lt extends It{constructor(t,e,r){super(t,e,r)}}function Dt(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function zt(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function Bt(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class Ht{constructor(t,e,r,i,s,a){var n=a?"string"==typeof a?Ht.parse(a):a:{};this.fontFamily=s||n.fontFamily,this.fontSize=i||n.fontSize,this.fontStyle=t||n.fontStyle,this.fontWeight=r||n.fontWeight,this.fontVariant=e||n.fontVariant}static parse(){var t=arguments.length>1?arguments[1]:void 0,e="",r="",i="",s="",a="",n=F(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return n.forEach((t=>{switch(!0){case!o.fontStyle&&Ht.styles.includes(t):"inherit"!==t&&(e=t),o.fontStyle=!0;break;case!o.fontVariant&&Ht.variants.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&Ht.weights.includes(t):"inherit"!==t&&(i=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([s]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(a+=t)}})),new Ht(e,r,i,s,a,t)}toString(){return[zt(this.fontStyle),this.fontVariant,Bt(this.fontWeight),this.fontSize,(t=this.fontFamily,"undefined"==typeof process?t:t.trim().split(",").map(Dt).join(","))].join(" ").trim();var t}}Ht.styles="normal|italic|oblique|inherit",Ht.variants="normal|small-caps|inherit",Ht.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class Ut{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.addPoint(t,e),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:i,y2:s}=t;this.addPoint(e,r),this.addPoint(i,s)}}sumCubic(t,e,r,i,s){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*i+Math.pow(t,3)*s}bezierCurveAdd(t,e,r,i,s){var a=6*e-12*r+6*i,n=-3*e+9*r-9*i+3*s,o=3*r-3*e;if(0!==n){var h=Math.pow(a,2)-4*o*n;if(!(h<0)){var l=(-a+Math.sqrt(h))/(2*n);0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,i,s)):this.addY(this.sumCubic(l,e,r,i,s)));var u=(-a-Math.sqrt(h))/(2*n);0<u&&u<1&&(t?this.addX(this.sumCubic(u,e,r,i,s)):this.addY(this.sumCubic(u,e,r,i,s)))}}else{if(0===a)return;var c=-o/a;0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,i,s)):this.addY(this.sumCubic(c,e,r,i,s)))}}addBezierCurve(t,e,r,i,s,a,n,o){this.addPoint(t,e),this.addPoint(n,o),this.bezierCurveAdd(!0,t,r,s,n),this.bezierCurveAdd(!1,e,i,a,o)}addQuadraticCurve(t,e,r,i,s,a){var n=t+2/3*(r-t),o=e+2/3*(i-e),h=n+1/3*(s-t),l=o+1/3*(a-e);this.addBezierCurve(t,e,n,h,o,l,s,a)}isPointInBox(t,e){var{x1:r,y1:i,x2:s,y2:a}=this;return r<=t&&t<=s&&i<=e&&e<=a}}class Xt extends L{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new xt(0,0),this.control=new xt(0,0),this.current=new xt(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new xt(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==L.CURVE_TO&&t!==L.SMOOTH_CURVE_TO&&t!==L.QUAD_TO&&t!==L.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:i,y:s}}=this;return new xt(2*e-i,2*r-s)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:i,angles:s}=this;r&&s.length>0&&!s[s.length-1]&&(s[s.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var i=r+1;i<e;i++)if(t[i]){t[r]=t[i];break}return t}}class Ft extends It{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),s=this.getStyle("stroke"),a=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var n=r.getFillStyleDefinition(this,i);n&&(t.fillStyle=n)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(i.hasValue()){var h=new mt(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=h}if(s.isUrlDefinition()){var l=s.getFillStyleDefinition(this,a);l&&(t.strokeStyle=l)}else if(s.hasValue()){"currentColor"===s.getString()&&s.setValue(this.getStyle("color").getColor());var u=s.getString();"inherit"!==u&&(t.strokeStyle="none"===u?"rgba(0,0,0,0)":u)}if(a.hasValue()){var c=new mt(this.document,"stroke",t.strokeStyle).addOpacity(a).getString();t.strokeStyle=c}var g=this.getStyle("stroke-width");if(g.hasValue()){var d=g.getPixels();t.lineWidth=d||nt}var p=this.getStyle("stroke-linecap"),f=this.getStyle("stroke-linejoin"),y=this.getStyle("stroke-miterlimit"),m=this.getStyle("stroke-dasharray"),v=this.getStyle("stroke-dashoffset");if(p.hasValue()&&(t.lineCap=p.getString()),f.hasValue()&&(t.lineJoin=f.getString()),y.hasValue()&&(t.miterLimit=y.getNumber()),m.hasValue()&&"none"!==m.getString()){var x=q(m.getString());void 0!==t.setLineDash?t.setLineDash(x):void 0!==t.webkitLineDash?t.webkitLineDash=x:void 0===t.mozDash||1===x.length&&0===x[0]||(t.mozDash=x);var b=v.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=b:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=b:void 0!==t.mozDashOffset&&(t.mozDashOffset=b)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var S=this.getStyle("font"),w=this.getStyle("font-style"),T=this.getStyle("font-variant"),A=this.getStyle("font-weight"),C=this.getStyle("font-size"),O=this.getStyle("font-family"),P=new Ht(w.getString(),T.getString(),A.getString(),C.hasValue()?"".concat(C.getPixels(!0),"px"):"",O.getString(),Ht.parse(S.getString(),t.font));w.setValue(P.fontStyle),T.setValue(P.fontVariant),A.setValue(P.fontWeight),C.setValue(P.fontSize),O.setValue(P.fontFamily),t.font=P.toString(),C.isPixels()&&(this.document.emSize=C.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class Yt extends Ft{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new Xt(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new Ut;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case Xt.MOVE_TO:this.pathM(t,r);break;case Xt.LINE_TO:this.pathL(t,r);break;case Xt.HORIZ_LINE_TO:this.pathH(t,r);break;case Xt.VERT_LINE_TO:this.pathV(t,r);break;case Xt.CURVE_TO:this.pathC(t,r);break;case Xt.SMOOTH_CURVE_TO:this.pathS(t,r);break;case Xt.QUAD_TO:this.pathQ(t,r);break;case Xt.SMOOTH_QUAD_TO:this.pathT(t,r);break;case Xt.ARC:this.pathA(t,r);break;case Xt.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map(((t,e)=>[t,r[e]]))}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var i=r.length-1,s=this.getStyle("marker-start"),a=this.getStyle("marker-mid"),n=this.getStyle("marker-end");if(s.isUrlDefinition()){var o=s.getDefinition(),[h,l]=r[0];o.render(t,h,l)}if(a.isUrlDefinition())for(var u=a.getDefinition(),c=1;c<i;c++){var[g,d]=r[c];u.render(t,g,d)}if(n.isUrlDefinition()){var p=n.getDefinition(),[f,y]=r[i];p.render(t,f,y)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:i}=Yt.pathM(r),{x:s,y:a}=i;r.addMarker(i),e.addPoint(s,a),t&&t.moveTo(s,a)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:i,point:s}=Yt.pathL(r),{x:a,y:n}=s;r.addMarker(s,i),e.addPoint(a,n),t&&t.lineTo(a,n)}static pathH(t){var{current:e,command:r}=t,i=new xt((r.relative?e.x:0)+r.x,e.y);return t.current=i,{current:e,point:i}}pathH(t,e){var{pathParser:r}=this,{current:i,point:s}=Yt.pathH(r),{x:a,y:n}=s;r.addMarker(s,i),e.addPoint(a,n),t&&t.lineTo(a,n)}static pathV(t){var{current:e,command:r}=t,i=new xt(e.x,(r.relative?e.y:0)+r.y);return t.current=i,{current:e,point:i}}pathV(t,e){var{pathParser:r}=this,{current:i,point:s}=Yt.pathV(r),{x:a,y:n}=s;r.addMarker(s,i),e.addPoint(a,n),t&&t.lineTo(a,n)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:i,point:s,controlPoint:a,currentPoint:n}=Yt.pathC(r);r.addMarker(n,a,s),e.addBezierCurve(i.x,i.y,s.x,s.y,a.x,a.y,n.x,n.y),t&&t.bezierCurveTo(s.x,s.y,a.x,a.y,n.x,n.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:i,point:s,controlPoint:a,currentPoint:n}=Yt.pathS(r);r.addMarker(n,a,s),e.addBezierCurve(i.x,i.y,s.x,s.y,a.x,a.y,n.x,n.y),t&&t.bezierCurveTo(s.x,s.y,a.x,a.y,n.x,n.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:i,controlPoint:s,currentPoint:a}=Yt.pathQ(r);r.addMarker(a,s,s),e.addQuadraticCurve(i.x,i.y,s.x,s.y,a.x,a.y),t&&t.quadraticCurveTo(s.x,s.y,a.x,a.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:i,controlPoint:s,currentPoint:a}=Yt.pathT(r);r.addMarker(a,s,s),e.addQuadraticCurve(i.x,i.y,s.x,s.y,a.x,a.y),t&&t.quadraticCurveTo(s.x,s.y,a.x,a.y)}static pathA(t){var{current:e,command:r}=t,{rX:i,rY:s,xRot:a,lArcFlag:n,sweepFlag:o}=r,h=a*(Math.PI/180),l=t.getAsCurrentPoint(),u=new xt(Math.cos(h)*(e.x-l.x)/2+Math.sin(h)*(e.y-l.y)/2,-Math.sin(h)*(e.x-l.x)/2+Math.cos(h)*(e.y-l.y)/2),c=Math.pow(u.x,2)/Math.pow(i,2)+Math.pow(u.y,2)/Math.pow(s,2);c>1&&(i*=Math.sqrt(c),s*=Math.sqrt(c));var g=(n===o?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(s,2)-Math.pow(i,2)*Math.pow(u.y,2)-Math.pow(s,2)*Math.pow(u.x,2))/(Math.pow(i,2)*Math.pow(u.y,2)+Math.pow(s,2)*Math.pow(u.x,2)));isNaN(g)&&(g=0);var d=new xt(g*i*u.y/s,g*-s*u.x/i),p=new xt((e.x+l.x)/2+Math.cos(h)*d.x-Math.sin(h)*d.y,(e.y+l.y)/2+Math.sin(h)*d.x+Math.cos(h)*d.y),f=lt([1,0],[(u.x-d.x)/i,(u.y-d.y)/s]),y=[(u.x-d.x)/i,(u.y-d.y)/s],m=[(-u.x-d.x)/i,(-u.y-d.y)/s],v=lt(y,m);return ht(y,m)<=-1&&(v=Math.PI),ht(y,m)>=1&&(v=0),{currentPoint:l,rX:i,rY:s,sweepFlag:o,xAxisRotation:h,centp:p,a1:f,ad:v}}pathA(t,e){var{pathParser:r}=this,{currentPoint:i,rX:s,rY:a,sweepFlag:n,xAxisRotation:o,centp:h,a1:l,ad:u}=Yt.pathA(r),c=1-n?1:-1,g=l+c*(u/2),d=new xt(h.x+s*Math.cos(g),h.y+a*Math.sin(g));if(r.addMarkerAngle(d,g-c*Math.PI/2),r.addMarkerAngle(i,g-c*Math.PI),e.addPoint(i.x,i.y),t&&!isNaN(l)&&!isNaN(u)){var p=s>a?s:a,f=s>a?1:s/a,y=s>a?a/s:1;t.translate(h.x,h.y),t.rotate(o),t.scale(f,y),t.arc(0,0,p,l,l+u,Boolean(1-n)),t.scale(1/f,1/y),t.rotate(-o),t.translate(-h.x,-h.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){Yt.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class jt extends Yt{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class qt extends Ft{constructor(t,e,r){super(t,e,new.target===qt||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach(((r,i)=>{var s=this.getChildBoundingBox(t,this,this,i);e?e.addBoundingBox(s):e=s})),e}getFontSize(){var{document:t,parent:e}=this,r=Ht.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new Ut(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var i=e[r],s=null;if(t.isArabic){var a=e.length,n=e[r-1],o=e[r+1],h="isolated";if((0===r||" "===n)&&r<a-1&&" "!==o&&(h="terminal"),r>0&&" "!==n&&r<a-1&&" "!==o&&(h="medial"),r>0&&" "!==n&&(r===a-1||" "===o)&&(h="initial"),void 0!==t.glyphs[i]){var l=t.glyphs[i];s=l instanceof jt?l:l[h]}}else s=t.glyphs[i];return s||(s=t.missingGlyph),s}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),i=r.indexOf(e),s=r.length-1,a=F(e.textContent||"");return 0===i&&(a=Y(a)),i===s&&(a=j(a)),a}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach(((e,r)=>{this.renderChild(t,this,this,r)}));var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,i=this.getText(),s=r.getStyle("font-family").getDefinition();if(s)for(var{unitsPerEm:a}=s.fontFace,n=Ht.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(n.fontSize),h=r.getStyle("font-style").getString(n.fontStyle),l=o/a,u=s.isRTL?i.split("").reverse().join(""):i,c=q(r.getAttribute("dx").getString()),g=u.length,d=0;d<g;d++){var p=this.getGlyph(s,u,d);t.translate(this.x,this.y),t.scale(l,-l);var f=t.lineWidth;t.lineWidth=t.lineWidth*a/o,"italic"===h&&t.transform(1,0,.4,1,0,0),p.render(t),"italic"===h&&t.transform(1,0,-.4,1,0,0),t.lineWidth=f,t.scale(1/l,-1/l),t.translate(-this.x,-this.y),this.x+=o*(p.horizAdvX||s.horizAdvX)/a,void 0===c[d]||isNaN(c[d])||(this.x+=c[d])}else{var{x:y,y:m}=this;t.fillStyle&&t.fillText(i,y,m),t.strokeStyle&&t.strokeText(i,y,m)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t,e=this.leafTexts[this.textChunkStart],r=e.getStyle("text-anchor").getString("start");t="start"===r?e.x-this.minX:"end"===r?e.x-this.maxX:e.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=t;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach(((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)})),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,i){var s=r.children[i];s.children.length>0?s.children.forEach(((r,i)=>{e.adjustChildCoordinatesRecursiveCore(t,e,s,i)})):this.adjustChildCoordinates(t,e,r,i)}adjustChildCoordinates(t,e,r,i){var s=r.children[i];if("function"!=typeof s.measureText)return s;t.save(),s.setContext(t,!0);var a=s.getAttribute("x"),n=s.getAttribute("y"),o=s.getAttribute("dx"),h=s.getAttribute("dy"),l=s.getStyle("font-family").getDefinition(),u=Boolean(l)&&l.isRTL;0===i&&(a.hasValue()||a.setValue(s.getInheritedAttribute("x")),n.hasValue()||n.setValue(s.getInheritedAttribute("y")),o.hasValue()||o.setValue(s.getInheritedAttribute("dx")),h.hasValue()||h.setValue(s.getInheritedAttribute("dy")));var c=s.measureText(t);return u&&(e.x-=c),a.hasValue()?(e.applyAnchoring(),s.x=a.getPixels("x"),o.hasValue()&&(s.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),s.x=e.x),e.x=s.x,u||(e.x+=c),n.hasValue()?(s.y=n.getPixels("y"),h.hasValue()&&(s.y+=h.getPixels("y"))):(h.hasValue()&&(e.y+=h.getPixels("y")),s.y=e.y),e.y=s.y,e.leafTexts.push(s),e.minX=Math.min(e.minX,s.x,s.x+c),e.maxX=Math.max(e.maxX,s.x,s.x+c),s.clearContext(t),t.restore(),s}getChildBoundingBox(t,e,r,i){var s=r.children[i];if("function"!=typeof s.getBoundingBox)return null;var a=s.getBoundingBox(t);return a?(s.children.forEach(((r,i)=>{var n=e.getChildBoundingBox(t,e,s,i);a.addBoundingBox(n)})),a):null}renderChild(t,e,r,i){var s=r.children[i];s.render(t),s.children.forEach(((r,i)=>{e.renderChild(t,e,s,i)}))}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),i=this.measureTargetText(t,r);return this.measureCache=i,i}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var s=this.getFontSize(),a=i.isRTL?e.split("").reverse().join(""):e,n=q(r.getAttribute("dx").getString()),o=a.length,h=0,l=0;l<o;l++)h+=(this.getGlyph(i,a,l).horizAdvX||i.horizAdvX)*s/i.fontFace.unitsPerEm,void 0===n[l]||isNaN(n[l])||(h+=n[l]);return h}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:u}=t.measureText(e);return this.clearContext(t),t.restore(),u}getInheritedAttribute(t){for(var e=this;e instanceof qt&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class Qt extends qt{constructor(t,e,r){super(t,e,new.target===Qt||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class Wt extends Qt{constructor(){super(...arguments),this.type="textNode"}}class $t extends Ft{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:i,window:s}=r,a=t.canvas;if(i.setDefaults(t),a.style&&void 0!==t.font&&s&&void 0!==s.getComputedStyle){t.font=s.getComputedStyle(a).getPropertyValue("font");var n=new mt(r,"fontSize",Ht.parse(t.font).fontSize);n.hasValue()&&(r.rootEmSize=n.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:h}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var l=this.getAttribute("refX"),u=this.getAttribute("refY"),c=this.getAttribute("viewBox"),g=c.hasValue()?q(c.getString()):null,d=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),p=0,f=0,y=0,m=0;g&&(p=g[0],f=g[1]),this.root||(o=this.getStyle("width").getPixels("x"),h=this.getStyle("height").getPixels("y"),"marker"===this.type&&(y=p,m=f,p=0,f=0)),i.viewPort.setCurrent(o,h),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),g&&(o=g[2],h=g[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:o,height:i.viewPort.height,desiredHeight:h,minX:p,minY:f,refX:l.getValue(),refY:u.getValue(),clip:d,clipX:y,clipY:m}),g&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(o,h))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.getAttribute("width",!0),s=this.getAttribute("height",!0),a=this.getAttribute("viewBox"),n=this.getAttribute("style"),o=i.getNumber(0),h=s.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var l=this.getAttribute("preserveAspectRatio");l.hasValue()&&l.setValue(l.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(t),s.setValue(e),a.hasValue()||a.setValue("0 0 ".concat(o||t," ").concat(h||e)),n.hasValue()){var u=this.getStyle("width"),c=this.getStyle("height");u.hasValue()&&u.setValue("".concat(t,"px")),c.hasValue()&&c.setValue("".concat(e,"px"))}}}class Gt extends Yt{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),s=this.getStyle("height",!1,!0).getPixels("y"),a=this.getAttribute("rx"),n=this.getAttribute("ry"),o=a.getPixels("x"),h=n.getPixels("y");if(a.hasValue()&&!n.hasValue()&&(h=o),n.hasValue()&&!a.hasValue()&&(o=h),o=Math.min(o,i/2),h=Math.min(h,s/2),t){var l=(Math.sqrt(2)-1)/3*4;t.beginPath(),s>0&&i>0&&(t.moveTo(e+o,r),t.lineTo(e+i-o,r),t.bezierCurveTo(e+i-o+l*o,r,e+i,r+h-l*h,e+i,r+h),t.lineTo(e+i,r+s-h),t.bezierCurveTo(e+i,r+s-h+l*h,e+i-o+l*o,r+s,e+i-o,r+s),t.lineTo(e+o,r+s),t.bezierCurveTo(e+o-l*o,r+s,e,r+s-h+l*h,e,r+s-h),t.lineTo(e,r+h),t.bezierCurveTo(e,r+h-l*h,e+o-l*o,r,e+o,r),t.closePath())}return new Ut(e,r,e+i,r+s)}getMarkers(){return null}}class Zt extends Yt{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return t&&i>0&&(t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()),new Ut(e-i,r-i,e+i,r+i)}getMarkers(){return null}}class Kt extends Yt{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),s=this.getAttribute("cx").getPixels("x"),a=this.getAttribute("cy").getPixels("y");return t&&r>0&&i>0&&(t.beginPath(),t.moveTo(s+r,a),t.bezierCurveTo(s+r,a+e*i,s+e*r,a+i,s,a+i),t.bezierCurveTo(s-e*r,a+i,s-r,a+e*i,s-r,a),t.bezierCurveTo(s-r,a-e*i,s-e*r,a-i,s,a-i),t.bezierCurveTo(s+e*r,a-i,s+r,a-e*i,s+r,a),t.closePath()),new Ut(s-r,a-i,s+r,a+i)}getMarkers(){return null}}class Jt extends Yt{constructor(){super(...arguments),this.type="line"}getPoints(){return[new xt(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new xt(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:i,y:s}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(i,s)),new Ut(e,r,i,s)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class te extends Yt{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=xt.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:i}]=e,s=new Ut(r,i);return t&&(t.beginPath(),t.moveTo(r,i)),e.forEach((e=>{var{x:r,y:i}=e;s.addPoint(r,i),t&&t.lineTo(r,i)})),s}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach(((i,s)=>{s!==e&&r.push([i,i.angleTo(t[s+1])])})),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class ee extends te{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:i}]=this.points;return t&&(t.lineTo(r,i),t.closePath()),e}}class re extends It{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var i=this.getStyle("width").getPixels("x",!0),s=this.getStyle("height").getPixels("y",!0),a=new $t(this.document,null);a.attributes.viewBox=new mt(this.document,"viewBox",this.getAttribute("viewBox").getValue()),a.attributes.width=new mt(this.document,"width","".concat(i,"px")),a.attributes.height=new mt(this.document,"height","".concat(s,"px")),a.attributes.transform=new mt(this.document,"transform",this.getAttribute("patternTransform").getValue()),a.children=this.children;var n=this.document.createCanvas(i,s),o=n.getContext("2d"),h=this.getAttribute("x"),l=this.getAttribute("y");h.hasValue()&&l.hasValue()&&o.translate(h.getPixels("x",!0),l.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var u=-1;u<=1;u++)for(var c=-1;c<=1;c++)o.save(),a.attributes.x=new mt(this.document,"x",u*n.width),a.attributes.y=new mt(this.document,"y",c*n.height),a.render(o),o.restore();return t.createPattern(n,"repeat")}}class ie extends It{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:i,y:s}=e,a=this.getAttribute("orient").getString("auto"),n=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(i,s),"auto"===a&&t.rotate(r),"strokeWidth"===n&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new $t(this.document,null);o.type=this.type,o.attributes.viewBox=new mt(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new mt(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new mt(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new mt(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new mt(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new mt(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new mt(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new mt(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===n&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===a&&t.rotate(-r),t.translate(-i,-s)}}}class se extends It{constructor(){super(...arguments),this.type="defs"}render(){}}class ae extends Ft{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new Ut;return this.children.forEach((r=>{e.addBoundingBox(r.getBoundingBox(t))})),e}}class ne extends It{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:s}=this;s.forEach((t=>{"stop"===t.type&&i.push(t)}))}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:s}=i,a=this.getGradient(t,e);if(!a)return this.addParentOpacity(r,s[s.length-1].color);if(s.forEach((t=>{a.addColorStop(t.offset,this.addParentOpacity(r,t.color))})),this.getAttribute("gradientTransform").hasValue()){var{document:n}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:h}=n.screen,[l]=h.viewPorts,u=new Gt(n,null);u.attributes.x=new mt(n,"x",-o/3),u.attributes.y=new mt(n,"y",-o/3),u.attributes.width=new mt(n,"width",o),u.attributes.height=new mt(n,"height",o);var c=new ae(n,null);c.attributes.transform=new mt(n,"transform",this.getAttribute("gradientTransform").getValue()),c.children=[u];var g=new $t(n,null);g.attributes.x=new mt(n,"x",0),g.attributes.y=new mt(n,"y",0),g.attributes.width=new mt(n,"width",l.width),g.attributes.height=new mt(n,"height",l.height),g.children=[c];var d=n.createCanvas(l.width,l.height),p=d.getContext("2d");return p.fillStyle=a,g.render(p),p.createPattern(d,"no-repeat")}return a}inheritStopContainer(t){this.attributesToInherit.forEach((e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())}))}addParentOpacity(t,e){return t.hasValue()?new mt(this.document,"color",e).addOpacity(t).getColor():e}}class oe extends ne{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=r?e.getBoundingBox(t):null;if(r&&!i)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var s=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),a=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),n=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return s===n&&a===o?null:t.createLinearGradient(s,a,n,o)}}class he extends ne{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=e.getBoundingBox(t);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var s=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),a=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),n=s,o=a;this.getAttribute("fx").hasValue()&&(n=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var h=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),l=this.getAttribute("fr").getPixels();return t.createRadialGradient(n,o,l,s,a,h)}}class le extends It{constructor(t,e,r){super(t,e,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),s=this.getStyle("stop-opacity"),a=this.getStyle("stop-color",!0);""===a.getString()&&a.setValue("#000"),s.hasValue()&&(a=a.addOpacity(s)),this.offset=i,this.color=a.getColor()}}class ue extends It{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new mt(t,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:i}=this.getProgress(),s=r.getNumber()+(i.getNumber()-r.getNumber())*e;return"%"===t&&(s*=100),"".concat(s).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==i||this.frozen){if("remove"===i&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var s=!1;if(this.begin<this.duration){var a=this.calcValue(),n=this.getAttribute("type");if(n.hasValue()){var o=n.getString();a="".concat(o,"(").concat(a,")")}r.setValue(a),s=!0}return s}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var i=r.progress*(e.getValue().length-1),s=Math.floor(i),a=Math.ceil(i);r.from=new mt(t,"from",parseFloat(e.getValue()[s])),r.to=new mt(t,"to",parseFloat(e.getValue()[a])),r.progress=(i-s)/(a-s)}else r.from=this.from,r.to=this.to;return r}}class ce extends ue{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=new m(e.getColor()),s=new m(r.getColor());if(i.ok&&s.ok){var a=i.r+(s.r-i.r)*t,n=i.g+(s.g-i.g)*t,o=i.b+(s.b-i.b)*t;return"rgb(".concat(Math.floor(a),", ").concat(Math.floor(n),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class ge extends ue{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=q(e.getString()),s=q(r.getString());return i.map(((e,r)=>e+(s[r]-e)*t)).join(" ")}}class de extends It{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=t,{children:s}=this;for(var a of s)switch(a.type){case"font-face":this.fontFace=a;var n=a.getStyle("font-family");n.hasValue()&&(i[n.getString()]=this);break;case"missing-glyph":this.missingGlyph=a;break;case"glyph":var o=a;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[o.unicode]&&(this.glyphs[o.unicode]=Object.create(null)),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o}}render(){}}class pe extends It{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class fe extends Yt{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class ye extends qt{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class me extends qt{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:i}=e,s=i[0],a=i.length>0&&Array.from(i).every((t=>3===t.nodeType));this.hasText=a,this.text=a?this.getTextFromNode(s):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:i}=this,{mouse:s}=e.screen,a=new mt(e,"fontSize",Ht.parse(e.ctx.font).fontSize);s.isWorking()&&s.checkBoundingBox(this,new Ut(r,i-a.getPixels("y"),r+this.measureText(t),i))}else if(this.children.length>0){var n=new ae(this.document,null);n.children=this.children,n.parent=this,n.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function ve(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function xe(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ve(Object(r),!0).forEach((function(e){a(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ve(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}class be extends qt{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach((e=>{var{type:r,points:i}=e;switch(r){case Xt.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case Xt.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case Xt.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case Xt.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case Xt.ARC:var[s,a,n,o,h,l,u,c]=i,g=n>o?n:o,d=n>o?1:n/o,p=n>o?o/n:1;t&&(t.translate(s,a),t.rotate(u),t.scale(d,p),t.arc(0,0,g,h,h+l,Boolean(1-c)),t.scale(1/d,1/p),t.rotate(-u),t.translate(-s,-a));break;case Xt.CLOSE_PATH:t&&t.closePath()}}))}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,s=t.fillStyle;"underline"===e&&t.beginPath(),i.forEach(((i,s)=>{var{p0:a,p1:n,rotation:o,text:h}=i;t.save(),t.translate(a.x,a.y),t.rotate(o),t.fillStyle&&t.fillText(h,0,0),t.strokeStyle&&t.strokeText(h,0,0),t.restore(),"underline"===e&&(0===s&&t.moveTo(a.x,a.y+r/8),t.lineTo(n.x,n.y+r/5))})),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=s,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,i,s,a,n,o,h){var l=a,u=this.measureText(t,o);" "===o&&"justify"===e&&r<i&&(u+=(i-r)/s),h>-1&&(l+=this.getLetterSpacingAt(h));var c=this.textHeight/20,g=this.getEquidistantPointOnPath(l,c,0),d=this.getEquidistantPointOnPath(l+u,c,0),p={p0:g,p1:d},f=g&&d?Math.atan2(d.y-g.y,d.x-g.x):0;if(n){var y=Math.cos(Math.PI/2+f)*n,m=Math.cos(-f)*n;p.p0=xe(xe({},g),{},{x:g.x+y,y:g.y+m}),p.p1=xe(xe({},d),{},{x:d.x+y,y:d.y+m})}return{offset:l+=u,segment:p,rotation:f}}measureText(t,e){var{measuresCache:r}=this,i=e||this.getText();if(r.has(i))return r.get(i);var s=this.measureTargetText(t,i);return r.set(i,s),s}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),i=e.split(" ").length-1,s=this.parent.getAttribute("dx").split().map((t=>t.getPixels("x"))),a=this.parent.getAttribute("dy").getPixels("y"),n=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),h=this.parent.getStyle("letter-spacing"),l=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(l=o.getPixels()):l=h.getPixels();var u=[],c=e.length;this.letterSpacingCache=u;for(var g=0;g<c;g++)u.push(void 0!==s[g]?s[g]:l);var d=u.reduce(((t,e,r)=>0===r?0:t+e||0),0),p=this.measureText(t),f=Math.max(p+d,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var y=this.getPathLength(),m=this.getStyle("startOffset").getNumber(0)*y,v=0;"middle"!==n&&"center"!==n||(v=-f/2),"end"!==n&&"right"!==n||(v=-f),v+=m,r.forEach(((e,s)=>{var{offset:o,segment:h,rotation:l}=this.findSegmentToFitChar(t,n,f,y,i,v,a,e,s);v=o,h.p0&&h.p1&&this.glyphInfo.push({text:r[s],p0:h.p0,p1:h.p1,rotation:l})}))}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:i}=r,s=i?i.x:0,a=i?i.y:0,n=r.next(),o=n.type,h=[];switch(n.type){case Xt.MOVE_TO:this.pathM(r,h);break;case Xt.LINE_TO:o=this.pathL(r,h);break;case Xt.HORIZ_LINE_TO:o=this.pathH(r,h);break;case Xt.VERT_LINE_TO:o=this.pathV(r,h);break;case Xt.CURVE_TO:this.pathC(r,h);break;case Xt.SMOOTH_CURVE_TO:o=this.pathS(r,h);break;case Xt.QUAD_TO:this.pathQ(r,h);break;case Xt.SMOOTH_QUAD_TO:o=this.pathT(r,h);break;case Xt.ARC:h=this.pathA(r);break;case Xt.CLOSE_PATH:Yt.pathZ(r)}n.type!==Xt.CLOSE_PATH?e.push({type:o,points:h,start:{x:s,y:a},pathLength:this.calcLength(s,a,o,h)}):e.push({type:Xt.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:i}=Yt.pathM(t).point;e.push(r,i)}pathL(t,e){var{x:r,y:i}=Yt.pathL(t).point;return e.push(r,i),Xt.LINE_TO}pathH(t,e){var{x:r,y:i}=Yt.pathH(t).point;return e.push(r,i),Xt.LINE_TO}pathV(t,e){var{x:r,y:i}=Yt.pathV(t).point;return e.push(r,i),Xt.LINE_TO}pathC(t,e){var{point:r,controlPoint:i,currentPoint:s}=Yt.pathC(t);e.push(r.x,r.y,i.x,i.y,s.x,s.y)}pathS(t,e){var{point:r,controlPoint:i,currentPoint:s}=Yt.pathS(t);return e.push(r.x,r.y,i.x,i.y,s.x,s.y),Xt.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:i}=Yt.pathQ(t);e.push(r.x,r.y,i.x,i.y)}pathT(t,e){var{controlPoint:r,currentPoint:i}=Yt.pathT(t);return e.push(r.x,r.y,i.x,i.y),Xt.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:i,xAxisRotation:s,centp:a,a1:n,ad:o}=Yt.pathA(t);return 0===i&&o>0&&(o-=2*Math.PI),1===i&&o<0&&(o+=2*Math.PI),[a.x,a.y,e,r,n,o,s,i]}calcLength(t,e,r,i){var s=0,a=null,n=null,o=0;switch(r){case Xt.LINE_TO:return this.getLineLength(t,e,i[0],i[1]);case Xt.CURVE_TO:for(s=0,a=this.getPointOnCubicBezier(0,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),o=.01;o<=1;o+=.01)n=this.getPointOnCubicBezier(o,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),s+=this.getLineLength(a.x,a.y,n.x,n.y),a=n;return s;case Xt.QUAD_TO:for(s=0,a=this.getPointOnQuadraticBezier(0,t,e,i[0],i[1],i[2],i[3]),o=.01;o<=1;o+=.01)n=this.getPointOnQuadraticBezier(o,t,e,i[0],i[1],i[2],i[3]),s+=this.getLineLength(a.x,a.y,n.x,n.y),a=n;return s;case Xt.ARC:s=0;var h=i[4],l=i[5],u=i[4]+l,c=Math.PI/180;if(Math.abs(h-u)<c&&(c=Math.abs(h-u)),a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],h,0),l<0)for(o=h-c;o>u;o-=c)n=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),s+=this.getLineLength(a.x,a.y,n.x,n.y),a=n;else for(o=h+c;o<u;o+=c)n=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),s+=this.getLineLength(a.x,a.y,n.x,n.y),a=n;return n=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),s+this.getLineLength(a.x,a.y,n.x,n.y)}return 0}getPointOnLine(t,e,r,i,s){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,n=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(s-r)/(i-e+nt),h=Math.sqrt(t*t/(1+o*o));i<e&&(h*=-1);var l=o*h,u=null;if(i===e)u={x:a,y:n+l};else if((n-r)/(a-e+nt)===o)u={x:a+h,y:n+l};else{var c,g,d=this.getLineLength(e,r,i,s);if(d<nt)return null;var p=(a-e)*(i-e)+(n-r)*(s-r);c=e+(p/=d*d)*(i-e),g=r+p*(s-r);var f=this.getLineLength(a,n,c,g),y=Math.sqrt(t*t-f*f);h=Math.sqrt(y*y/(1+o*o)),i<e&&(h*=-1),u={x:c+h,y:g+(l=o*h)}}return u}getPointOnPath(t){var e=this.getPathLength(),r=0,i=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:s}=this;for(var a of s){if(!a||!(a.pathLength<5e-5||r+a.pathLength+5e-5<t)){var n=t-r,o=0;switch(a.type){case Xt.LINE_TO:i=this.getPointOnLine(n,a.start.x,a.start.y,a.points[0],a.points[1],a.start.x,a.start.y);break;case Xt.ARC:var h=a.points[4],l=a.points[5],u=a.points[4]+l;if(o=h+n/a.pathLength*l,l<0&&o<u||l>=0&&o>u)break;i=this.getPointOnEllipticalArc(a.points[0],a.points[1],a.points[2],a.points[3],o,a.points[6]);break;case Xt.CURVE_TO:(o=n/a.pathLength)>1&&(o=1),i=this.getPointOnCubicBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3],a.points[4],a.points[5]);break;case Xt.QUAD_TO:(o=n/a.pathLength)>1&&(o=1),i=this.getPointOnQuadraticBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3])}if(i)return i;break}r+=a.pathLength}return null}getLineLength(t,e,r,i){return Math.sqrt((r-t)*(r-t)+(i-e)*(i-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce(((t,e)=>e.pathLength>0?t+e.pathLength:t),0)),this.pathLength}getPointOnCubicBezier(t,e,r,i,s,a,n,o,h){return{x:o*ut(t)+a*ct(t)+i*gt(t)+e*dt(t),y:h*ut(t)+n*ct(t)+s*gt(t)+r*dt(t)}}getPointOnQuadraticBezier(t,e,r,i,s,a,n){return{x:a*pt(t)+i*ft(t)+e*yt(t),y:n*pt(t)+s*ft(t)+r*yt(t)}}getPointOnEllipticalArc(t,e,r,i,s,a){var n=Math.cos(a),o=Math.sin(a),h=r*Math.cos(s),l=i*Math.sin(s);return{x:t+(h*n-l*o),y:e+(h*o+l*n)}}buildEquidistantCache(t,e){var r=this.getPathLength(),i=e||.25,s=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==s||this.equidistantCache.precision!==i){this.equidistantCache={step:s,precision:i,points:[]};for(var a=0,n=0;n<=r;n+=i){var o=this.getPointOnPath(n),h=this.getPointOnPath(n+i);o&&h&&(a+=this.getLineLength(o.x,o.y,h.x,h.y))>=s&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:n}),a-=s)}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var i=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var Se=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class we extends Ft{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var s=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);t.images.push(this),s?this.loadSvg(i):this.loadImage(i),this.isSvg=s}}loadImage(t){var e=this;return s((function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(i){}e.loaded=!0}))()}loadSvg(t){var e=this;return s((function*(){var r=Se.exec(t);if(r){var i=r[5];"base64"===r[4]?e.image=atob(i):e.image=decodeURIComponent(i)}else try{var s=yield e.document.fetch(t),a=yield s.text();e.image=a}catch(n){}e.loaded=!0}))()}renderChildren(t){var{document:e,image:r,loaded:i}=this,s=this.getAttribute("x").getPixels("x"),a=this.getAttribute("y").getPixels("y"),n=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(i&&r&&n&&o){if(t.save(),t.translate(s,a),this.isSvg){var h=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:n,scaleHeight:o});h.document.documentElement.parent=this,h.render()}else{var l=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:n,desiredWidth:l.width,height:o,desiredHeight:l.height}),this.loaded&&(void 0===l.complete||l.complete)&&t.drawImage(l,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new Ut(t,e,t+r,e+i)}}class Te extends Ft{constructor(){super(...arguments),this.type="symbol"}render(t){}}class Ae{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return s((function*(){try{var{document:i}=r,s=(yield i.canvg.parser.load(e)).getElementsByTagName("font");Array.from(s).forEach((e=>{var r=i.createElement(e);i.definitions[t]=r}))}catch(a){}r.loaded=!0}))()}}class Ce extends It{constructor(t,e,r){super(t,e,r),this.type="style",F(Array.from(e.childNodes).map((t=>t.textContent)).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach((e=>{var r=e.trim();if(r){var i=r.split("{"),s=i[0].split(","),a=i[1].split(";");s.forEach((e=>{var r=e.trim();if(r){var i=t.styles[r]||{};if(a.forEach((e=>{var r=e.indexOf(":"),s=e.substr(0,r).trim(),a=e.substr(r+1,e.length-r).trim();s&&a&&(i[s]=new mt(t,s,a))})),t.styles[r]=i,t.stylesSpecificity[r]=at(r),"@font-face"===r){var s=i["font-family"].getString().replace(/"|'/g,"");i.src.getString().split(",").forEach((e=>{if(e.indexOf('format("svg")')>0){var r=$(e);r&&new Ae(t).load(s,r)}}))}}}))}}))}}Ce.parseExternalUrl=$;class Oe extends Ft{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var i=r;if("symbol"===r.type&&((i=new $t(e,null)).attributes.viewBox=new mt(e,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new mt(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new mt(e,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new mt(e,"opacity",this.calculateOpacity())),"svg"===i.type){var s=this.getStyle("width",!1,!0),a=this.getStyle("height",!1,!0);s.hasValue()&&(i.attributes.width=new mt(e,"width",s.getString())),a.hasValue()&&(i.attributes.height=new mt(e,"height",a.getString()))}var n=i.parent;i.parent=this,i.render(t),i.parent=n}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return Rt.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function Pe(t,e,r,i,s,a){return t[r*i*4+4*e+a]}function Ne(t,e,r,i,s,a,n){t[r*i*4+4*e+a]=n}function Me(t,e,r){return t[e]*r}function Ee(t,e,r,i){return e+Math.cos(t)*r+Math.sin(t)*i}class Ve extends It{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var i=q(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var s=i[0];i=[.213+.787*s,.715-.715*s,.072-.072*s,0,0,.213-.213*s,.715+.285*s,.072-.072*s,0,0,.213-.213*s,.715-.715*s,.072+.928*s,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var a=i[0]*Math.PI/180;i=[Ee(a,.213,.787,-.213),Ee(a,.715,-.715,-.715),Ee(a,.072,-.072,.928),0,0,Ee(a,.213,-.213,.143),Ee(a,.715,.285,.14),Ee(a,.072,-.072,-.283),0,0,Ee(a,.213,-.213,-.787),Ee(a,.715,-.715,.715),Ee(a,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,i,s){for(var{includeOpacity:a,matrix:n}=this,o=t.getImageData(0,0,i,s),h=0;h<s;h++)for(var l=0;l<i;l++){var u=Pe(o.data,l,h,i,0,0),c=Pe(o.data,l,h,i,0,1),g=Pe(o.data,l,h,i,0,2),d=Pe(o.data,l,h,i,0,3),p=Me(n,0,u)+Me(n,1,c)+Me(n,2,g)+Me(n,3,d)+Me(n,4,1),f=Me(n,5,u)+Me(n,6,c)+Me(n,7,g)+Me(n,8,d)+Me(n,9,1),y=Me(n,10,u)+Me(n,11,c)+Me(n,12,g)+Me(n,13,d)+Me(n,14,1),m=Me(n,15,u)+Me(n,16,c)+Me(n,17,g)+Me(n,18,d)+Me(n,19,1);a&&(p=0,f=0,y=0,m*=d/255),Ne(o.data,l,h,i,0,0,p),Ne(o.data,l,h,i,0,1,f),Ne(o.data,l,h,i,0,2,y),Ne(o.data,l,h,i,0,3,m)}t.clearRect(0,0,i,s),t.putImageData(o,0,0)}}class _e extends It{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),s=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),n=this.getStyle("height").getPixels("y");if(!a&&!n){var o=new Ut;this.children.forEach((e=>{o.addBoundingBox(e.getBoundingBox(t))})),i=Math.floor(o.x1),s=Math.floor(o.y1),a=Math.floor(o.width),n=Math.floor(o.height)}var h=this.removeStyles(e,_e.ignoreStyles),l=r.createCanvas(i+a,s+n),u=l.getContext("2d");r.screen.setDefaults(u),this.renderChildren(u),new Ve(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(u,0,0,i+a,s+n);var c=r.createCanvas(i+a,s+n),g=c.getContext("2d");r.screen.setDefaults(g),e.render(g),g.globalCompositeOperation="destination-in",g.fillStyle=u.createPattern(l,"no-repeat"),g.fillRect(0,0,i+a,s+n),t.fillStyle=g.createPattern(c,"no-repeat"),t.fillRect(0,0,i+a,s+n),this.restoreStyles(e,h)}render(t){}}_e.ignoreStyles=["mask","transform","clip-path"];var ke=()=>{};class Re extends It{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:i,closePath:s}=t;r&&(r.beginPath=ke,r.closePath=ke),Reflect.apply(i,t,[]),this.children.forEach((i=>{if(void 0!==i.path){var a=void 0!==i.elementTransform?i.elementTransform():null;a||(a=Rt.fromElement(e,i)),a&&a.apply(t),i.path(t),r&&(r.closePath=s),a&&a.unapply(t)}})),Reflect.apply(s,t,[]),t.clip(),r&&(r.beginPath=i,r.closePath=s)}render(t){}}class Ie extends It{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:i}=this,s=e.getBoundingBox(t);if(s){var a=0,n=0;i.forEach((t=>{var e=t.extraFilterDistance||0;a=Math.max(a,e),n=Math.max(n,e)}));var o=Math.floor(s.width),h=Math.floor(s.height),l=o+2*a,u=h+2*n;if(!(l<1||u<1)){var c=Math.floor(s.x),g=Math.floor(s.y),d=this.removeStyles(e,Ie.ignoreStyles),p=r.createCanvas(l,u),f=p.getContext("2d");r.screen.setDefaults(f),f.translate(-c+a,-g+n),e.render(f),i.forEach((t=>{"function"==typeof t.apply&&t.apply(f,0,0,l,u)})),t.drawImage(p,0,0,l,u,c-a,g-n,l,u),this.restoreStyles(e,d)}}}render(t){}}Ie.ignoreStyles=["filter","transform","clip-path"];class Le extends It{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,i,s){}}class De extends It{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,i,s){}}class ze extends It{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,i,s){}}class Be extends It{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,i,s){var{document:a,blurRadius:n}=this,o=a.window?a.window.document.body:null,h=t.canvas;h.id=a.getUniqueId(),o&&(h.style.display="none",o.appendChild(h)),function(t,e,r,i,s,a){if(!(isNaN(a)||a<1)){a|=0;var n=function(t,e,r,i,s){if("string"==typeof t&&(t=document.getElementById(t)),!t||"object"!==z(t)||!("getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var a=t.getContext("2d");try{return a.getImageData(e,r,i,s)}catch(n){throw new Error("unable to access image data: "+n)}}(t,e,r,i,s);n=function(t,e,r,i,s,a){for(var n,o=t.data,h=2*a+1,l=i-1,u=s-1,c=a+1,g=c*(c+1)/2,d=new U,p=d,f=1;f<h;f++)p=p.next=new U,f===c&&(n=p);p.next=d;for(var y=null,m=null,v=0,x=0,b=B[a],S=H[a],w=0;w<s;w++){p=d;for(var T=o[x],A=o[x+1],C=o[x+2],O=o[x+3],P=0;P<c;P++)p.r=T,p.g=A,p.b=C,p.a=O,p=p.next;for(var N=0,M=0,E=0,V=0,_=c*T,k=c*A,R=c*C,I=c*O,L=g*T,D=g*A,z=g*C,X=g*O,F=1;F<c;F++){var Y=x+((l<F?l:F)<<2),j=o[Y],q=o[Y+1],Q=o[Y+2],W=o[Y+3],$=c-F;L+=(p.r=j)*$,D+=(p.g=q)*$,z+=(p.b=Q)*$,X+=(p.a=W)*$,N+=j,M+=q,E+=Q,V+=W,p=p.next}y=d,m=n;for(var G=0;G<i;G++){var Z=X*b>>>S;if(o[x+3]=Z,0!==Z){var K=255/Z;o[x]=(L*b>>>S)*K,o[x+1]=(D*b>>>S)*K,o[x+2]=(z*b>>>S)*K}else o[x]=o[x+1]=o[x+2]=0;L-=_,D-=k,z-=R,X-=I,_-=y.r,k-=y.g,R-=y.b,I-=y.a;var J=G+a+1;J=v+(J<l?J:l)<<2,L+=N+=y.r=o[J],D+=M+=y.g=o[J+1],z+=E+=y.b=o[J+2],X+=V+=y.a=o[J+3],y=y.next;var tt=m,et=tt.r,rt=tt.g,it=tt.b,st=tt.a;_+=et,k+=rt,R+=it,I+=st,N-=et,M-=rt,E-=it,V-=st,m=m.next,x+=4}v+=i}for(var at=0;at<i;at++){var nt=o[x=at<<2],ot=o[x+1],ht=o[x+2],lt=o[x+3],ut=c*nt,ct=c*ot,gt=c*ht,dt=c*lt,pt=g*nt,ft=g*ot,yt=g*ht,mt=g*lt;p=d;for(var vt=0;vt<c;vt++)p.r=nt,p.g=ot,p.b=ht,p.a=lt,p=p.next;for(var xt=i,bt=0,St=0,wt=0,Tt=0,At=1;At<=a;At++){x=xt+at<<2;var Ct=c-At;pt+=(p.r=nt=o[x])*Ct,ft+=(p.g=ot=o[x+1])*Ct,yt+=(p.b=ht=o[x+2])*Ct,mt+=(p.a=lt=o[x+3])*Ct,Tt+=nt,bt+=ot,St+=ht,wt+=lt,p=p.next,At<u&&(xt+=i)}x=at,y=d,m=n;for(var Ot=0;Ot<s;Ot++){var Pt=x<<2;o[Pt+3]=lt=mt*b>>>S,lt>0?(lt=255/lt,o[Pt]=(pt*b>>>S)*lt,o[Pt+1]=(ft*b>>>S)*lt,o[Pt+2]=(yt*b>>>S)*lt):o[Pt]=o[Pt+1]=o[Pt+2]=0,pt-=ut,ft-=ct,yt-=gt,mt-=dt,ut-=y.r,ct-=y.g,gt-=y.b,dt-=y.a,Pt=at+((Pt=Ot+c)<u?Pt:u)*i<<2,pt+=Tt+=y.r=o[Pt],ft+=bt+=y.g=o[Pt+1],yt+=St+=y.b=o[Pt+2],mt+=wt+=y.a=o[Pt+3],y=y.next,ut+=nt=m.r,ct+=ot=m.g,gt+=ht=m.b,dt+=lt=m.a,Tt-=nt,bt-=ot,St-=ht,wt-=lt,m=m.next,x+=i}}return t}(n,0,0,i,s,a),t.getContext("2d").putImageData(n,e,r)}}(h,e,r,i,s,n),o&&o.removeChild(h)}}class He extends It{constructor(){super(...arguments),this.type="title"}}class Ue extends It{constructor(){super(...arguments),this.type="desc"}}var Xe={svg:$t,rect:Gt,circle:Zt,ellipse:Kt,line:Jt,polyline:te,polygon:ee,path:Yt,pattern:re,marker:ie,defs:se,linearGradient:oe,radialGradient:he,stop:le,animate:ue,animateColor:ce,animateTransform:ge,font:de,"font-face":pe,"missing-glyph":fe,glyph:jt,text:qt,tspan:Qt,tref:ye,a:me,textPath:be,image:we,g:ae,symbol:Te,style:Ce,use:Oe,mask:_e,clipPath:Re,filter:Ie,feDropShadow:Le,feMorphology:De,feComposite:ze,feColorMatrix:Ve,feGaussianBlur:Be,title:He,desc:Ue};function Fe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function Ye(){return Ye=s((function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise(((e,i)=>{r.onload=()=>{e(r)},r.onerror=(t,e,r,s,a)=>{i(a)},r.src=t}))})),Ye.apply(this,arguments)}class je{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:i=je.createCanvas,createImage:s=je.createImage,anonymousCrossOrigin:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(s,a),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(r,i)=>t(r,"boolean"==typeof i?i:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every((t=>t.loaded))}isFontsLoaded(){return this.fonts.every((t=>t.loaded))}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=je.elementTypes[e];return void 0!==r?new r(this,t):new Lt(this,t)}createTextNode(t){return new Wt(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Fe(Object(r),!0).forEach((function(e){a(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Fe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({document:this},t))}}function qe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function Qe(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?qe(Object(r),!0).forEach((function(e){a(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):qe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}je.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},je.createImage=function(t){return Ye.apply(this,arguments)},je.elementTypes=Xe;class We{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new Ot(r),this.screen=new Tt(t,r),this.options=r;var i=new je(this,r),s=i.createDocumentElement(e);this.document=i,this.documentElement=s}static from(t,e){var r=arguments;return s((function*(){var i=r.length>2&&void 0!==r[2]?r[2]:{},s=new Ot(i),a=yield s.parse(e);return new We(t,a,i)}))()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new Ot(r).parseFromString(e);return new We(t,i,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return We.from(t,e,Qe(Qe({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return We.fromString(t,e,Qe(Qe({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return s((function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(Qe({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()}))()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:i}=this;r.start(e,Qe(Qe({enableRedraw:!0},i),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}export{me as AElement,ce as AnimateColorElement,ue as AnimateElement,ge as AnimateTransformElement,Ut as BoundingBox,ut as CB1,ct as CB2,gt as CB3,dt as CB4,We as Canvg,Zt as CircleElement,Re as ClipPathElement,se as DefsElement,Ue as DescElement,je as Document,It as Element,Kt as EllipseElement,Ve as FeColorMatrixElement,ze as FeCompositeElement,Le as FeDropShadowElement,Be as FeGaussianBlurElement,De as FeMorphologyElement,Ie as FilterElement,Ht as Font,de as FontElement,pe as FontFaceElement,ae as GElement,jt as GlyphElement,ne as GradientElement,we as ImageElement,Jt as LineElement,oe as LinearGradientElement,ie as MarkerElement,_e as MaskElement,Et as Matrix,fe as MissingGlyphElement,bt as Mouse,nt as PSEUDO_ZERO,Ot as Parser,Yt as PathElement,Xt as PathParser,re as PatternElement,xt as Point,ee as PolygonElement,te as PolylineElement,mt as Property,pt as QB1,ft as QB2,yt as QB3,he as RadialGradientElement,Gt as RectElement,Ft as RenderedElement,Nt as Rotate,$t as SVGElement,Ae as SVGFontLoader,Mt as Scale,Tt as Screen,Vt as Skew,_t as SkewX,kt as SkewY,le as StopElement,Ce as StyleElement,Te as SymbolElement,ye as TRefElement,Qt as TSpanElement,qt as TextElement,be as TextPathElement,He as TitleElement,Rt as Transform,Pt as Translate,Lt as UnknownElement,Oe as UseElement,vt as ViewPort,F as compressSpaces,We as default,at as getSelectorSpecificity,W as normalizeAttributeName,G as normalizeColor,$ as parseExternalUrl,X as presets,q as toNumbers,Y as trimLeft,j as trimRight,ot as vectorMagnitude,lt as vectorsAngle,ht as vectorsRatio};
