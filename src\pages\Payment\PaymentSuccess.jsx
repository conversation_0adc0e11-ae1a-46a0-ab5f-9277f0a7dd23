import React, { useEffect, useState } from 'react';
import { motion } from 'motion/react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import { 
  CheckCircle, 
  Download, 
  Eye, 
  Home, 
  ArrowLeft,
  Calendar,
  CreditCard,
  Package
} from 'lucide-react';
import paymentService from '../../services/paymentService';
import { toast } from 'react-hot-toast';

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [paymentInfo, setPaymentInfo] = useState(null);
  const [designInfo, setDesignInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  // Lấy thông tin từ URL params theo PayOS Return URL format
  const code = searchParams.get('code');
  const paymentLinkId = searchParams.get('id');
  const cancel = searchParams.get('cancel');
  const status = searchParams.get('status');
  const orderCode = searchParams.get('orderCode');

  useEffect(() => {
    const handlePaymentSuccess = async () => {
      // Kiểm tra các tham số bắt buộc từ PayOS
      if (!orderCode) {
        toast.error('Thông tin thanh toán không hợp lệ');
        navigate('/account');
        return;
      }

      // Kiểm tra mã lỗi từ PayOS
      if (code !== '00') {
        toast.error('Thanh toán không thành công');
        navigate('/payment-cancel' + window.location.search);
        return;
      }

      // Kiểm tra trạng thái thanh toán
      if (status !== 'PAID') {
        toast.error('Thanh toán chưa hoàn thành');
        navigate('/payment-cancel' + window.location.search);
        return;
      }

      try {
        setLoading(true);

        // Cập nhật trạng thái thanh toán design
        const updateResult = await paymentService.updateDesignPaymentStatus(
          orderCode, // transactionIdGateway - sử dụng orderCode thay vì paymentLinkId
          'PAID' // status
        );

        if (updateResult.success) {
          setPaymentInfo({
            orderCode,
            paymentLinkId, // vẫn giữ để hiển thị thông tin
            status: 'PAID',
            amount: updateResult.data?.amount,
            designId: updateResult.data?.designId
          });

          // Lấy thông tin design nếu có
          if (updateResult.data?.designId) {
            // TODO: Implement design service to get design info
            // const designResult = await designService.getDesignById(updateResult.data.designId);
            // if (designResult.success) {
            //   setDesignInfo(designResult.data);
            // }
          }

          toast.success('Thanh toán thành công! Thiết kế đã được mở khóa.');
        } else {
          toast.error(updateResult.message || 'Có lỗi xảy ra khi cập nhật trạng thái thanh toán');
        }

      } catch (error) {
        console.error('Error handling payment success:', error);
        toast.error('Có lỗi xảy ra khi xử lý thanh toán');
      } finally {
        setLoading(false);
      }
    };

    handlePaymentSuccess();
  }, [orderCode, code, status, navigate]);

  const handleDownloadDesign = () => {
    if (designInfo?.pathUrl) {
      // Mở link download trong tab mới
      window.open(designInfo.pathUrl, '_blank');
      toast.success('Đang tải xuống thiết kế...');
    }
  };

  const handleViewDesign = () => {
    if (paymentInfo?.designId) {
      navigate(`/design-viewer/${paymentInfo.designId}`);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-dexin-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Đang xử lý thông tin thanh toán...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg shadow-lg overflow-hidden"
        >
          {/* Header với icon thành công */}
          <div className="bg-gradient-to-r from-green-500 to-green-600 px-6 py-8 text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <CheckCircle className="w-16 h-16 text-white mx-auto mb-4" />
            </motion.div>
            <h1 className="text-2xl font-bold text-white mb-2">
              Thanh toán thành công!
            </h1>
            <p className="text-green-100">
              Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của DEXIN
            </p>
          </div>

          {/* Thông tin thanh toán */}
          <div className="px-6 py-6">
            <div className="space-y-4">
              {/* Mã đơn hàng */}
              <div className="flex items-center justify-between py-3 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <CreditCard className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600">Mã đơn hàng:</span>
                </div>
                <span className="font-semibold text-gray-900">{paymentInfo?.orderCode}</span>
              </div>

              {/* Số tiền */}
              {paymentInfo?.amount && (
                <div className="flex items-center justify-between py-3 border-b border-gray-200">
                  <div className="flex items-center space-x-3">
                    <Package className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-600">Số tiền:</span>
                  </div>
                  <span className="font-semibold text-dexin-primary">
                    {Number(paymentInfo.amount).toLocaleString('vi-VN')} VNĐ
                  </span>
                </div>
              )}

              {/* Thời gian */}
              <div className="flex items-center justify-between py-3 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600">Thời gian:</span>
                </div>
                <span className="font-semibold text-gray-900">
                  {new Date().toLocaleString('vi-VN')}
                </span>
              </div>

              {/* Trạng thái */}
              <div className="flex items-center justify-between py-3">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600">Trạng thái:</span>
                </div>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  Đã thanh toán
                </span>
              </div>
            </div>

            {/* Thông báo quan trọng */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="text-sm font-medium text-blue-800 mb-2">
                Thông tin quan trọng:
              </h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Thiết kế của bạn đã được mở khóa và có thể xem/tải xuống</li>
                <li>• Bạn có thể truy cập thiết kế bất cứ lúc nào trong tài khoản</li>
                <li>• Nếu có vấn đề, vui lòng liên hệ hỗ trợ khách hàng</li>
              </ul>
            </div>

            {/* Action buttons */}
            <div className="mt-8 flex flex-col sm:flex-row gap-3">
              {paymentInfo?.designId && (
                <>
                  <button
                    onClick={handleViewDesign}
                    className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-dexin-primary text-white rounded-lg hover:bg-dexin-light transition-colors"
                  >
                    <Eye className="w-4 h-4" />
                    <span>Xem thiết kế</span>
                  </button>

                  <button
                    onClick={handleDownloadDesign}
                    className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    <span>Tải xuống</span>
                  </button>
                </>
              )}
              
              <Link
                to="/account"
                className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Về tài khoản</span>
              </Link>
              
              <Link
                to="/"
                className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Home className="w-4 h-4" />
                <span>Trang chủ</span>
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PaymentSuccess;
