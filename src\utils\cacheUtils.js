/**
 * Lightweight Cache Utils - Chỉ essential functions
 */

// Cache keys constants
export const CACHE_KEYS = {
  VERSION: 'dexin_cache_version',
  AUTO_REFRESH: 'dexin_auto_refresh'
};

// Get current app version
export const getCurrentAppVersion = () => {
  return window.APP_VERSION || Date.now().toString();
};

// Check if cache should be cleared
export const shouldClearCache = () => {
  const currentVersion = getCurrentAppVersion();
  const storedVersion = localStorage.getItem(CACHE_KEYS.VERSION);
  return !storedVersion || storedVersion !== currentVersion;
};

// Essential cache clear only
export const clearEssentialCache = async () => {
  try {
    // Clear browser caches
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
    }

    // Clear temp localStorage only
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('dexin_temp_') || key.includes('cache_'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));

  } catch (error) {
    // Silent fail
  }
};

// Manual refresh - simplified
export const manualRefresh = {
  soft: async () => {
    await clearEssentialCache();
    localStorage.setItem(CACHE_KEYS.VERSION, getCurrentAppVersion());
    window.location.reload();
  }
};

// Auto refresh toggle
export const autoRefresh = {
  isEnabled: () => localStorage.getItem(CACHE_KEYS.AUTO_REFRESH) === 'enabled',
  toggle: () => {
    const current = autoRefresh.isEnabled();
    localStorage.setItem(CACHE_KEYS.AUTO_REFRESH, current ? 'disabled' : 'enabled');
    return !current;
  }
};

// Lightweight initialization
export const initializeCacheManagement = () => {
  if (shouldClearCache()) {
    clearEssentialCache().then(() => {
      localStorage.setItem(CACHE_KEYS.VERSION, getCurrentAppVersion());
    });
  }
};

// Export default minimal object
export default {
  clearEssentialCache,
  manualRefresh,
  autoRefresh,
  initializeCacheManagement,
  CACHE_KEYS,
  shouldClearCache,
  getCurrentAppVersion
}; 