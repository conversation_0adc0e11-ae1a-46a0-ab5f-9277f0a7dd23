#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> để validate sitemap.xml cho DEXIN
 * Kiểm tra format XML, URLs và accessibility
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// Màu sắc cho console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Hàm log với màu sắc
const log = {
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bold}${colors.blue}${msg}${colors.reset}`)
};

// Hàm kiểm tra URL accessibility
function checkURL(url) {
  return new Promise((resolve) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, (res) => {
      resolve({
        url,
        status: res.statusCode,
        accessible: res.statusCode >= 200 && res.statusCode < 400
      });
    });

    req.on('error', () => {
      resolve({
        url,
        status: 'ERROR',
        accessible: false
      });
    });

    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        url,
        status: 'TIMEOUT',
        accessible: false
      });
    });
  });
}

// Hàm parse sitemap XML
function parseSitemap(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Kiểm tra XML format cơ bản
    if (!content.includes('<?xml version="1.0" encoding="UTF-8"?>')) {
      throw new Error('Missing XML declaration');
    }

    if (!content.includes('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"')) {
      throw new Error('Missing or incorrect urlset declaration');
    }

    // Extract URLs
    const urlMatches = content.match(/<loc>(.*?)<\/loc>/g);
    if (!urlMatches) {
      throw new Error('No URLs found in sitemap');
    }

    const urls = urlMatches.map(match => 
      match.replace('<loc>', '').replace('</loc>', '')
    );

    // Extract other data
    const lastmodMatches = content.match(/<lastmod>(.*?)<\/lastmod>/g) || [];
    const changefreqMatches = content.match(/<changefreq>(.*?)<\/changefreq>/g) || [];
    const priorityMatches = content.match(/<priority>(.*?)<\/priority>/g) || [];

    return {
      urls,
      lastmod: lastmodMatches.map(m => m.replace(/<\/?lastmod>/g, '')),
      changefreq: changefreqMatches.map(m => m.replace(/<\/?changefreq>/g, '')),
      priority: priorityMatches.map(m => m.replace(/<\/?priority>/g, ''))
    };
  } catch (error) {
    throw new Error(`Failed to parse sitemap: ${error.message}`);
  }
}

// Hàm validate sitemap
async function validateSitemap(filePath) {
  log.header(`Validating sitemap: ${filePath}`);

  try {
    // Parse sitemap
    const sitemap = parseSitemap(filePath);
    log.success(`Found ${sitemap.urls.length} URLs in sitemap`);

    // Validate URLs
    log.info('Checking URL format and domain...');
    const expectedDomain = 'https://dexin.io.vn';
    let validUrls = 0;

    sitemap.urls.forEach(url => {
      if (url.startsWith(expectedDomain)) {
        validUrls++;
        log.success(`✓ ${url}`);
      } else {
        log.error(`✗ Invalid domain: ${url}`);
      }
    });

    // Validate required pages
    const requiredPages = [
      '/',
      '/store',
      '/phac-y',
      '/mo-loi',
      '/ngo-loi',
      '/chung-nhip',
      '/chuyen-nha',
      '/wishlist',
      '/cart'
    ];

    log.info('Checking required pages...');
    requiredPages.forEach(page => {
      const fullUrl = expectedDomain + page;
      if (sitemap.urls.includes(fullUrl)) {
        log.success(`✓ Required page found: ${page}`);
      } else {
        log.error(`✗ Missing required page: ${page}`);
      }
    });

    // Validate priorities
    log.info('Checking priorities...');
    sitemap.priority.forEach((priority, index) => {
      const priorityNum = parseFloat(priority);
      if (priorityNum >= 0.0 && priorityNum <= 1.0) {
        log.success(`✓ Valid priority: ${priority} for ${sitemap.urls[index]}`);
      } else {
        log.error(`✗ Invalid priority: ${priority} for ${sitemap.urls[index]}`);
      }
    });

    // Validate changefreq
    log.info('Checking changefreq values...');
    const validChangefreq = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];
    sitemap.changefreq.forEach((freq, index) => {
      if (validChangefreq.includes(freq)) {
        log.success(`✓ Valid changefreq: ${freq} for ${sitemap.urls[index]}`);
      } else {
        log.error(`✗ Invalid changefreq: ${freq} for ${sitemap.urls[index]}`);
      }
    });

    // Validate lastmod dates
    log.info('Checking lastmod dates...');
    sitemap.lastmod.forEach((date, index) => {
      const dateObj = new Date(date);
      if (!isNaN(dateObj.getTime())) {
        log.success(`✓ Valid date: ${date} for ${sitemap.urls[index]}`);
      } else {
        log.error(`✗ Invalid date: ${date} for ${sitemap.urls[index]}`);
      }
    });

    return {
      valid: validUrls === sitemap.urls.length,
      totalUrls: sitemap.urls.length,
      validUrls,
      urls: sitemap.urls
    };

  } catch (error) {
    log.error(`Validation failed: ${error.message}`);
    return { valid: false, error: error.message };
  }
}

// Main function
async function main() {
  log.header('🔍 DEXIN Sitemap Validator');
  log.info('Validating sitemap.xml files for dexin.io.vn domain');

  const sitemapPaths = [
    './public/sitemap.xml',
    './build/sitemap.xml'
  ];

  let allValid = true;

  for (const sitemapPath of sitemapPaths) {
    if (fs.existsSync(sitemapPath)) {
      const result = await validateSitemap(sitemapPath);
      if (!result.valid) {
        allValid = false;
      }
    } else {
      log.error(`Sitemap not found: ${sitemapPath}`);
      allValid = false;
    }
  }

  // Summary
  log.header('📊 Validation Summary');
  if (allValid) {
    log.success('All sitemaps are valid! ✨');
    log.info('Your sitemaps are ready for Google Search Console submission.');
    log.info('Domain: https://dexin.io.vn');
  } else {
    log.error('Some issues found in sitemaps. Please fix them before deployment.');
  }

  process.exit(allValid ? 0 : 1);
}

// Run the validator
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { validateSitemap, parseSitemap };
