import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Tag } from 'lucide-react';
import Button from '../../../components/common/Button';

// Hàm định dạng giá tiền
const formatPrice = (price) => {
  return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
};

const CartSummary = ({ cartTotal }) => {
  const [promoCode, setPromoCode] = useState('');
  const [discount, setDiscount] = useState(0);
  const navigate = useNavigate();

  const handleApplyPromo = () => {
    if (promoCode.toUpperCase() === 'DEXIN10') {
      const discountAmount = cartTotal * 0.1;
      setDiscount(discountAmount);
    } else {
      setDiscount(0);
      alert('Mã giảm gi<PERSON> không hợp lệ!');
    }
  };

  const handleCheckout = () => {
    navigate('/cart/checkout');
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 sticky top-24">
      <h2 className="text-xl font-bold mb-4">Thanh toán</h2>

      {/* Chi tiết giá */}
      <div className="mb-4">
        <div className="flex justify-between py-2 border-b border-gray-100">
          <span className="text-gray-600">Tạm tính</span>
          <span className="font-medium">{formatPrice(cartTotal)}</span>
        </div>

        {discount > 0 && (
          <div className="flex justify-between py-2 border-b border-gray-100 text-green-600">
            <span>Giảm giá</span>
            <span>- {formatPrice(discount)}</span>
          </div>
        )}

        <div className="flex justify-between py-2 border-b border-gray-100">
          <span className="text-gray-600">Vận chuyển</span>
          <span className="font-medium">0 ₫</span>
        </div>

        <div className="flex justify-between py-2 font-bold text-lg mt-2">
          <span>Tổng tiền</span>
          <span className="text-dexin-primary">{formatPrice(cartTotal - discount)}</span>
        </div>
      </div>

      {/* Nhập mã giảm giá */}
      <div className="mb-6">
        <div className="flex mb-2 items-center">
          <Tag className="h-4 w-4 text-dexin-primary mr-2" />
          <span className="text-sm font-medium">Mã giảm giá</span>
        </div>
        <div className="flex">
          <input
            type="text"
            value={promoCode}
            onChange={(e) => setPromoCode(e.target.value)}
            placeholder="Nhập mã giảm giá"
            className="flex-1 border border-gray-300 rounded-l-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-dexin-light"
          />
          <button
            onClick={handleApplyPromo}
            className="bg-dexin-light text-white px-4 py-2 rounded-r-lg hover:bg-pink-600 transition-colors"
          >
            Áp dụng
          </button>
        </div>
        {promoCode && promoCode.toUpperCase() === 'DEXIN10' && discount > 0 && (
          <div className="text-green-600 text-sm mt-2">
            Đã áp dụng mã giảm giá DEXIN10 (giảm 10%)
          </div>
        )}
      </div>

      {/* Nút thanh toán */}
      <Button
        variant="primary"
        size="lg"
        className="w-full mb-4"
        onClick={handleCheckout}
      >
        Tiến hành thanh toán
      </Button>

      {/* Chính sách */}
      <div className="text-xs text-gray-500 leading-relaxed">
        <p className="mb-2">Bằng cách đặt hàng, bạn đồng ý với Điều khoản sử dụng của DEXIN</p>
        <p>Khách hàng có thể hủy đơn hàng trong vòng 24 giờ sau khi đặt</p>
      </div>
    </div>
  );
};

export default CartSummary; 