{"name": "dexin", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@google/generative-ai": "^0.24.0", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "@vercel/analytics": "^1.5.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compressorjs": "^1.2.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "emoji-picker-react": "^4.12.2", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "konva": "^9.3.20", "lucide-react": "^0.484.0", "motion": "^12.6.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-konva": "^19.0.3", "react-markdown": "^10.1.0", "react-range": "^1.10.0", "react-resizable-panels": "^3.0.2", "react-router-dom": "^7.4.0", "react-toastify": "^11.0.5", "remark-gfm": "^4.0.1", "sonner": "^2.0.5", "styled-components": "^6.1.17", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "use-image": "^1.1.1", "vaul": "^1.1.2", "web-vitals": "^5.0.1", "zod": "^3.25.64"}, "scripts": {"dev": "vite", "build": "vite build", "serve": "serve -s build -l 3000", "serve:vite": "vite preview", "test": "vitest", "dev:prod": "cross-env NODE_ENV=production vite", "dev:staging": "cross-env NODE_ENV=staging vite", "build:prod": "cross-env NODE_ENV=production vite build", "build:staging": "cross-env NODE_ENV=staging vite build", "build:analyze": "vite build --mode analyze", "preview": "vite preview", "clean": "node clear-cache.js", "fresh": "npm run clean && npm run serve", "cache:clear": "node clear-cache.js", "cache:auto": "node clear-cache.js --auto-version", "cache:force": "node clear-cache.js --force-refresh", "deploy:fresh": "npm run cache:auto && npm run build"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "postcss": "^8.5.3", "postcss-import": "^16.0.0", "postcss-nesting": "^12.0.1", "serve": "^14.2.4", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "^3.4.1", "terser": "^5.39.2", "vite": "^6.3.5", "vitest": "^3.1.4"}}