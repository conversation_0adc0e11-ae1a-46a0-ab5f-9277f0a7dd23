import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Search, Heart, MessageSquare, ThumbsUp } from 'lucide-react';
import { Button } from '../components/common';
import { motion } from 'motion/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCrown } from '@fortawesome/free-solid-svg-icons';
import SEOHead from '../components/common/SEOHead';
import { pagesSEO } from '../utils/seoUtils';
// Dữ liệu mẫu cho bài viết
const postData = [
  {
    id: 1,
    author: 'Quốc Bảo',
    avatar: 'https://i.pravatar.cc/150?img=1',
    time: '1 phút trước',
    title: 'Làm sao để phòng ngủ nhà vẫn có cảm giác rộng rãi?',
    content: 'Phòng mình khá nhỏ, chỉ khoảng 9m², mà đồ đạc thì nhi<PERSON>, <PERSON><PERSON><PERSON> nào cũng thấy chật chội và bí bách. <PERSON><PERSON><PERSON> đã thử dọn bớt đồ nhưng vẫn không đủ rộng như mong muốn. Mình nghĩ nếu có cách sắp xếp nội thất hoặc dùng...',
    tags: ['#Decor', '#PhòngNgủNhỏ', '#KhôngGianNhỏ', '#TổLàmĐẹpGọn', '#MoiTrangTri'],
    likes: 0,
    comments: 0,
    views: 0
  },
  {
    id: 2,
    author: 'Quỳnh Hương',
    avatar: 'https://i.pravatar.cc/150?img=5',
    time: '21 phút trước',
    title: 'Tự làm đèn LED treo tường chỉ với 100k!',
    content: 'Gần đây mình đã tự DIY một chiếc đèn LED treo tường với chi phí chỉ khoảng 100k. Rất đơn giản và hiệu quả!',
    tags: ['#DIY', '#Decor', '#DenLED', '#TựLàm'],
    likes: 0,
    comments: 0,
    views: 0
  }
];

// Dữ liệu xếp hạng
const rankingData = [
  { id: 1, user: 'Hoàng Hải', avatar: 'https://i.pravatar.cc/150?img=3', likes: 2241 },
  { id: 2, user: 'Kelly', avatar: 'https://i.pravatar.cc/150?img=4', likes: 1995 },
  { id: 3, user: 'Ân', avatar: 'https://i.pravatar.cc/150?img=6', likes: 1921 },
  { id: 4, user: 'Nguyen Phuc', avatar: 'https://i.pravatar.cc/150?img=7', likes: 1784 },
  { id: 5, user: 'An', avatar: 'https://i.pravatar.cc/150?img=8', likes: 1678 },
  { id: 6, user: 'Thu Lê', avatar: 'https://i.pravatar.cc/150?img=9', likes: 1565 },
  { id: 7, user: 'Uyển Nhi', avatar: 'https://i.pravatar.cc/150?img=10', likes: 1555 },
  { id: 8, user: 'Nhã Nhi', avatar: 'https://i.pravatar.cc/150?img=11', likes: 1503 },
  { id: 9, user: 'Quốc Cường', avatar: 'https://i.pravatar.cc/150?img=12', likes: 1211 },
  { id: 10, user: 'Hàn Hân', avatar: 'https://i.pravatar.cc/150?img=13', likes: 841 }
];

// Dữ liệu hashtag phổ biến
const popularHashtags = [
  '#decor',
  '#nội_thất',
  '#phòngngủnhỏ',
  '#meotrângtri',
  '#đènLED',
  '#giường ngủ',
  '#giấydántường',
  '#tốiưukhônggian',
  '#DIY'
];

const ChungNhip = () => {
  const [activeTab, setActiveTab] = useState('newest');



  // Hàm trả về icon huy chương cho top 3
  const getRankBadge = (rank) => {
    if (rank === 0) return <FontAwesomeIcon icon={faCrown} className='text-dexin-gold'/>; // Crown for 1st place
    if (rank === 1) return '🥈';
    if (rank === 2) return '🥉';
    return null;
  };

  // Hàm trả về class cho các hạng top
  const getTopRankingStyles = (rank) => {
    switch(rank) {
      case 0:
        return 'bg-white border border-pink-100 rounded-lg shadow-sm mb-3'; // Top 1
      case 1:
        return 'bg-white border border-pink-100 rounded-lg mb-3'; // Top 2
      case 2:
        return 'bg-white border border-pink-100 rounded-lg mb-3'; // Top 3
      default:
        return ''; // Các vị trí khác
    }
  };

  // Hàm trả về style cho tên người dùng
  const getUserNameStyle = (rank) => {
    switch(rank) {
      case 0: return 'font-bold text-gray-900'; // Top 1
      case 1: return 'font-bold text-gray-900';  // Top 2
      case 2: return 'font-bold text-gray-900'; // Top 3
      default: return 'font-medium text-gray-800'; // Các vị trí khác
    }
  };

  // Hàm trả về style cho số lượt thích
  const getLikesStyle = (rank) => {
    switch(rank) {
      case 0: return 'text-gray-800 font-medium'; // Top 1
      case 1: return 'text-gray-800 font-medium';  // Top 2
      case 2: return 'text-gray-800 font-medium'; // Top 3
      default: return 'text-gray-500 text-sm'; // Các vị trí khác
    }
  };

  return (
    <>
      <SEOHead
        title={pagesSEO.chungnhip.title}
        description={pagesSEO.chungnhip.description}
        keywords={pagesSEO.chungnhip.keywords}
        url={pagesSEO.chungnhip.url}
      />
      <motion.div
        className="bg-white min-h-screen pb-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.4 }}
      >
      {/* Hero Section */}
      <motion.div
        className="pt-10 pb-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex flex-col lg:flex-row items-center">
            {/* Left Side - Text and Search */}
            <motion.div
              className="w-full ml-4 sm:ml-6 lg:ml-10 lg:w-1/2 lg:pr-1"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="mb-10">
                <motion.h1
                  className="text-2xl md:text-4xl font-bold text-dexin-primary mb-4"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  Rất vui được đồng hành cùng bạn!
                </motion.h1>
                <motion.h2
                  className="text-3xl md:text-4xl font-bold text-dexin-primary"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  Bạn đang tìm ý tưởng gì hôm nay?
                </motion.h2>
              </div>

              {/* Search Box */}
              <motion.div
                className="w-full bg-pink-50 p-4 rounded-3xl shadow-sm"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                whileHover={{ boxShadow: "0 4px 12px rgba(254, 124, 171, 0.2)" }}
              >
                <div className="flex">
                  <div className="relative w-full mr-2">
                    <motion.input
                      whileFocus={{ scale: 1.01 }}
                      type="text"
                      placeholder="Tìm kiếm..."
                      className="w-full pl-12 pr-4 py-3 bg-white rounded-full border-2 border-pink-100 focus:outline-none focus:border-dexin-primary"
                    />
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  </div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button variant="bolded" size="md">Tạo chủ đề</Button>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Side - Image */}
            <div className="w-full lg:w-1/2 mt-8 lg:mt-0 flex justify-center">
              <img src="/images/chungnhip.png" alt="Community Illustration" className="max-w-full h-auto" />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content Area */}
      <motion.div
        className="container mx-auto px-4 sm:px-6"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="flex flex-col lg:flex-row gap-4 sm:gap-6 lg:gap-8">
          {/* Left Side - Posts */}
          <div className="w-full lg:w-3/4">
            {/* Tabs */}
            <div className="mb-6 border-b border-pink-200">
              <div className="flex">
                <motion.button
                  onClick={() => setActiveTab('newest')}
                  className={`py-3 px-6 font-medium ${
                    activeTab === 'newest'
                      ? 'text-dexin-primary border-b-2 border-dexin-primary'
                      : 'text-gray-600 hover:text-dexin-primary'
                  }`}
                  whileHover={{ y: -2 }}
                  whileTap={{ y: 0 }}
                >
                  Bài Viết Mới Nhất
                </motion.button>
                <motion.button
                  onClick={() => setActiveTab('hot')}
                  className={`py-3 px-6 font-medium ${
                    activeTab === 'hot'
                      ? 'text-dexin-primary border-b-2 border-dexin-primary'
                      : 'text-gray-600 hover:text-dexin-primary'
                  }`}
                  whileHover={{ y: -2 }}
                  whileTap={{ y: 0 }}
                >
                  Bài Viết Nổi Bật
                </motion.button>
              </div>
            </div>

            {/* Posts List */}
            <div className="space-y-6">
              {postData.map((post, index) => (
                <motion.div
                  key={post.id}
                  className="bg-white rounded-xl shadow-sm overflow-hidden border border-pink-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                  whileHover={{
                    y: -5,
                    boxShadow: "0 10px 20px rgba(254, 124, 171, 0.1)"
                  }}
                >
                  <div className="p-6">
                    {/* Post Header */}
                    <div className="flex items-center mb-4">
                      <motion.img
                        src={post.avatar}
                        alt={post.author}
                        className="w-10 h-10 rounded-full mr-3"
                        whileHover={{ scale: 1.1 }}
                      />
                      <div>
                        <h3 className="font-medium text-gray-800">{post.author}</h3>
                        <p className="text-sm text-gray-500">{post.time}</p>
                      </div>
                    </div>

                    {/* Post Content */}
                    <h2 className="text-xl font-bold mb-3 text-gray-800">{post.title}</h2>
                    <p className="text-gray-600 mb-4">{post.content}</p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {post.tags.map((tag, idx) => (
                        <motion.span
                          key={idx}
                          className="inline-block bg-pink-50 text-gray-600 text-sm px-3 py-1 rounded-full"
                          whileHover={{
                            backgroundColor: "rgba(254, 124, 171, 0.2)",
                            scale: 1.05
                          }}
                        >
                          {tag}
                        </motion.span>
                      ))}
                    </div>

                    {/* Post Actions */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-6">
                        <motion.button
                          className="flex items-center space-x-1 text-gray-500 hover:text-dexin-primary"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <ThumbsUp size={18} />
                          <span>{post.likes || ''}</span>
                        </motion.button>
                        <motion.button
                          className="flex items-center space-x-1 text-gray-500 hover:text-dexin-primary"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <MessageSquare size={18} />
                          <span>{post.comments || ''}</span>
                        </motion.button>
                        <motion.button
                          className="flex items-center space-x-1 text-gray-500 hover:text-dexin-primary"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Heart size={18} />
                          <span>{post.views || ''}</span>
                        </motion.button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Right Side - Rankings and Hashtags */}
          <div className="w-full lg:w-1/4 space-y-4 sm:space-y-6">
            {/* Bảng Xếp Hạng */}
            <motion.div
              className="bg-white rounded-xl shadow-sm overflow-hidden border border-pink-100 p-3 sm:p-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              whileHover={{
                boxShadow: "0 8px 16px rgba(254, 124, 171, 0.15)"
              }}
            >
              <h3 className="font-bold text-lg sm:text-xl text-[#de1e54] mb-4 sm:mb-6">Bảng Xếp Hạng</h3>

              <div className="space-y-0">
                {rankingData.map((item, index) => (
                  <motion.div
                    key={item.id}
                    className={`py-2 sm:py-3 ${getTopRankingStyles(index)}`}
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 + index * 0.03 }}
                    whileHover={{
                      backgroundColor: "rgba(254, 124, 171, 0.05)",
                      x: 5
                    }}
                  >
                    <div className="flex items-center justify-between px-1 sm:px-2 min-h-[2.5rem]">
                      <div className="flex items-center flex-1 min-w-0 mr-2">
                        <span className={`w-4 sm:w-6 text-center font-bold text-base sm:text-xl flex-shrink-0 ${index === 0 ? 'text-dexin-gold' : index === 1 ? 'text-dexin-silver' : index === 2 ? 'text-dexin-bronze' : 'text-gray-500'}`}>
                          {index + 1}
                        </span>
                        {getRankBadge(index) && (
                          <span className="ml-1 mr-1 sm:mr-2 flex-shrink-0">{getRankBadge(index)}</span>
                        )}
                        <motion.img
                          src={item.avatar}
                          alt={item.user}
                          className="w-6 h-6 sm:w-8 sm:h-8 rounded-full mx-1 sm:mx-2 flex-shrink-0"
                          whileHover={{ scale: 1.15 }}
                        />
                        <span className={`${getUserNameStyle(index)} truncate text-sm sm:text-base`}>
                          {item.user}
                        </span>
                      </div>
                      <div className="flex items-center flex-shrink-0">
                        <span className={`${getLikesStyle(index)} text-xs sm:text-sm whitespace-nowrap`}>
                          <span className="hidden sm:inline">{item.likes.toLocaleString()} Lượt Thích</span>
                          <span className="sm:hidden">{item.likes.toLocaleString()}</span>
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Hashtags */}
            <motion.div
              className="bg-white rounded-xl shadow-sm overflow-hidden border border-dexin-light-20 p-3 sm:p-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{
                boxShadow: "0 8px 16px rgba(254, 124, 171, 0.15)"
              }}
            >
              <div className="flex items-center mb-3 sm:mb-4">
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className="mr-2 flex-shrink-0"
                >
                  <Heart className="w-4 h-4 sm:w-5 sm:h-5 text-dexin-primary" />
                </motion.div>
                <h3 className="font-bold text-lg sm:text-xl text-dexin-primary">Hashtag</h3>
              </div>

              <div className="flex flex-wrap gap-1.5 sm:gap-2">
                {popularHashtags.map((tag, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.2 + index * 0.04 }}
                    className="flex-shrink-0"
                  >
                    <Link
                      to={`/chung-nhip/tag/${tag.replace('#', '')}`}
                      className="inline-block bg-pink-50 text-gray-600 hover:bg-pink-100 hover:text-dexin-primary text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-full transition-colors duration-300 whitespace-nowrap"
                    >
                      <motion.span
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="block"
                      >
                        {tag}
                      </motion.span>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>

          </div>
        </div>
      </motion.div>
      </motion.div>
    </>
  );
};

export default ChungNhip;