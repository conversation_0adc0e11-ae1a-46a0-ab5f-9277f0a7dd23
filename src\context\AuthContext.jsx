import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { authService } from '../services/authService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Constants
  const STORAGE_KEY = 'dexin_user';
  const TOKEN_KEY = 'dexin_token';

  // Helper function để lưu user và token vào localStorage
  const saveUserToStorage = useCallback((userData) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(userData));
      // Lưu token riêng để dễ quản lý
      if (userData.token) {
        localStorage.setItem(TOKEN_KEY, userData.token);
      }
    } catch (error) {
      console.error('Error saving user to localStorage:', error);
    }
  }, []);

  // Helper function để xóa user và token khỏi localStorage
  const removeUserFromStorage = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(TOKEN_KEY);
    } catch (error) {
      console.error('Error removing user from localStorage:', error);
    }
  }, []);

  // Khởi tạo - kiểm tra localStorage và token
  useEffect(() => {
    const initAuth = async () => {
      try {
        setError(null);
        const savedUser = localStorage.getItem(STORAGE_KEY);
        const savedToken = localStorage.getItem(TOKEN_KEY);

        if (savedUser && savedToken) {
          const userData = JSON.parse(savedUser);

          // Validate user data structure và token
          if (userData && userData.id && userData.email && userData.token === savedToken) {
            // Kiểm tra token expiry
            try {
              const tokenPayload = JSON.parse(atob(savedToken.split('.')[1]));
              const currentTime = Math.floor(Date.now() / 1000);

              if (tokenPayload.exp && tokenPayload.exp > currentTime) {
                // Token còn hạn, set user
                setUser(userData);
              } else {
                // Token hết hạn, xóa và yêu cầu đăng nhập lại
                console.warn('Token expired, removing auth data...');
                removeUserFromStorage();
              }
            } catch (error) {
              console.warn('Invalid token format, removing auth data...');
              removeUserFromStorage();
            }
          } else {
            console.warn('Invalid user data or token mismatch in localStorage, removing...');
            removeUserFromStorage();
          }
        } else if (savedUser || savedToken) {
          // Nếu chỉ có một trong hai thì xóa cả hai để đảm bảo consistency
          console.warn('Incomplete auth data in localStorage, removing...');
          removeUserFromStorage();
        }
      } catch (error) {
        console.error('Error loading user from localStorage:', error);
        setError('Có lỗi khi tải thông tin người dùng');
        removeUserFromStorage();
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, [removeUserFromStorage]);

  // Đăng nhập
  const login = useCallback(async (loginData) => {
    try {
      setLoading(true);
      setError(null);

      // Validate input - sử dụng identifier thay vì username
      if (!loginData?.identifier?.trim() || !loginData?.password) {
        return {
          success: false,
          message: 'Vui lòng nhập đầy đủ thông tin đăng nhập'
        };
      }

      const result = await authService.login({
        identifier: loginData.identifier.trim(),
        password: loginData.password
      });

      if (result.success && result.user) {
        // Lấy thông tin chi tiết user từ API
        try {
          const userDetailResult = await authService.getUserDetailById(result.user.id);
          if (userDetailResult.success && userDetailResult.user) {
            // Merge token vào user detail
            const completeUser = {
              ...userDetailResult.user,
              token: result.user.token
            };
            setUser(completeUser);
            saveUserToStorage(completeUser);
          } else {
            // Fallback về user từ token nếu không lấy được detail
            setUser(result.user);
            saveUserToStorage(result.user);
          }
        } catch (error) {
          console.warn('Could not fetch user details, using token data:', error);
          setUser(result.user);
          saveUserToStorage(result.user);
        }

        setError(null);
        return { success: true, message: result.message, user: result.user };
      } else {
        setError(result.message);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = 'Có lỗi xảy ra khi đăng nhập';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [saveUserToStorage]);

  // Đăng ký
  const register = useCallback(async (userData) => {
    try {
      setLoading(true);
      setError(null);
      
      // Validate input
      if (!userData?.firstName?.trim() || !userData?.lastName?.trim()) {
        return { success: false, message: 'Vui lòng nhập đầy đủ họ tên' };
      }
      if (!userData?.email?.trim() || !userData?.username?.trim()) {
        return { success: false, message: 'Vui lòng nhập email và tên người dùng' };
      }
      if (!userData?.password || userData.password.length < 6) {
        return { success: false, message: 'Mật khẩu phải có ít nhất 6 ký tự' };
      }

      // Tạm thời disable check exists để tránh multiple API calls
      // Để server handle duplicate validation
      const result = await authService.register(userData);

      if (result.success && result.user) {
        // Đăng ký thành công, nhưng không cần lấy detail vì user mới chưa có đầy đủ thông tin
        // Chỉ lưu thông tin cơ bản từ registration
        setUser(result.user);
        saveUserToStorage(result.user);
        setError(null);
        return { success: true, message: result.message };
      } else {
        setError(result.message);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Register error:', error);
      const errorMessage = 'Có lỗi xảy ra khi đăng ký';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [saveUserToStorage]);

  // Đăng xuất
  const logout = useCallback(async () => {
    try {
      // Gọi API logout
      await authService.logout();
    } catch (error) {
      console.warn('Logout API error:', error);
    } finally {
      // Luôn clear local data dù API có lỗi
      setUser(null);
      setError(null);
      removeUserFromStorage();
    }
  }, [removeUserFromStorage]);

  // Cập nhật thông tin user với API
  const updateUser = useCallback(async (updatedUserData) => {
    try {
      if (!user?.id) {
        const errorMessage = 'Không tìm thấy thông tin người dùng';
        setError(errorMessage);
        return { success: false, message: errorMessage };
      }

      setLoading(true);
      setError(null);

      // Gọi API để cập nhật trên server
      const result = await authService.updateUser(user.id, {
        ...user,
        ...updatedUserData
      });

      if (result.success && result.user) {
        // Cập nhật state và localStorage với dữ liệu mới từ server
        setUser(result.user);
        saveUserToStorage(result.user);
        setError(null);
        return { success: true, message: result.message };
      } else {
        setError(result.message);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Update user error:', error);
      const errorMessage = error.message || 'Có lỗi xảy ra khi cập nhật thông tin';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [user, saveUserToStorage]);

  // Cập nhật thông tin user chỉ local (không gọi API)
  const updateUserLocal = useCallback((updatedUserData) => {
    if (!user) return;
    
    const updatedUser = { ...user, ...updatedUserData };
    setUser(updatedUser);
    saveUserToStorage(updatedUser);
    setError(null);
  }, [user, saveUserToStorage]);

  // Làm mới thông tin user từ server
  const refreshUser = useCallback(async () => {
    try {
      if (!user?.id) {
        return { success: false, message: 'Không tìm thấy thông tin người dùng' };
      }

      setLoading(true);
      setError(null);

      const result = await authService.getUserDetailById(user.id);

      if (result.success && result.user) {
        // Merge token vào user detail
        const completeUser = {
          ...result.user,
          token: user.token
        };
        setUser(completeUser);
        saveUserToStorage(completeUser);
        setError(null);
        return { success: true, user: completeUser };
      } else {
        setError(result.message);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      const errorMessage = 'Có lỗi xảy ra khi làm mới thông tin người dùng';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [user?.id, user?.token, saveUserToStorage]);

  // Thay đổi mật khẩu
  const changePassword = useCallback(async (currentPassword, newPassword) => {
    try {
      if (!user?.id) {
        const errorMessage = 'Không tìm thấy thông tin người dùng';
        setError(errorMessage);
        return { success: false, message: errorMessage };
      }

      if (!currentPassword?.trim() || !newPassword?.trim()) {
        return { success: false, message: 'Vui lòng nhập đầy đủ mật khẩu' };
      }

      if (newPassword.length < 6) {
        return { success: false, message: 'Mật khẩu mới phải có ít nhất 6 ký tự' };
      }

      setLoading(true);
      setError(null);
      
      const result = await authService.changePassword(user.id, currentPassword, newPassword);
      
      if (!result.success) {
        setError(result.message);
      }
      
      return result;
    } catch (error) {
      console.error('Change password error:', error);
      const errorMessage = 'Có lỗi xảy ra khi đổi mật khẩu';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Upload avatar
  const uploadAvatar = useCallback(async (avatarFile) => {
    try {
      if (!user?.id) {
        const errorMessage = 'Không tìm thấy thông tin người dùng';
        setError(errorMessage);
        return { success: false, message: errorMessage };
      }

      setLoading(true);
      setError(null);

      console.log('🔄 Starting avatar upload for user:', user.id);

      // Gọi API upload avatar
      const result = await authService.uploadAvatar(user.id, avatarFile);

      if (result.success) {
        console.log('✅ Avatar upload successful, refreshing user data...');

        // Refresh user data để lấy avatar mới
        try {
          const refreshResult = await authService.getUserDetailById(user.id);
          if (refreshResult.success && refreshResult.user) {
            // Merge token vào user detail mới
            const completeUser = {
              ...refreshResult.user,
              token: user.token
            };

            console.log('✅ User data refreshed with new avatar:', completeUser.avatar);

            setUser(completeUser);
            saveUserToStorage(completeUser);
            setError(null);

            return { success: true, message: result.message };
          } else {
            console.warn('⚠️ Could not refresh user data after upload');
            return { success: true, message: result.message };
          }
        } catch (refreshError) {
          console.warn('⚠️ Refresh error after upload:', refreshError);
          return { success: true, message: result.message };
        }
      } else {
        setError(result.message);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Upload avatar error:', error);
      const errorMessage = error.message || 'Có lỗi xảy ra khi upload avatar';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [user?.id, user?.token, saveUserToStorage]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Computed values với useMemo để tối ưu performance
  const authValues = useMemo(() => ({
    // State
    user,
    loading,
    error,
    
    // Auth methods
    login,
    register,
    logout,
    updateUser,
    updateUserLocal,
    refreshUser,
    changePassword,
    uploadAvatar,
    clearError,
    
    // Computed helpers
    isAuthenticated: Boolean(user),
    getDisplayName: () => {
      if (!user) return '';
      return `${user.firstName || ''} ${user.lastName || ''}`.trim() || 
             user.userName || 
             user.email || 
             'Người dùng';
    },
    getAvatarUrl: () => {
      // Chỉ return avatar URL nếu có và không phải default
      if (user?.avatar && user.avatar !== 'default_avatar.png' && user.avatar !== '') {
        // Thêm timestamp để force refresh image cache
        const url = user.avatar;
        if (url.includes('cloudinary.com') || url.includes('http')) {
          return `${url}?t=${Date.now()}`;
        }
        return url;
      }
      // Return undefined để Avatar component tự fallback
      return undefined;
    },
    
    // Role helpers - hỗ trợ cả format cũ và mới
    hasRole: (role) => {
      const userRole = user?.role?.toLowerCase();
      const checkRole = role?.toLowerCase();
      return userRole === checkRole;
    },
    hasAnyRole: (roles) => {
      if (!Array.isArray(roles)) return false;
      const userRole = user?.role?.toLowerCase();
      return roles.some(role => role?.toLowerCase() === userRole);
    },
    isAdmin: () => {
      const role = user?.role?.toLowerCase();
      return role === 'admin';
    },
    isUser: () => {
      const role = user?.role?.toLowerCase();
      return role === 'user';
    },
    isStaff: () => {
      const role = user?.role?.toLowerCase();
      return role === 'staff';
    }
  }), [
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateUser,
    updateUserLocal,
    refreshUser,
    changePassword,
    uploadAvatar,
    clearError
  ]);

  return (
    <AuthContext.Provider value={authValues}>
      {children}
    </AuthContext.Provider>
  );
}; 