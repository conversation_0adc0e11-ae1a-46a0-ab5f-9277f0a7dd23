#!/usr/bin/env node

/**
 * Lightweight Cache Clear Script
 * Gi<PERSON>i quyết vấn đề MIME type error mà không ảnh hưởng performance
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import crypto from 'crypto';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const args = process.argv.slice(2);
const autoVersion = args.includes('--auto-version');
const forceRefresh = args.includes('--force-refresh');

// Tạo version mới lightweight
function generateNewVersion() {
  const timestamp = Date.now();
  return {
    version: `1.0.${Math.floor(timestamp / 1000)}`,
    buildId: timestamp.toString(),
    buildDate: new Date().toISOString(),
    gitCommit: getGitCommit(),
    environment: process.env.NODE_ENV || 'development',
    hash: crypto.randomBytes(4).toString('hex')
  };
}

// Lấy git commit hash
function getGitCommit() {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
  } catch (error) {
    return 'unknown';
  }
}

// Update version.json
function updateVersionFile(versionData) {
  const versionPath = path.join(__dirname, 'public', 'version.json');
  fs.writeFileSync(versionPath, JSON.stringify(versionData, null, 2));
  return versionData;
}

// Hàm xóa thư mục lightweight
function removeDir(dirPath) {
  const fullPath = path.resolve(dirPath);
  if (fs.existsSync(fullPath)) {
    try {
      fs.rmSync(fullPath, { recursive: true, force: true });
    } catch (error) {
      // Silent fail
    }
  }
}

// Hàm xóa file
function removeFile(filePath) {
  const fullPath = path.resolve(filePath);
  if (fs.existsSync(fullPath)) {
    try {
      fs.unlinkSync(fullPath);
    } catch (error) {
      // Silent fail
    }
  }
}

// Main cleanup function
async function performCleanup() {
  try {
    console.log('🧹 Cache Clear - Starting...\n');

    // 1. Generate new version nếu cần
    let versionData;
    if (autoVersion) {
      versionData = generateNewVersion();
      updateVersionFile(versionData);
      console.log(`✅ Version updated: ${versionData.version}\n`);
    }

    // 2. Xóa các thư mục build
    removeDir('build');
    removeDir('dist');
    removeDir('.next');
    
    // 3. Xóa Vite cache
    removeDir('node_modules/.vite');
    removeDir('node_modules/.cache');
    
    // 4. Xóa các file cache khác
    removeFile('.eslintcache');
    removeFile('tsconfig.tsbuildinfo');
    removeFile('.parcel-cache');
    
    // 5. Clear npm cache (optional)
    if (forceRefresh) {
      try {
        execSync('npm cache clean --force', { stdio: 'inherit' });
      } catch (error) {
        // Silent fail
      }
    }
    
    console.log('🔧 Building...\n');
    
    // 6. Rebuild project
    execSync('npm run build', { stdio: 'inherit' });
    
    console.log('\n🎉 Cache clear completed!');
    
    if (versionData) {
      console.log(`📊 New version: ${versionData.version}`);
    }
    
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    process.exit(1);
  }
}

// Show help
function showHelp() {
  console.log(`
🧹 Lightweight Cache Clear Script

Usage: node clear-cache.js [options]

Options:
  --auto-version    Auto generate new version
  --force-refresh   Aggressive cleanup (includes npm cache)
  --help           Show this help

Examples:
  node clear-cache.js                    # Basic cache clear
  node clear-cache.js --auto-version     # Clear + new version
  node clear-cache.js --force-refresh    # Aggressive clear
  `);
}

// Run script
if (args.includes('--help') || args.includes('-h')) {
  showHelp();
} else {
  performCleanup();
} 