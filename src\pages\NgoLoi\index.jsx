import React, { useState, useCallback } from 'react';
import { motion } from 'motion/react';
import {
  ChatSidebar,
  ChatInput,
  MessagesList,
  ChatDetail,
  EmptyChat,
  ChatList
} from '../../components/chat';

const NgoLoi = () => {
  const [chatState, setChatState] = useState('empty'); // empty, list, detail
  const [selectedUser, setSelectedUser] = useState(null);

  // Mock data cho danh sách hội thoại
  const [conversations, setConversations] = useState([
    {
      id: 1,
      name: 'Tư vấn viên Thiên <PERSON>',
      avatar: "/images/Avatar.png",
      lastMessage: 'Hoàn toàn được nha! Khi đặt dịch vụ, bạn có thể gửi yêu cầu chi tiết...',
      time: 'Thứ sáu 18:08',
      status: 'Đang hoạt động'
    },
  ]);

  const [messages, setMessages] = useState([
    {
      id: 1,
      sender: 'staff',
      text: 'Chào bạn! 💚 <PERSON><PERSON><PERSON> là <PERSON>hiê<PERSON>, từ DEXIN đây. <PERSON><PERSON><PERSON> thấy bạn vừa trải nghiệm sketch 2D, không biết bạn có cần hỗ trợ gì thêm không nè? 🤗',
      time: 'Thứ sáu 18:04'
    },
    {
      id: 2,
      sender: 'user',
      text: 'Chào bạn, mình vừa thử xong bản sketch 2D, nhìn cũng ổn nhưng vẫn hơi khó hình dung tổng thể... DEXIN có hỗ trợ lên bản vẽ 3D không nhỉ?',
      time: 'Thứ sáu 18:05'
    },
    {
      id: 3,
      sender: 'staff',
      text: 'Chào bạn! DEXIN có dịch vụ thiết kế 3D cá nhân hóa nhé. Bạn mình sẽ dùng không gian theo bản sketch 2D của bạn, bổ sung chất liệu, ánh sáng, và màu sắc để bạn có cái nhìn chân thực hơn.',
      time: 'Thứ sáu 18:05'
    },
    {
      id: 4,
      sender: 'user',
      text: 'Nghe ổn nha! 😃 Mà nếu mình có ý tưởng decor riêng, kiểu như muốn tường có hoa văn đặc biệt hoặc giường phải đúng kiểu low-bed, thì bên bạn có làm theo không?',
      time: 'Thứ sáu 18:08'
    },
    {
      id: 5,
      sender: 'staff',
      text: 'Hoàn toàn được nha! Khi đặt dịch vụ, bạn có thể gửi yêu cầu chi tiết về phong cách, màu sắc, nội thất mong muốn. Bọn mình sẽ đảm bảo thiết kế 3D đúng với sở thích của bạn nhất có thể.',
      time: 'Thứ sáu 18:08'
    }
  ]);

  // Chuyển sang chế độ xem danh sách chat
  const showChatList = useCallback(() => {
    setChatState('list');
  }, []);

  // Chuyển sang chế độ xem chi tiết chat
  const showChatDetail = useCallback((conversation) => {
    setSelectedUser(conversation);
    setChatState('detail');
  }, []);

  // Xử lý khi click vào icon tư vấn viên
  const handleAdvisorIconClick = useCallback(() => {
    if (chatState === 'empty') {
      // Nếu chưa có tin nhắn, chuyển sang trạng thái danh sách
      showChatList();
    } else {
      // Nếu đã có tin nhắn, chuyển sang trạng thái chi tiết
      showChatDetail(conversations[0]);
    }
  }, [chatState, conversations, showChatList, showChatDetail]);

  // Trở lại màn hình trước đó
  const goBack = useCallback(() => {
    if (chatState === 'detail') {
      setChatState('list');
    } else if (chatState === 'list') {
      setChatState('empty');
    }
  }, [chatState]);

  // Xử lý gửi tin nhắn từ component ChatInput
  const handleSendMessage = useCallback((messageData) => {
    let messageToAdd;

    // Xử lý theo loại message
    if (typeof messageData === 'string') {
      // Backward compatibility cho text message cũ
      messageToAdd = {
        id: Date.now(),
        sender: 'user',
        text: messageData,
        time: 'Vừa xong',
        type: 'text'
      };
    } else {
      // Xử lý message object mới từ ChatInput
      switch (messageData.type) {
        case 'text':
          messageToAdd = {
            id: Date.now(),
            sender: 'user',
            text: messageData.content,
            time: 'Vừa xong',
            type: 'text'
          };
          break;

        case 'files':
          messageToAdd = {
            id: Date.now(),
            sender: 'user',
            text: `📎 Đã gửi ${messageData.content.length} file`,
            time: 'Vừa xong',
            type: 'files',
            files: messageData.content
          };
          break;

        case 'images':
          messageToAdd = {
            id: Date.now(),
            sender: 'user',
            text: `🖼️ Đã gửi ${messageData.content.length} hình ảnh`,
            time: 'Vừa xong',
            type: 'images',
            images: messageData.content
          };
          break;

        default:
          messageToAdd = {
            id: Date.now(),
            sender: 'user',
            text: 'Tin nhắn không xác định',
            time: 'Vừa xong',
            type: 'unknown'
          };
      }
    }

    setMessages(prevMessages => [...prevMessages, messageToAdd]);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="min-h-screen bg-gray-50"
    >
      {chatState === 'empty' && (
        <EmptyChat showChatList={showChatList} />
      )}

      {chatState === 'list' && (
        <ChatList
          conversations={conversations}
          selectedUser={selectedUser}
          showChatDetail={showChatDetail}
          handleAdvisorIconClick={handleAdvisorIconClick}
        />
      )}

      {chatState === 'detail' && (
        <ChatDetail
          conversations={conversations}
          selectedUser={selectedUser}
          messages={messages}
          onSendMessage={handleSendMessage}
          showChatDetail={showChatDetail}
          handleAdvisorIconClick={handleAdvisorIconClick}
          goBack={goBack}
        />
      )}
    </motion.div>
  );
};

export default NgoLoi;