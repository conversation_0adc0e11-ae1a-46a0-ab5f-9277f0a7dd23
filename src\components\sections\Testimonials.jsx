import React from 'react';
import { Twitter, Linkedin, Globe } from 'lucide-react';

const Testimonials = () => {
  const teamMembers = [
    {
      name: '<PERSON><PERSON>',
      role: 'Founder & CEO',
      description: 'Former co-founder of Opendoor. Early staff at Spotify and Clearbit.',
    },
    {
      name: '<PERSON><PERSON> <PERSON>',
      role: 'Engineering Manager',
      description: 'Lead engineering teams at Figma, Pitch, and Protocol Labs.',
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      role: 'Product Manager',
      description: 'Former PM for Linear, Lambda School, and On Deck.',
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      role: 'Designer',
      description: 'Founding design team at Figma. Former Pleo, Stripe, and Tile.',
    },
    {
      name: '<PERSON><PERSON> <PERSON>',
      role: 'Backend Developer',
      description: 'Lead backend dev at Clearbit. Former Clearbit and Loom.',
    },
    {
      name: '<PERSON>',
      role: 'Frontend Developer',
      description: 'Former frontend dev for Linear, Coinbase, and Postscript.',
    },
  ];

  return (
    <>
      <section className="py-10 sm:py-16 md:py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex flex-col lg:flex-row gap-8 sm:gap-12 items-center">
            <div className="w-full lg:w-1/2 text-base sm:text-lg">
              <h3 className="text-dexin-primary font-semibold mb-3 sm:mb-4 text-center lg:text-left">Câu chuyện DEXIN</h3>
              <p className="text-gray-800 mb-6 sm:mb-8 leading-relaxed text-center lg:text-left">
                DEXIN là sự kết hợp tinh tế giữa Deco (Trang trí), Ex (External) và In (Internal), mang đến một không gian hài hòa giữa vẻ đẹp ngoại cảnh và cảm xúc nội tâm. Được lấy cảm hứng từ quá trình Inhale-Exhale, DEXIN không chỉ là sự hoàn hảo trong thiết kế mà còn là sự thu giãn sâu trong tâm hồn. Không gian DEXIN mang đến cảm giác thoải mái, yên bình, như một hơi thở nhẹ nhàng, giúp con người kết nối với bản thân và tìm thấy sự cân bằng giữa vẻ đẹp bên ngoài và bình an bên trong.
              </p>
            </div>

            <div className="w-full lg:w-1/2 ml-0">
              <div className="overflow-hidden rounded-2xl sm:rounded-[40px]">
                <img
                  src="/images/spacejoy-3z_61bnbFhM-unsplash 2.png"
                  alt="DEXIN Interior Design"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-10 sm:py-16 md:py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex flex-col lg:flex-row gap-8 sm:gap-12 items-center lg:items-start">
            <div className="w-full lg:w-1/3 order-2 lg:order-1">
              <div className="mt-0 lg:mt-8 flex justify-center lg:justify-start">
                <img
                  src="/images/Group 1707480559.png"
                  alt="Team Illustration"
                  className="max-w-full h-auto w-3/4 sm:w-1/2 lg:w-full"
                />
              </div>
            </div>

            <div className="w-full lg:w-2/3 order-1 lg:order-2">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 sm:gap-x-8 gap-y-8 sm:gap-y-12">
                {teamMembers.map((member, index) => (
                  <div key={index} className="text-center">
                    <div className="mb-3 sm:mb-4 overflow-hidden rounded-full w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 mx-auto bg-dexin-light-20 flex items-center justify-center">
                      <div className="text-xl sm:text-2xl font-bold text-dexin-primary">
                        {member.name.split(' ')[0].charAt(0) + member.name.split(' ')[1].charAt(0)}
                      </div>
                    </div>
                    <h4 className="font-bold text-gray-900 mb-1">{member.name}</h4>
                    <p className="text-dexin-primary mb-2">{member.role}</p>
                    <p className="text-gray-600 text-xs sm:text-sm mb-3 sm:mb-4">{member.description}</p>
                    <div className="flex justify-center space-x-3">
                      <a href="#" className="text-gray-400 hover:text-gray-500">
                        <Twitter size={14} className="sm:w-4 sm:h-4 md:w-4 md:h-4" />
                      </a>
                      <a href="#" className="text-gray-400 hover:text-gray-500">
                        <Linkedin size={14} className="sm:w-4 sm:h-4 md:w-4 md:h-4" />
                      </a>
                      <a href="#" className="text-gray-400 hover:text-gray-500">
                        <Globe size={14} className="sm:w-4 sm:h-4 md:w-4 md:h-4" />
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Testimonials;