import React, { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { Phone, Video, ChevronLeft, MoreVertical } from 'lucide-react';
import { ChatSidebar } from './index';
import EnhancedMessagesList from './EnhancedMessagesList';
import ChatInput from './ChatInput';

const ChatDetail = React.memo(({
  conversations,
  selectedUser,
  messages,
  onSendMessage,
  showChatDetail,
  handleAdvisorIconClick,
  goBack
}) => {
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(false);
  const [lastMessageCount, setLastMessageCount] = useState(messages.length);

  // Track when new messages are added to trigger auto-scroll
  useEffect(() => {
    if (messages.length > lastMessageCount) {
      // New message(s) added, trigger auto-scroll
      setShouldScrollToBottom(true);
      setLastMessageCount(messages.length);

      // Reset scroll trigger after a short delay
      const timer = setTimeout(() => {
        setShouldScrollToBottom(false);
      }, 100);

      return () => clearTimeout(timer);
    } else {
      setLastMessageCount(messages.length);
    }
  }, [messages.length, lastMessageCount]);

  // Enhanced onSendMessage wrapper to ensure auto-scroll
  const handleSendMessage = (messageData) => {
    onSendMessage(messageData);
    // Force scroll to bottom when user sends a message
    setShouldScrollToBottom(true);
  };

  return (
  <div className="flex h-screen bg-white">
    {/* Sidebar bên trái với danh sách chat - chỉ hiển thị trên màn hình lớn */}
    <div className="hidden sm:block border-r border-gray-100 h-full">
      <ChatSidebar
        isEmpty={false}
        conversations={conversations}
        selectedConversation={selectedUser}
        onSelectConversation={showChatDetail}
        onStartChat={handleAdvisorIconClick}
        className="w-full sm:w-80 md:w-96 h-full"
      />
    </div>

    {/* Phần nội dung chat chi tiết bên phải */}
    <motion.div
      className="flex-grow flex flex-col"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header của chat */}
      <motion.div
        className="flex items-center justify-between px-3 sm:px-6 py-3 sm:py-4 border-b border-dexin-light-50 bg-white shadow-sm"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <motion.button
          onClick={goBack}
          className="text-dexin-light hover:text-dexin-primary transition-colors sm:hidden mr-2"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <ChevronLeft className="h-5 w-5 sm:h-6 sm:w-6" />
        </motion.button>
        <div className="flex-1 flex items-center">
          <motion.div
            className="relative mr-2 sm:mr-4 flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <motion.img
              src="/images/Avatar.png"
              alt={selectedUser?.name}
              className="w-full h-full rounded-full shadow-sm object-cover"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            />
          </motion.div>
          <motion.div
            initial={{ x: -10, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <h3 className="font-medium text-base sm:text-lg text-gray-800">{selectedUser?.name}</h3>
            <p className="text-xs sm:text-sm text-green-500 flex items-center">
              <motion.span
                className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full inline-block mr-1.5"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [1, 0.8, 1]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              ></motion.span>
              Đang hoạt động
            </p>
          </motion.div>
        </div>
        <div className="flex space-x-3 sm:space-x-6">
          <motion.button
            className="text-dexin-light hover:text-dexin-primary transition-colors"
            whileHover={{ scale: 1.1}}
            whileTap={{ scale: 0.9 }}
          >
            <MoreVertical className="h-5 w-5 sm:h-6 sm:w-6" />
          </motion.button>
        </div>
      </motion.div>

      {/* Sử dụng EnhancedMessagesList component */}
      <EnhancedMessagesList
        messages={messages}
        shouldScrollToBottom={shouldScrollToBottom}
      />

      {/* Sử dụng ChatInput component */}
      <ChatInput onSendMessage={handleSendMessage} />
    </motion.div>
  </div>
  );
});

export default ChatDetail;