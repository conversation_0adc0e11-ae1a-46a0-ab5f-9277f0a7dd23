# 🎨 DEXIN Shadcn/UI Components

Tất cả Shadcn/UI components đã được cài đặt và tích hợp với DEXIN design system.

## 📦 Components đã cài đặt

### 🔧 Core Components
- **Button** - Buttons với nhiều variants và sizes
- **Card** - Container components với header, content, footer
- **Input** - Form input fields
- **Label** - Form labels
- **Textarea** - Multi-line text input

### 📋 Layout & Navigation
- **Accordion** - Collapsible content sections
- **Alert** - Alert messages và notifications
- **Avatar** - User profile images với fallback
- **Badge** - Status indicators và labels
- **Breadcrumb** - Navigation breadcrumbs
- **Dropdown Menu** - Context menus và dropdowns
- **Navigation Menu** - Main navigation components
- **Menubar** - Application menu bars
- **Tabs** - Tabbed interfaces
- **Sheet** - Slide-out panels
- **Sidebar** - Application sidebars

### 📝 Form Components
- **Checkbox** - Checkbox inputs
- **Calendar** - Date picker calendar
- **Command** - Command palette interface
- **Form** - React Hook Form integration
- **Radio Group** - Radio button groups
- **Select** - Dropdown select inputs
- **Switch** - Toggle switches
- **Slider** - Range sliders
- **Input OTP** - One-time password inputs

### 📊 Data Display
- **Table** - Data tables với sorting
- **Progress** - Progress bars
- **Pagination** - Page navigation
- **Separator** - Visual dividers
- **Skeleton** - Loading placeholders

### 🎯 Interactive Components
- **Dialog** - Modal dialogs
- **Popover** - Floating content panels
- **Tooltip** - Hover tooltips
- **Hover Card** - Rich hover content
- **Toast** - Notification toasts
- **Sonner** - Modern toast notifications
- **Context Menu** - Right-click menus
- **Drawer** - Mobile-friendly drawers

### 🔧 Advanced Components
- **Collapsible** - Collapsible content
- **Aspect Ratio** - Maintain aspect ratios
- **Scroll Area** - Custom scrollbars
- **Resizable** - Resizable panels
- **Toggle** - Toggle buttons
- **Toggle Group** - Toggle button groups
- **Carousel** - Image/content carousels

## 🚀 Cách sử dụng

### Import components:
```javascript
import { Button, Card, Input, Label } from '@/components/ui';
```

### Hoặc import riêng lẻ:
```javascript
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
```

### Sử dụng với DEXIN styling:
```javascript
<Button className="bg-dexin-primary hover:bg-dexin-light-90 text-white">
  DEXIN Button
</Button>

<Card className="border-dexin-light">
  <CardHeader>
    <CardTitle className="text-dexin-primary">DEXIN Card</CardTitle>
  </CardHeader>
  <CardContent>
    Content với DEXIN styling
  </CardContent>
</Card>
```

## 🎨 DEXIN Brand Colors

Các màu sắc DEXIN có thể sử dụng với components:

- `dexin-primary` - #B90E56 (màu chính)
- `dexin-light` - #FE7CAB (màu sáng)
- `dexin-light-90` - #f35b90 (hover state)
- `dexin-light-50` - #FEBDD5 (màu nhạt)
- `dexin-light-20` - #fe7cac41 (opacity 20%)
- `dexin-light-10` - #fe7cac20 (opacity 10%)
- `dexin-bg` - #FDF2F8 (background)

## 🔗 Hooks

### Shadcn/UI Hooks:
```javascript
import { useToast, toast } from '@/hooks/use-toast';
import { useMobile } from '@/hooks/use-mobile';
```

### Custom Hooks:
```javascript
import { useWindowSize } from '@/hooks';
```

## 📱 Responsive Design

Tất cả components đều responsive và hoạt động tốt trên:
- Desktop (lg:, xl:)
- Tablet (md:)
- Mobile (sm:, default)

## 🌙 Dark Mode Support

Components hỗ trợ dark mode thông qua CSS variables:
- Light mode: Default
- Dark mode: Tự động áp dụng khi có class `dark`

## 🎯 Best Practices

1. **Consistent Styling**: Luôn sử dụng DEXIN brand colors
2. **Accessibility**: Components đã được tối ưu cho accessibility
3. **Performance**: Import chỉ những components cần thiết
4. **Customization**: Sử dụng className để override styles khi cần

## 📚 Documentation

- [Shadcn/UI Docs](https://ui.shadcn.com/docs)
- [Radix UI Docs](https://www.radix-ui.com/docs)
- [Tailwind CSS Docs](https://tailwindcss.com/docs)

## 🎮 Demo

Xem demo tất cả components tại:
```javascript
import AllComponentsDemo from '@/components/demo/AllComponentsDemo';
```

## 🔄 Updates

Để cập nhật components:
```bash
npx shadcn@latest add [component-name] --overwrite
```

Để thêm components mới:
```bash
npx shadcn@latest add [component-name]
```
