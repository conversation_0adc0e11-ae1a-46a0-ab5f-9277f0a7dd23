import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'motion/react';
import { X, Image as ImageIcon, Upload } from 'lucide-react';
import { toast } from 'react-toastify';
import Compressor from 'compressorjs';

const ImageUpload = ({ isOpen, onClose, onImageSelect, buttonRef }) => {
  const [selectedImages, setSelectedImages] = useState([]);
  const [isCompressing, setIsCompressing] = useState(false);
  const [compressionProgress, setCompressionProgress] = useState(0);

  // Validate image file
  const validateImage = (file) => {
    const maxSize = 18 * 1024 * 1024; // 18MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

    if (file.size > maxSize) {
      toast.error(`Hình ảnh "${file.name}" v<PERSON><PERSON><PERSON> quá giới hạn 18MB`);
      return false;
    }

    if (!allowedTypes.includes(file.type)) {
      toast.error(`Loại file "${file.type}" không đ<PERSON>ợc hỗ trợ`);
      return false;
    }

    return true;
  };

  // Process image without compression (maintain original quality)
  const compressImage = (file) => {
    return new Promise((resolve, reject) => {
      new Compressor(file, {
        quality: 1.0, // Giữ nguyên chất lượng 100%
        maxWidth: Infinity, // Không giới hạn chiều rộng
        maxHeight: Infinity, // Không giới hạn chiều cao
        convertSize: Infinity, // Không chuyển đổi định dạng
        checkOrientation: false, // Không xoay ảnh
        success: (compressedFile) => {
          resolve(compressedFile);
        },
        error: (error) => {
          reject(error);
        },
      });
    });
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Create image preview URL
  const createImagePreview = (file) => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.readAsDataURL(file);
    });
  };

  // Handle image drop
  const onDrop = useCallback(async (acceptedFiles, rejectedFiles) => {
    // Xử lý rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach(error => {
        if (error.code === 'file-too-large') {
          toast.error(`File "${file.name}" quá lớn (tối đa 18MB)`);
        } else if (error.code === 'file-invalid-type') {
          toast.error(`Loại file "${file.name}" không được hỗ trợ`);
        }
      });
    });

    // Validate và process accepted files
    const validFiles = acceptedFiles.filter(validateImage);

    if (validFiles.length > 0) {
      setIsCompressing(true);
      setCompressionProgress(0);

      try {
        const processedImages = [];

        for (let i = 0; i < validFiles.length; i++) {
          const file = validFiles[i];

          // Update progress
          setCompressionProgress(((i + 1) / validFiles.length) * 100);

          // Process image (maintain original quality)
          const processedFile = await compressImage(file);

          // Create preview
          const preview = await createImagePreview(processedFile);

          processedImages.push({
            file: processedFile,
            preview,
            originalSize: file.size,
            processedSize: processedFile.size,
            name: file.name
          });
        }

        setSelectedImages(prev => [...prev, ...processedImages]);
        toast.success(`Đã xử lý ${validFiles.length} hình ảnh thành công!`);
      } catch (error) {
        toast.error('Có lỗi xảy ra khi xử lý hình ảnh');
        console.error('Image compression error:', error);
      } finally {
        setIsCompressing(false);
        setCompressionProgress(0);
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxSize: 18 * 1024 * 1024, // 18MB
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/gif': ['.gif'],
      'image/webp': ['.webp']
    },
    multiple: true
  });

  // Remove image from list
  const removeImage = (index) => {
    setSelectedImages(prev => {
      const newImages = prev.filter((_, i) => i !== index);
      // Revoke object URL to prevent memory leaks
      URL.revokeObjectURL(prev[index].preview);
      return newImages;
    });
  };

  // Handle send images
  const handleSendImages = () => {
    if (selectedImages.length === 0) return;

    onImageSelect(selectedImages);

    // Clean up object URLs
    selectedImages.forEach(img => URL.revokeObjectURL(img.preview));
    setSelectedImages([]);
    onClose();

    toast.success(`Đã gửi ${selectedImages.length} hình ảnh!`);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Đính kèm hình ảnh</h3>
              <button
                type="button"
                onClick={onClose}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>

            {/* Dropzone */}
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-dexin-primary bg-dexin-light-10'
                  : 'border-gray-300 hover:border-dexin-primary'
              }`}
            >
              <input {...getInputProps()} />
              <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600 mb-2">
                {isDragActive ? 'Thả hình ảnh vào đây...' : 'Kéo thả hình ảnh hoặc click để chọn'}
              </p>
              <p className="text-sm text-gray-500">
                Hỗ trợ: JPG, PNG, GIF, WEBP (tối đa 18MB mỗi file)
              </p>
            </div>

            {/* Processing Progress */}
            {isCompressing && (
              <div className="mt-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Đang xử lý hình ảnh (chất lượng gốc)...</span>
                  <span className="text-sm text-gray-600">{Math.round(compressionProgress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-dexin-primary h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${compressionProgress}%` }}
                    transition={{ duration: 0.2 }}
                  />
                </div>
              </div>
            )}

            {/* Selected Images */}
            {selectedImages.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-3">
                  Hình ảnh đã chọn ({selectedImages.length})
                </h4>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
                  {selectedImages.map((image, index) => (
                    <div key={index} className="relative group">
                      <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                        <img
                          src={image.preview}
                          alt={image.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                      >
                        <X className="h-3 w-3" />
                      </button>
                      <div className="mt-1 text-xs text-gray-500 text-center">
                        <p className="truncate">{image.name}</p>
                        <p>
                          {formatFileSize(image.processedSize)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-3 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                disabled={isCompressing}
              >
                Hủy
              </button>
              <button
                type="button"
                onClick={handleSendImages}
                disabled={selectedImages.length === 0 || isCompressing}
                className="flex-1 px-4 py-2 bg-dexin-primary text-white rounded-lg hover:bg-dexin-light-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isCompressing ? 'Đang xử lý...' : `Gửi (${selectedImages.length})`}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ImageUpload;
