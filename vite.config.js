import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  return {
    base: '/', // Đảm bảo base URL đúng
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@fonts': path.resolve(__dirname, './src/assets/fonts'),
      },
      extensions: ['.js', '.jsx', '.json'],
    },
    server: {
      port: env.VITE_PORT || 5173,
      open: true,
      host: true,
      strictPort: true,
      // Cấu hình fallback cho SPA routing
      historyApiFallback: {
        index: '/index.html',
        rewrites: [
          { from: /^\/payment\/.*$/, to: '/index.html' },
          { from: /^\/account.*$/, to: '/index.html' },
          { from: /^\/staff\/.*$/, to: '/index.html' }
        ]
      },
      // Thêm headers để tránh MIME type issues
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,PUT,POST,DELETE,OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type,Authorization,Content-Length,X-Requested-With',
      }
    },
    preview: {
      port: env.VITE_PREVIEW_PORT || 4173,
      open: true,
      host: true,
      strictPort: false,
      // Cấu hình cho preview mode
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
      // Thêm middleware để handle SPA routing
      middlewareMode: false,
      // Cấu hình fallback cho SPA
      historyApiFallback: {
        index: '/index.html',
        rewrites: [
          { from: /^\/payment\/.*$/, to: '/index.html' },
          { from: /^\/account.*$/, to: '/index.html' },
          { from: /^\/staff\/.*$/, to: '/index.html' }
        ]
      }
    },
    build: {
      outDir: 'build',
      sourcemap: false, // Tắt sourcemap cho production để giảm kích thước
      assetsInlineLimit: 4096, // Inline assets nhỏ hơn 4KB
      rollupOptions: {
        // Tối ưu tree shaking
        treeshake: {
          preset: 'recommended',
          moduleSideEffects: false
        },
        output: {
          // Sử dụng automatic chunking của Vite thay vì manual chunks để tránh lỗi
          // manualChunks: undefined, // Để Vite tự động xử lý
          // Tối ưu tên file với timestamp để cache busting tốt hơn
          chunkFileNames: (chunkInfo) => {
            return `assets/js/[name]-[hash].js`
          },
          entryFileNames: `assets/js/[name]-[hash].js`,
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const extType = info[info.length - 1];
            if (/\.(otf|ttf|woff|woff2)$/.test(assetInfo.name)) {
              return `assets/fonts/[name]-[hash].[ext]`;
            }
            if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/.test(assetInfo.name)) {
              return `assets/img/[name]-[hash].[ext]`;
            }
            if (extType === 'css') {
              return `assets/css/[name]-[hash].[ext]`;
            }
            return `assets/[ext]/[name]-[hash].[ext]`;
          }
        }
      },
      // Tăng giới hạn cảnh báo chunk size để phù hợp với automatic chunking
      chunkSizeWarningLimit: 1500, // Tăng lên để giảm cảnh báo với automatic chunking
      // Tối ưu CSS
      cssCodeSplit: true,
      // Minify options
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // Loại bỏ console.log trong production
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug'], // Loại bỏ các console functions
          passes: 2 // Chạy 2 lần để tối ưu tốt hơn
        },
        mangle: {
          safari10: true // Tương thích với Safari 10
        }
      }
    },
    css: {
      postcss: './postcss.config.js',
      preprocessorOptions: {
        // Các tùy chọn CSS preprocessor
      },
    },
    esbuild: {
      loader: 'jsx',
      include: /src\/.*\.jsx?$/,
      exclude: [],
    },
    optimizeDeps: {
      esbuildOptions: {
        loader: {
          '.js': 'jsx',
        },
      },
    },
    define: {
      // Chỉ expose các environment variables cần thiết để tránh security risk
      'process.env.NODE_ENV': JSON.stringify(mode),
      'process.env.VITE_TAILWIND_ENABLED': JSON.stringify(true),
      // Chỉ expose các VITE_ variables
      ...Object.keys(env).reduce((prev, key) => {
        if (key.startsWith('VITE_')) {
          prev[`process.env.${key}`] = JSON.stringify(env[key]);
        }
        return prev;
      }, {})
    },
    assetsInclude: ['**/*.otf', '**/*.ttf', '**/*.woff', '**/*.woff2'],
  }
})