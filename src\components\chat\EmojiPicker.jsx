import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import EmojiPicker from 'emoji-picker-react';

const EmojiPickerComponent = ({ isOpen, onClose, onEmojiClick, buttonRef }) => {
  const pickerRef = useRef(null);

  // Đóng picker khi click bên ngoài
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, buttonRef]);

  // X<PERSON> lý khi chọn emoji - không đóng picker để cho phép chọn nhiều emoji
  const handleEmojiClick = (emojiData) => {
    onEmojiClick(emojiData.emoji);
    // Loại bỏ onClose() để picker tiếp tục hiển thị
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={pickerRef}
          className="absolute bottom-full left-0 mb-2 z-50 shadow-2xl rounded-2xl overflow-hidden"
          initial={{ opacity: 0, scale: 0.8, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 10 }}
          transition={{
            duration: 0.2,
            ease: "easeOut",
            type: "spring",
            stiffness: 300,
            damping: 25
          }}
        >
          <div className="bg-white rounded-2xl border border-dexin-light shadow-xl">
            <EmojiPicker
              onEmojiClick={handleEmojiClick}
              width={320}
              height={400}
              theme="light"
              previewConfig={{
                showPreview: false
              }}
              skinTonesDisabled={false}
              searchDisabled={false}
              emojiStyle="native"
              lazyLoadEmojis={true}
              categories={[
                'smileys_people',
                'animals_nature',
                'food_drink',
                'travel_places',
                'activities',
                'objects',
                'symbols',
                'flags'
              ]}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EmojiPickerComponent;
