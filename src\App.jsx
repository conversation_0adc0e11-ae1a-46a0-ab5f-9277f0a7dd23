import { useEffect, Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

// Import trực tiếp các components quan trọng để tránh lỗi context
import HomePage from './pages/Home';
import Login from './pages/Login';
import Signup from './pages/Signup';
import <PERSON><PERSON><PERSON> from './pages/MoLoi';
import <PERSON><PERSON><PERSON><PERSON> from './pages/NgoLoi';
import { PaymentSuccess, PaymentCancel } from './pages/Payment';

// Lazy load chỉ các components ít quan trọng hoặc ít dùng
const ChungNhip = lazy(() => import('./pages/ChungNhip'));
const ChuyenNha = lazy(() => import('./pages/ChuyenNha'));
const PhacY = lazy(() => import('./pages/PhacY'));
const Store = lazy(() => import('./pages/Store'));
const Wishlist = lazy(() => import('./pages/Wishlist'));
const Cart = lazy(() => import('./pages/Cart'));
const ProductDetail = lazy(() => import('./pages/Store/ProductDetail'));
const Account = lazy(() => import('./pages/Account'));
const StaffDashboard = lazy(() => import('./pages/Staff'));

import { LoadingProvider } from './context/LoadingContext';
import { ChatProvider } from './context/ChatContext';
import { WishlistProvider } from './context/WishlistContext';
import { CartProvider } from './context/CartContext';
import { AuthProvider } from './context/AuthContext';
import LoadingRoute from './components/layout/LoadingRoute';
import ProtectedRoute from './components/common/ProtectedRoute';
import RoleBasedRedirect from './components/common/RoleBasedRedirect';
import Analytics from './components/common/Analytics';
import { initializeCacheManagement } from './utils/cacheUtils';

// Layout chung cho các trang thông thường
import Layout from './components/layout/Layout';

const DefaultLayout = ({ children }) => (
  <Layout>{children}</Layout>
);

// Loading component cho Suspense
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-dexin-bg to-dexin-light-10">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-dexin-primary mx-auto mb-4"></div>
      <p className="text-dexin-primary font-medium">Đang tải...</p>
    </div>
  </div>
);

function App() {
  // Lightweight initialization
  useEffect(() => {
    // Initialize cache management only
    initializeCacheManagement();
  }, []);

  return (
    <AuthProvider>
      <LoadingProvider>
        <ChatProvider>
          <WishlistProvider>
            <CartProvider>
              <Router>
                <LoadingRoute>
                  <Routes>
                    {/* Trang MoLoi độc lập không có Navbar và Footer */}
                    <Route path="/mo-loi" element={<MoLoi />} />

                    {/* Trang NgoLoi độc lập không có Navbar và Footer */}
                    <Route path="/ngo-loi" element={<NgoLoi />} />

                    {/* Trang PhacY độc lập không có Navbar và Footer - Lazy loaded */}
                    <Route path="/phac-y" element={
                      <Suspense fallback={<PageLoader />}>
                        <PhacY />
                      </Suspense>
                    } />

                    {/* Trang Staff Dashboard độc lập không có Navbar và Footer - Chỉ cho staff */}
                    <Route path="/staff/*" element={
                      <ProtectedRoute requiredRole="staff">
                        <Suspense fallback={<PageLoader />}>
                          <StaffDashboard />
                        </Suspense>
                      </ProtectedRoute>
                    } />

                    {/* Payment pages - độc lập không có Navbar và Footer */}
                    <Route path="/payment/success" element={<PaymentSuccess />} />
                    <Route path="/payment/cancel" element={<PaymentCancel />} />

                    {/* Các trang khác sử dụng layout mặc định */}
                    <Route path="/" element={
                      <RoleBasedRedirect>
                        <DefaultLayout><HomePage /></DefaultLayout>
                      </RoleBasedRedirect>
                    } />
                    <Route path="/login" element={<DefaultLayout><Login /></DefaultLayout>} />
                    <Route path="/signup" element={<DefaultLayout><Signup /></DefaultLayout>} />
                    <Route path="/chung-nhip" element={
                      <DefaultLayout>
                        <Suspense fallback={<PageLoader />}>
                          <ChungNhip />
                        </Suspense>
                      </DefaultLayout>
                    } />
                    <Route path="/chuyen-nha" element={
                      <DefaultLayout>
                        <Suspense fallback={<PageLoader />}>
                          <ChuyenNha />
                        </Suspense>
                      </DefaultLayout>
                    } />
                    <Route path="/store" element={
                      <DefaultLayout>
                        <Suspense fallback={<PageLoader />}>
                          <Store />
                        </Suspense>
                      </DefaultLayout>
                    } />
                    <Route path="/wishlist" element={
                      <DefaultLayout>
                        <Suspense fallback={<PageLoader />}>
                          <Wishlist />
                        </Suspense>
                      </DefaultLayout>
                    } />
                    <Route path="/cart" element={
                      <DefaultLayout>
                        <Suspense fallback={<PageLoader />}>
                          <Cart />
                        </Suspense>
                      </DefaultLayout>
                    } />
                    <Route path="/product/:productId" element={
                      <DefaultLayout>
                        <Suspense fallback={<PageLoader />}>
                          <ProductDetail />
                        </Suspense>
                      </DefaultLayout>
                    } />
                    <Route path="/account" element={
                      <DefaultLayout>
                        <Suspense fallback={<PageLoader />}>
                          <Account />
                        </Suspense>
                      </DefaultLayout>
                    } />

                  </Routes>
                </LoadingRoute>
                
                {/* Vercel Web Analytics - Lightweight */}
                <Analytics />

                {/* Toast notifications */}
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      fontFamily: 'BDLifelessGrotesk, sans-serif',
                    },
                    success: {
                      style: {
                        background: '#10B981',
                        color: 'white',
                      },
                    },
                    error: {
                      style: {
                        background: '#EF4444',
                        color: 'white',
                      },
                    },
                  }}
                />
              </Router>
            </CartProvider>
          </WishlistProvider>
        </ChatProvider>
      </LoadingProvider>
    </AuthProvider>
  );
}

export default App;
