import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'motion/react';
import { Link } from 'react-router-dom';
import { useWishlist } from '../../context/WishlistContext';
import WishlistContent from './components/WishlistContent';
import Button from '../../components/common/Button';
import { Heart, ShoppingBag } from 'lucide-react';
import { toast } from 'react-toastify';

// Component trang Wishlist
const Wishlist = () => {
  const { wishlistItems, clearWishlist } = useWishlist();
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage] = useState(6); // Hiển thị 6 sản phẩm trên mỗi trang

  // Reset trang về 1 khi danh sách sản phẩm thay đổi
  useEffect(() => {
    setCurrentPage(1);
  }, [wishlistItems.length]);

  // Tính toán các sản phẩm hiển thị trên trang hiện tại
  const currentProducts = useMemo(() => {
    const indexOfLastProduct = currentPage * productsPerPage;
    const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
    return wishlistItems.slice(indexOfFirstProduct, indexOfLastProduct);
  }, [wishlistItems, currentPage, productsPerPage]);

  // Tính toán tổng số trang
  const totalPages = useMemo(() => {
    return Math.ceil(wishlistItems.length / productsPerPage);
  }, [wishlistItems.length, productsPerPage]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-dexin-bg py-8 md:py-12">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <motion.div
              className="mb-6 md:mb-0 md:w-1/2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-4">
                Danh sách yêu thích <span className="text-dexin-primary">({wishlistItems.length})</span>
              </h1>
              <p className="text-gray-600 mb-6 max-w-lg">
                Những sản phẩm bạn đã đánh dấu yêu thích sẽ được hiển thị ở đây. Dễ dàng theo dõi và mua sắm khi bạn sẵn sàng.
              </p>
              <div className="flex space-x-4">
                <Link to="/store">
                  <Button variant="outline" className="flex items-center">
                    <ShoppingBag className="h-5 w-5 mr-2" />
                    <span>Tiếp tục mua sắm</span>
                  </Button>
                </Link>
                {wishlistItems.length > 0 && (
                  <Button
                    variant="ghost"
                    className="text-gray-500 hover:text-dexin-primary"
                    onClick={() => {
                      clearWishlist();
                      toast.info('Đã xóa tất cả sản phẩm', {
                        icon: () => <span style={{ fontSize: '1rem', marginRight: '10px' }}>🗑️</span>,
                        className: 'toast-message'
                      });
                    }}
                  >
                    Xóa tất cả
                  </Button>
                )}
              </div>
            </motion.div>
            <motion.div
              className="md:w-1/2 flex justify-center md:justify-end"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <img
                src="/images/wishlist-hero.png"
                alt="Wishlist"
                className="w-auto h-auto max-w-full max-h-[300px] md:max-h-full object-contain"
                onError={(e) => {
                  e.target.src = "/images/jogging.png"; // Fallback image
                }}
              />
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 py-8 md:py-12">
        <WishlistContent
          wishlistItems={currentProducts}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          totalPages={totalPages}
        />
      </div>
    </div>
  );
};

export default Wishlist;
