import { useEffect } from 'react';
import { Analytics as VercelAnalytics } from '@vercel/analytics/react';
import { useLocation } from 'react-router-dom';

// Component quản lý Vercel Web Analytics
const Analytics = () => {
  const location = useLocation();

  // Track page views khi route thay đổi
  useEffect(() => {
    // Custom event để track navigation
    if (typeof window !== 'undefined' && window.va) {
      window.va('track', 'PageView', {
        page: location.pathname,
        referrer: document.referrer
      });
    }
  }, [location]);

  // Chỉ render Analytics component khi đang ở môi trường Vercel
  const isVercelEnvironment = () => {
    // Kiểm tra environment variables của Vercel
    if (import.meta.env.VERCEL_ENV ||
        import.meta.env.VERCEL_URL ||
        import.meta.env.VERCEL_GIT_COMMIT_SHA ||
        import.meta.env.VITE_VERCEL_ENV) {
      return true;
    }

    // Kiểm tra hostname của Vercel
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      return hostname.includes('vercel.app') ||
             hostname.includes('vercel.sh') ||
             hostname.includes('.vercel.app') ||
             // Domain tùy chỉnh được deploy qua Vercel
             (hostname !== 'localhost' && 
              hostname !== '127.0.0.1' && 
              !hostname.includes('192.168.') &&
              !hostname.includes('10.0.') &&
              import.meta.env.PROD);
    }

    return import.meta.env.PROD;
  };

  if (!isVercelEnvironment()) {
    return null;
  }

  return <VercelAnalytics />;
};

// Utility functions để track custom events
export const trackEvent = (eventName, properties = {}) => {
  if (typeof window !== 'undefined' && window.va) {
    window.va('track', eventName, properties);
  }
};

// Track specific user interactions
export const trackUserAction = {
  // Track khi user thêm sản phẩm vào giỏ hàng
  addToCart: (productId, productName, price) => {
    trackEvent('AddToCart', {
      productId,
      productName,
      price,
      currency: 'VND'
    });
  },

  // Track khi user thêm vào wishlist
  addToWishlist: (productId, productName) => {
    trackEvent('AddToWishlist', {
      productId,
      productName
    });
  },

  // Track khi user đăng ký
  signUp: (method = 'email') => {
    trackEvent('SignUp', {
      method
    });
  },

  // Track khi user đăng nhập
  signIn: (method = 'email') => {
    trackEvent('SignIn', {
      method
    });
  },

  // Track khi user mua hàng thành công
  purchase: (orderId, total, items) => {
    trackEvent('Purchase', {
      orderId,
      total,
      currency: 'VND',
      itemCount: items?.length || 0
    });
  },

  // Track khi user sử dụng AI Chat
  chatInteraction: (messageType = 'user') => {
    trackEvent('ChatInteraction', {
      messageType
    });
  },

  // Track khi user sử dụng design tool
  designToolUsage: (action, toolType) => {
    trackEvent('DesignTool', {
      action,
      toolType
    });
  },

  // Track search behavior
  search: (query, category = 'general') => {
    trackEvent('Search', {
      query: query?.length > 50 ? query.substring(0, 50) + '...' : query,
      category
    });
  }
};

export default Analytics;
