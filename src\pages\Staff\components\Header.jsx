import React from 'react';
import { motion } from 'motion/react';
import { Search, Bell } from 'lucide-react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '../../../components/ui/avatar';
import { useAuth } from '../../../context/AuthContext';

const Header = () => {
  const { user, getDisplayName } = useAuth();

  // Tạo initials từ tên user để làm fallback
  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <motion.header
      className="bg-background border-b px-6 py-4"
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center justify-between">
        {/* Search Bar */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
            <Input
              type="text"
              placeholder="Tìm kiếm mã đơn hàng..."
              className="pl-10"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-4">
          {/* Notification Bell */}
          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
            <Button variant="ghost" size="icon" className="relative">
              <Bell size={20} />
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
              >
                3
              </Badge>
            </Button>
          </motion.div>

          {/* User Info */}
          <div className="flex items-center space-x-3">
            <div className="text-right">
              <p className="text-sm font-medium">
                {getDisplayName() || 'Staff User'}
              </p>
              <p className="text-xs text-muted-foreground">
                {user?.role?.toLowerCase() === 'staff' ? 'Nhân viên' : 'Người dùng'}
              </p>
            </div>
            <Avatar className="w-8 h-8">
              <AvatarImage
                src={user?.avatar && user.avatar !== 'default_avatar.png' ? user.avatar : undefined}
                alt={getDisplayName() || 'User Avatar'}
              />
              <AvatarFallback className="bg-dexin-primary text-white text-sm font-medium">
                {getInitials(getDisplayName())}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
