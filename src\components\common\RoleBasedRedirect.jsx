import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const RoleBasedRedirect = ({ children }) => {
  const { user, loading, isAuthenticated, isStaff } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Chỉ redirect khi đã load xong và user đã đăng nhập với role staff
    if (!loading && isAuthenticated && isStaff()) {
      console.log('🔄 Staff user detected, redirecting to staff dashboard');
      // Nếu staff truy cập trang chủ, chuyển hướng đến staff dashboard
      navigate('/staff', { replace: true });
    }
  }, [loading, isAuthenticated, isStaff, navigate]);

  // Nếu đang loading hoặc là staff (sẽ được redirect), không render children
  if (loading || (isAuthenticated && isStaff())) {
    return null;
  }

  // Render children cho user thông thường hoặc chưa đăng nhập
  return children;
};

export default RoleBasedRedirect;
