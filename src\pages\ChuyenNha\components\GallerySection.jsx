import React, { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Heart, Eye, Share2, Download } from 'lucide-react';
import { toast } from 'react-toastify';

const GallerySection = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [likedItems, setLikedItems] = useState(new Set());

  // Categories cho filter
  const categories = [
    { id: 'all', name: '<PERSON><PERSON>t cả', count: 19 },
    { id: 'living-room', name: '<PERSON><PERSON><PERSON> khách', count: 7 },
    { id: 'bedroom', name: '<PERSON>òng ngủ', count: 5 },
    { id: 'kitchen', name: '<PERSON><PERSON><PERSON> bếp', count: 4 },
    { id: 'bathroom', name: '<PERSON>òng tắm', count: 3 }
  ];

  // Dữ liệu gallery items với aspect ratios khác nhau cho masonry layout
  const galleryItems = [
    {
      id: 1,
      title: "<PERSON>òng khách hiện đại",
      category: "living-room",
      image: "/images/ChuyenNha/image.png",
      views: 1200,
      likes: 89,
      aspectRatio: "tall" // Portrait
    },
    {
      id: 2,
      title: "Không gian xanh",
      category: "living-room",
      image: "/images/ChuyenNha/image (1).png",
      views: 980,
      likes: 67,
      aspectRatio: "square" // Square
    },
    {
      id: 3,
      title: "Phòng ngủ ấm cúng",
      category: "bedroom",
      image: "/images/ChuyenNha/image (2).png",
      views: 1500,
      likes: 112,
      aspectRatio: "wide" // Landscape
    },
    {
      id: 4,
      title: "Nhà bếp thông minh",
      category: "kitchen",
      image: "/images/ChuyenNha/image (3).png",
      views: 890,
      likes: 45,
      aspectRatio: "tall" // Portrait
    },
    {
      id: 5,
      title: "Phòng tắm sang trọng",
      category: "bathroom",
      image: "/images/ChuyenNha/image (4).png",
      views: 750,
      likes: 38,
      aspectRatio: "square" // Square
    },
    {
      id: 6,
      title: "Góc làm việc",
      category: "living-room",
      image: "/images/ChuyenNha/image (5).png",
      views: 1100,
      likes: 78,
      aspectRatio: "wide" // Landscape
    },
    {
      id: 7,
      title: "Phòng ngủ trẻ em",
      category: "bedroom",
      image: "/images/ChuyenNha/image (6).png",
      views: 920,
      likes: 56,
      aspectRatio: "tall" // Portrait
    },
    {
      id: 8,
      title: "Khu vực ăn uống",
      category: "kitchen",
      image: "/images/ChuyenNha/image (7).png",
      views: 1300,
      likes: 95,
      aspectRatio: "square" // Square
    },
    {
      id: 9,
      title: "Phòng khách tối giản",
      category: "living-room",
      image: "/images/ChuyenNha/image (8).png",
      views: 1450,
      likes: 103,
      aspectRatio: "wide" // Landscape
    },
    {
      id: 10,
      title: "Không gian mở",
      category: "living-room",
      image: "/images/ChuyenNha/image (9).png",
      views: 1320,
      likes: 87,
      aspectRatio: "tall" // Portrait
    },
    {
      id: 11,
      title: "Phòng ngủ master",
      category: "bedroom",
      image: "/images/ChuyenNha/image (10).png",
      views: 1180,
      likes: 94,
      aspectRatio: "square" // Square
    },
    {
      id: 12,
      title: "Bếp hiện đại",
      category: "kitchen",
      image: "/images/ChuyenNha/image (11).png",
      views: 1050,
      likes: 72,
      aspectRatio: "wide" // Landscape
    },
    {
      id: 13,
      title: "Phòng tắm spa",
      category: "bathroom",
      image: "/images/ChuyenNha/image (12).png",
      views: 890,
      likes: 65,
      aspectRatio: "tall" // Portrait
    },
    {
      id: 14,
      title: "Góc đọc sách",
      category: "living-room",
      image: "/images/ChuyenNha/image (13).png",
      views: 1240,
      likes: 88,
      aspectRatio: "square" // Square
    },
    {
      id: 15,
      title: "Phòng ngủ vintage",
      category: "bedroom",
      image: "/images/ChuyenNha/image (14).png",
      views: 1380,
      likes: 101,
      aspectRatio: "wide" // Landscape
    },
    {
      id: 16,
      title: "Bếp đảo",
      category: "kitchen",
      image: "/images/ChuyenNha/image (15).png",
      views: 1150,
      likes: 79,
      aspectRatio: "tall" // Portrait
    },
    {
      id: 17,
      title: "Phòng tắm tối giản",
      category: "bathroom",
      image: "/images/ChuyenNha/image (16).png",
      views: 920,
      likes: 54,
      aspectRatio: "square" // Square
    },
    {
      id: 18,
      title: "Phòng khách sang trọng",
      category: "living-room",
      image: "/images/ChuyenNha/image (17).png",
      views: 1560,
      likes: 118,
      aspectRatio: "wide" // Landscape
    },
    {
      id: 19,
      title: "Phòng ngủ Scandinavian",
      category: "bedroom",
      image: "/images/ChuyenNha/image (18).png",
      views: 1290,
      likes: 96,
      aspectRatio: "tall" // Portrait
    }
  ];

  // Function để get height class dựa trên aspect ratio
  const getImageHeight = (aspectRatio) => {
    switch (aspectRatio) {
      case 'tall':
        return 'h-80'; // 320px - Portrait
      case 'wide':
        return 'h-48'; // 192px - Landscape
      case 'square':
      default:
        return 'h-64'; // 256px - Square
    }
  };

  // Filter items theo category
  const filteredItems = selectedCategory === 'all'
    ? galleryItems
    : galleryItems.filter(item => item.category === selectedCategory);

  // Handle like/unlike
  const handleLike = (itemId) => {
    const newLikedItems = new Set(likedItems);
    if (newLikedItems.has(itemId)) {
      newLikedItems.delete(itemId);
      toast.success('Đã bỏ thích!', {
        position: "top-right",
        autoClose: 2000,
        style: { marginTop: '80px' }
      });
    } else {
      newLikedItems.add(itemId);
      toast.success('Đã thêm vào yêu thích!', {
        position: "top-right",
        autoClose: 2000,
        style: { marginTop: '80px' }
      });
    }
    setLikedItems(newLikedItems);
  };

  // Handle share
  const handleShare = (item) => {
    if (navigator.share) {
      navigator.share({
        title: item.title,
        text: `Xem thiết kế "${item.title}" trên DEXIN`,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Đã sao chép link!', {
        position: "top-right",
        autoClose: 2000,
        style: { marginTop: '80px' }
      });
    }
  };

  return (
    <section id="gallery-section" className="py-16 sm:py-20 bg-dexin-bg">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-dexin-dark mb-4">
            Thư viện ý tưởng
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Khám phá hàng nghìn ý tưởng thiết kế nội thất từ cộng đồng DEXIN
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-3 mb-12"
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-dexin-primary text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:bg-dexin-light-10 hover:text-dexin-primary'
              }`}
            >
              {category.name}
              <span className="ml-2 text-sm opacity-75">({category.count})</span>
            </button>
          ))}
        </motion.div>

        {/* Pinterest-style Masonry Gallery */}
        <motion.div
          layout
          className="columns-1 sm:columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-6 space-y-6"
        >
          <AnimatePresence mode="wait">
            {filteredItems.map((item, index) => (
              <motion.div
                key={item.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                className="group relative bg-white rounded-xl overflow-hidden shadow-lg
                         hover:shadow-xl transition-all duration-300 break-inside-avoid mb-6"
              >
                {/* Image với dynamic height */}
                <div className={`relative ${getImageHeight(item.aspectRatio)} overflow-hidden`}>
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-full object-cover group-hover:scale-110
                             transition-transform duration-500"
                  />

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20
                                transition-all duration-300"></div>

                  {/* Action Buttons */}
                  <div className="absolute top-3 right-3 flex flex-col gap-2
                                opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <button
                      onClick={() => handleLike(item.id)}
                      className={`p-2 rounded-full backdrop-blur-sm transition-all duration-300 ${
                        likedItems.has(item.id)
                          ? 'bg-red-500 text-white'
                          : 'bg-white/80 text-gray-700 hover:bg-red-500 hover:text-white'
                      }`}
                    >
                      <Heart size={16} fill={likedItems.has(item.id) ? 'currentColor' : 'none'} />
                    </button>
                    <button
                      onClick={() => handleShare(item)}
                      className="p-2 rounded-full bg-white/80 text-gray-700
                               hover:bg-dexin-primary hover:text-white transition-all duration-300"
                    >
                      <Share2 size={16} />
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="p-4">
                  <h3 className="font-semibold text-dexin-dark mb-2 group-hover:text-dexin-primary
                               transition-colors duration-300">
                    {item.title}
                  </h3>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Eye size={14} />
                      <span>{item.views.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Heart size={14} />
                      <span>{item.likes}</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Load More Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <button className="bg-dexin-primary text-white px-8 py-3 rounded-full font-medium
                           hover:bg-dexin-light-90 transition-all duration-300
                           shadow-lg hover:shadow-dexin">
            Xem thêm ý tưởng
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default GallerySection;
