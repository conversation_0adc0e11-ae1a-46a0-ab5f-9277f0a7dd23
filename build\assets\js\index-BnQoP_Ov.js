import{c as i,j as e,m as a,U as t,C as n,B as s,r as l,A as o,H as r,E as c,y as h}from"./index-DdBL2cja.js";import{S as d}from"./share-2-Cf5bFpV9.js";
/**
 * @license lucide-react v0.484.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m=i("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]),g=()=>e.jsx("section",{className:"relative bg-gradient-to-br from-dexin-bg to-white py-16 sm:py-10 lg:py-15",children:e.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs(a.div,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},transition:{duration:.8,ease:"easeOut"},className:"text-center mb-10",children:[e.jsxs(a.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"flex items-center justify-center space-x-2 text-sm text-gray-600 mb-6",children:[e.jsx("span",{children:"Trang chủ"}),e.jsx("span",{children:"/"}),e.jsx("span",{className:"text-dexin-primary font-medium",children:"Chuyện nhà"})]}),e.jsx(a.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"text-dexin-primary font-medium text-lg mb-4",children:"Rất vui được đồng hành cùng bạn!"}),e.jsxs(a.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-dexin-dark mb-8 leading-tight",children:["Mỗi không gian là",e.jsx("br",{}),e.jsx("div",{className:"mt-2",children:e.jsx("span",{className:"text-dexin-primary",children:"một câu chuyện đáng nhớ"})})]})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"hidden lg:grid lg:grid-cols-5 gap-6 items-center",children:[e.jsx(a.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.6},className:"col-span-1",children:e.jsxs("div",{className:"relative rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300",children:[e.jsx("img",{src:"/images/hometile hero.jpg",alt:"Không gian nội thất phụ",className:"w-full h-[300px] xl:h-[400px] object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"})]})}),e.jsx(a.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"col-span-3 relative",children:e.jsxs("div",{className:"relative rounded-2xl overflow-hidden shadow-2xl",children:[e.jsx("img",{src:"/images/ChuyenNha/phong-cach-biophilic-design-24.jpg",alt:"Phong cách biophilic design - Không gian xanh trong nhà",className:"w-full h-[400px] xl:h-[500px] object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),e.jsx(a.div,{animate:{y:[0,-10,0],rotate:[0,2,0]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},className:"absolute -top-4 -right-4 w-16 h-16 bg-dexin-light rounded-full\n                             opacity-80 blur-sm"}),e.jsx(a.div,{animate:{y:[0,15,0],rotate:[0,-3,0]},transition:{duration:5,repeat:1/0,ease:"easeInOut",delay:1},className:"absolute -bottom-6 -left-6 w-12 h-12 bg-dexin-primary rounded-full\n                             opacity-60 blur-sm"})]})}),e.jsx(a.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:1},className:"col-span-1",children:e.jsxs("div",{className:"relative rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300",children:[e.jsx("img",{src:"/images/hometile hero.jpg",alt:"Không gian nội thất phụ",className:"w-full h-[300px] xl:h-[400px] object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"})]})})]}),e.jsxs("div",{className:"hidden md:grid lg:hidden md:grid-cols-3 gap-6 items-center",children:[e.jsx(a.div,{initial:{opacity:0,x:-30},animate:{opacity:1,x:0},transition:{duration:.8,delay:.6},className:"col-span-1",children:e.jsxs("div",{className:"relative rounded-2xl overflow-hidden shadow-lg",children:[e.jsx("img",{src:"/images/hometile hero.jpg",alt:"Không gian nội thất phụ",className:"w-full h-[250px] object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"})]})}),e.jsx(a.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"col-span-2",children:e.jsxs("div",{className:"relative rounded-2xl overflow-hidden shadow-2xl",children:[e.jsx("img",{src:"/images/ChuyenNha/phong-cach-biophilic-design-24.jpg",alt:"Phong cách biophilic design - Không gian xanh trong nhà",className:"w-full h-[350px] object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]})})]}),e.jsx(a.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"md:hidden",children:e.jsxs("div",{className:"relative rounded-2xl overflow-hidden shadow-2xl",children:[e.jsx("img",{src:"/images/ChuyenNha/phong-cach-biophilic-design-24.jpg",alt:"Phong cách biophilic design - Không gian xanh trong nhà",className:"w-full h-[300px] sm:h-[400px] object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]})})]}),e.jsxs(a.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:1.2},className:"text-center mt-12",children:[e.jsx("p",{className:"text-gray-600 text-lg max-w-2xl mx-auto mb-8",children:"Khám phá những câu chuyện thú vị về thiết kế nội thất, xu hướng trang trí và cách tạo nên không gian sống hoàn hảo cho gia đình bạn."}),e.jsx(a.button,{onClick:()=>{const i=document.getElementById("gallery-section");if(i){const e=80,a=i.getBoundingClientRect().top+window.pageYOffset-e;window.scrollTo({top:a,behavior:"smooth"})}},whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-dexin-primary text-white px-8 py-3 rounded-full font-medium\n                       hover:bg-dexin-light-90 transition-all duration-300\n                       shadow-lg hover:shadow-dexin cursor-pointer",children:"Khám phá ngay"})]})]})}),x=()=>{const i={title:"Góc nhỏ, bình yên và một chút thay đổi",content:"Có bao giờ bạn bước vào phòng sau một ngày dài và cảm thấy không gian quanh mình quá đơn điệu, nhưng lại chẳng biết vì sao bạn lại cảm thấy như thế.\n\nThế là một buổi chiều, mình quyết định thay đổi một chút - không cần gì quá lớn lao. Chỉ là đổi chỗ bàn làm việc để đón nắng sớm hơn, đặt thêm một bình hoa nhỏ, và đổi gối đến từ góc phòng mà thôi. Thật là, chỉ với điều giản đơn mà cảm phòng như thôi thế, nhẹ nhàng đến lạ minh sau những bận bề.\n\nBạn có một góc nhỏ nào muốn làm mới không? Biết đâu, chỉ cần một sự thay đổi rất nhỏ, bạn đã có thể tìm thấy bình yên trong chính không gian của mình. 😊",author:"@dexinhomyyy",date:"14.2.2020, Thứ bảy 16:06",avatar:"/images/Avatar.png"};return e.jsx("section",{className:"py-16 sm:py-20 bg-white",children:e.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs(a.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 bg-dexin-light-10 text-dexin-primary px-4 py-2 rounded-full text-sm font-medium mb-6",children:[e.jsx("span",{children:"Bài viết của tháng"}),e.jsx("span",{children:"💖"})]}),e.jsx("h2",{className:"text-3xl sm:text-4xl font-bold text-dexin-dark mb-4",children:i.title})]}),e.jsx(a.article,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-dexin-light-20 border border-dexin-light overflow-hidden\n                         hover:shadow-xl transition-all duration-300",children:[e.jsx("div",{className:"p-6 sm:p-8 border-b border-gray-100",children:e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-full overflow-hidden bg-gray-200",children:e.jsx("img",{src:i.avatar,alt:i.author,className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx(t,{size:14}),e.jsx("span",{className:"font-medium text-dexin-dark",children:i.author})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500 mt-1",children:[e.jsx(n,{size:14}),e.jsx("span",{children:i.date})]})]})]})}),e.jsxs("div",{className:"p-6 sm:p-8",children:[e.jsx("div",{className:"prose prose-lg max-w-none",children:i.content.split("\n\n").map(((i,t)=>e.jsx(a.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"text-gray-700 leading-relaxed mb-4 last:mb-0",children:i},t)))}),e.jsx(a.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},viewport:{once:!0},className:"flex justify-center mt-8",children:e.jsx("div",{className:"group",children:e.jsxs(s,{variant:"outline",size:"lg",className:" border-dexin-primary/20 hover:border-dexin-primary/40\n                          transition-all duration-300",children:[e.jsx("span",{children:"Đọc tiếp"}),e.jsx(m,{size:16,className:"ml-2 group-hover:translate-y-1 transition-transform duration-300"})]})})})]})]})})]})})},p=()=>{const[i,t]=l.useState("all"),[n,s]=l.useState(new Set),m=[{id:1,title:"Phòng khách hiện đại",category:"living-room",image:"/images/ChuyenNha/image.png",views:1200,likes:89,aspectRatio:"tall"},{id:2,title:"Không gian xanh",category:"living-room",image:"/images/ChuyenNha/image (1).png",views:980,likes:67,aspectRatio:"square"},{id:3,title:"Phòng ngủ ấm cúng",category:"bedroom",image:"/images/ChuyenNha/image (2).png",views:1500,likes:112,aspectRatio:"wide"},{id:4,title:"Nhà bếp thông minh",category:"kitchen",image:"/images/ChuyenNha/image (3).png",views:890,likes:45,aspectRatio:"tall"},{id:5,title:"Phòng tắm sang trọng",category:"bathroom",image:"/images/ChuyenNha/image (4).png",views:750,likes:38,aspectRatio:"square"},{id:6,title:"Góc làm việc",category:"living-room",image:"/images/ChuyenNha/image (5).png",views:1100,likes:78,aspectRatio:"wide"},{id:7,title:"Phòng ngủ trẻ em",category:"bedroom",image:"/images/ChuyenNha/image (6).png",views:920,likes:56,aspectRatio:"tall"},{id:8,title:"Khu vực ăn uống",category:"kitchen",image:"/images/ChuyenNha/image (7).png",views:1300,likes:95,aspectRatio:"square"},{id:9,title:"Phòng khách tối giản",category:"living-room",image:"/images/ChuyenNha/image (8).png",views:1450,likes:103,aspectRatio:"wide"},{id:10,title:"Không gian mở",category:"living-room",image:"/images/ChuyenNha/image (9).png",views:1320,likes:87,aspectRatio:"tall"},{id:11,title:"Phòng ngủ master",category:"bedroom",image:"/images/ChuyenNha/image (10).png",views:1180,likes:94,aspectRatio:"square"},{id:12,title:"Bếp hiện đại",category:"kitchen",image:"/images/ChuyenNha/image (11).png",views:1050,likes:72,aspectRatio:"wide"},{id:13,title:"Phòng tắm spa",category:"bathroom",image:"/images/ChuyenNha/image (12).png",views:890,likes:65,aspectRatio:"tall"},{id:14,title:"Góc đọc sách",category:"living-room",image:"/images/ChuyenNha/image (13).png",views:1240,likes:88,aspectRatio:"square"},{id:15,title:"Phòng ngủ vintage",category:"bedroom",image:"/images/ChuyenNha/image (14).png",views:1380,likes:101,aspectRatio:"wide"},{id:16,title:"Bếp đảo",category:"kitchen",image:"/images/ChuyenNha/image (15).png",views:1150,likes:79,aspectRatio:"tall"},{id:17,title:"Phòng tắm tối giản",category:"bathroom",image:"/images/ChuyenNha/image (16).png",views:920,likes:54,aspectRatio:"square"},{id:18,title:"Phòng khách sang trọng",category:"living-room",image:"/images/ChuyenNha/image (17).png",views:1560,likes:118,aspectRatio:"wide"},{id:19,title:"Phòng ngủ Scandinavian",category:"bedroom",image:"/images/ChuyenNha/image (18).png",views:1290,likes:96,aspectRatio:"tall"}],g=i=>{switch(i){case"tall":return"h-80";case"wide":return"h-48";default:return"h-64"}},x="all"===i?m:m.filter((e=>e.category===i));return e.jsx("section",{id:"gallery-section",className:"py-16 sm:py-20 bg-dexin-bg",children:e.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs(a.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12",children:[e.jsx("h2",{className:"text-3xl sm:text-4xl font-bold text-dexin-dark mb-4",children:"Thư viện ý tưởng"}),e.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Khám phá hàng nghìn ý tưởng thiết kế nội thất từ cộng đồng DEXIN"})]}),e.jsx(a.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"flex flex-wrap justify-center gap-3 mb-12",children:[{id:"all",name:"Tất cả",count:19},{id:"living-room",name:"Phòng khách",count:7},{id:"bedroom",name:"Phòng ngủ",count:5},{id:"kitchen",name:"Nhà bếp",count:4},{id:"bathroom",name:"Phòng tắm",count:3}].map((a=>e.jsxs("button",{onClick:()=>t(a.id),className:"px-6 py-3 rounded-full font-medium transition-all duration-300 "+(i===a.id?"bg-dexin-primary text-white shadow-lg":"bg-white text-gray-600 hover:bg-dexin-light-10 hover:text-dexin-primary"),children:[a.name,e.jsxs("span",{className:"ml-2 text-sm opacity-75",children:["(",a.count,")"]})]},a.id)))}),e.jsx(a.div,{layout:!0,className:"columns-1 sm:columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-6 space-y-6",children:e.jsx(o,{mode:"wait",children:x.map(((i,t)=>e.jsxs(a.div,{layout:!0,initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{duration:.4,delay:.1*t},className:"group relative bg-white rounded-xl overflow-hidden shadow-lg\n                         hover:shadow-xl transition-all duration-300 break-inside-avoid mb-6",children:[e.jsxs("div",{className:`relative ${g(i.aspectRatio)} overflow-hidden`,children:[e.jsx("img",{src:i.image,alt:i.title,className:"w-full h-full object-cover group-hover:scale-110\n                             transition-transform duration-500"}),e.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20\n                                transition-all duration-300"}),e.jsxs("div",{className:"absolute top-3 right-3 flex flex-col gap-2\n                                opacity-0 group-hover:opacity-100 transition-all duration-300",children:[e.jsx("button",{onClick:()=>(i=>{const e=new Set(n);e.has(i)?(e.delete(i),h.success("Đã bỏ thích!",{position:"top-right",autoClose:2e3,style:{marginTop:"80px"}})):(e.add(i),h.success("Đã thêm vào yêu thích!",{position:"top-right",autoClose:2e3,style:{marginTop:"80px"}})),s(e)})(i.id),className:"p-2 rounded-full backdrop-blur-sm transition-all duration-300 "+(n.has(i.id)?"bg-red-500 text-white":"bg-white/80 text-gray-700 hover:bg-red-500 hover:text-white"),children:e.jsx(r,{size:16,fill:n.has(i.id)?"currentColor":"none"})}),e.jsx("button",{onClick:()=>(i=>{navigator.share?navigator.share({title:i.title,text:`Xem thiết kế "${i.title}" trên DEXIN`,url:window.location.href}):(navigator.clipboard.writeText(window.location.href),h.success("Đã sao chép link!",{position:"top-right",autoClose:2e3,style:{marginTop:"80px"}}))})(i),className:"p-2 rounded-full bg-white/80 text-gray-700\n                               hover:bg-dexin-primary hover:text-white transition-all duration-300",children:e.jsx(d,{size:16})})]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"font-semibold text-dexin-dark mb-2 group-hover:text-dexin-primary\n                               transition-colors duration-300",children:i.title}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(c,{size:14}),e.jsx("span",{children:i.views.toLocaleString()})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(r,{size:14}),e.jsx("span",{children:i.likes})]})]})]})]},i.id)))})}),e.jsx(a.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center mt-12",children:e.jsx("button",{className:"bg-dexin-primary text-white px-8 py-3 rounded-full font-medium\n                           hover:bg-dexin-light-90 transition-all duration-300\n                           shadow-lg hover:shadow-dexin",children:"Xem thêm ý tưởng"})})]})})},y=()=>{const i={hidden:{opacity:0,y:20},show:{opacity:1,y:0,transition:{duration:.6,ease:[.25,.1,.25,1]}}};return e.jsxs(a.div,{initial:"hidden",animate:"show",variants:{hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.3}}},className:"min-h-screen bg-white",children:[e.jsx(a.div,{variants:i,children:e.jsx(g,{})}),e.jsx(a.div,{variants:i,children:e.jsx(x,{})}),e.jsx(a.div,{variants:i,children:e.jsx(p,{})})]})};export{y as default};
