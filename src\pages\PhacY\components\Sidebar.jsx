import React, { useState, useRef } from 'react';
import { Search } from 'lucide-react';
import { classNames } from '../../../utils/classNames';
import {
  getDistance,
  triggerHapticFeedback,
  DRAG_CONSTANTS,
  logTouchEvent
} from '../../../utils/touchDragHelper';

// Component riêng cho furniture item với mobile drag support
const FurnitureItem = ({
  item,
  isDragging,
  onDragStart,
  onMobileDragStart,
  onMobileDragMove,
  onMobileDragEnd
}) => {
  const [isTouching, setIsTouching] = useState(false);
  const touchStartPos = useRef({ x: 0, y: 0 });
  const itemRef = useRef(null);

  // Use useEffect to add non-passive event listeners
  React.useEffect(() => {
    const element = itemRef.current;
    if (!element) return;

    const handleTouchStart = (e) => {
      const touch = e.touches[0];
      touchStartPos.current = { x: touch.clientX, y: touch.clientY };
      setIsTouching(true);

      // Prevent default to avoid conflicts
      e.preventDefault();
    };

    const handleTouchMove = (e) => {
      if (!isTouching) return;

      const touch = e.touches[0];
      const distance = getDistance(touchStartPos.current, { x: touch.clientX, y: touch.clientY });

      // Start drag if moved enough distance
      if (distance > DRAG_CONSTANTS.DRAG_THRESHOLD) {
        onMobileDragStart(item, touch.clientX, touch.clientY);
        setIsTouching(false);
      }

      // Continue drag if already dragging
      onMobileDragMove(touch.clientX, touch.clientY);

      e.preventDefault();
    };

    const handleTouchEnd = (e) => {
      const touch = e.changedTouches[0];

      if (isTouching) {
        // If still touching (didn't start drag), just reset
        setIsTouching(false);
      } else {
        // If we were dragging, handle the drop
        onMobileDragEnd(touch.clientX, touch.clientY);
      }

      e.preventDefault();
    };

    // Add non-passive event listeners
    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [item, isTouching, onMobileDragStart, onMobileDragMove, onMobileDragEnd]);

  return (
    <div
      ref={itemRef}
      data-furniture-item={item.id}
      className={classNames(
        "bg-white rounded-md md:rounded-lg overflow-hidden cursor-grab transition-transform hover:scale-105 hover:shadow-md aspect-square flex items-center justify-center p-0.5 md:p-1",
        isDragging ? "opacity-50 scale-95" : ""
      )}
      draggable
      onDragStart={(e) => onDragStart(e, item)}
      title={item.name}
      style={{
        touchAction: 'none',
        userSelect: 'none'
      }}
    >
      <img
        src={item.src}
        alt={item.name}
        className="max-w-full max-h-full object-contain pointer-events-none"
      />
    </div>
  );
};

const Sidebar = ({
  isVisible,
  searchTerm,
  setSearchTerm,
  categories,
  activeCategory,
  setActiveCategory,
  filteredItems,
  handleDragStart
}) => {
  // State cho mobile drag and drop
  const [isDragging, setIsDragging] = useState(false);
  const [draggedItem, setDraggedItem] = useState(null);
  const [dragPreview, setDragPreview] = useState({ x: 0, y: 0, visible: false });
  const dragStartPos = useRef({ x: 0, y: 0 });
  const containerRef = useRef(null);

  // Mobile drag and drop handlers
  const handleMobileDragStart = (item, clientX, clientY) => {
    setDraggedItem(item);
    setIsDragging(true);
    setDragPreview({ x: clientX, y: clientY, visible: true });
    dragStartPos.current = { x: clientX, y: clientY };

    // Haptic feedback cho mobile devices
    triggerHapticFeedback(DRAG_CONSTANTS.HAPTIC_START);
    logTouchEvent('dragStart', { item: item.name, x: clientX, y: clientY });
  };

  const handleMobileDragMove = (clientX, clientY) => {
    if (!isDragging || !draggedItem) return;
    setDragPreview({ x: clientX, y: clientY, visible: true });
  };

  const handleMobileDragEnd = (clientX, clientY) => {
    if (!isDragging || !draggedItem) {
      setIsDragging(false);
      setDraggedItem(null);
      setDragPreview({ x: 0, y: 0, visible: false });
      return;
    }

    // Check if dropped on canvas
    const canvasContainer = document.querySelector('[data-canvas-container="true"]');
    if (canvasContainer) {
      const rect = canvasContainer.getBoundingClientRect();
      const isOverCanvas = (
        clientX >= rect.left &&
        clientX <= rect.right &&
        clientY >= rect.top &&
        clientY <= rect.bottom
      );

      if (isOverCanvas) {
        // Dispatch custom event to canvas
        const customDropEvent = new CustomEvent('mobileDrop', {
          detail: {
            item: draggedItem,
            x: clientX - rect.left,
            y: clientY - rect.top
          }
        });
        canvasContainer.dispatchEvent(customDropEvent);

        triggerHapticFeedback(DRAG_CONSTANTS.HAPTIC_DROP);
        logTouchEvent('dropSuccess', { item: draggedItem.name, x: clientX, y: clientY });
      } else {
        logTouchEvent('dropFailed', { item: draggedItem.name, x: clientX, y: clientY });
      }
    }

    // Reset state
    setIsDragging(false);
    setDraggedItem(null);
    setDragPreview({ x: 0, y: 0, visible: false });
  };

  return (
    <>
      {/* Mobile drag preview */}
      {dragPreview.visible && draggedItem && (
        <div
          className="fixed pointer-events-none z-[9999] transform -translate-x-1/2 -translate-y-1/2 transition-all duration-75 ease-out"
          style={{
            left: dragPreview.x,
            top: dragPreview.y,
          }}
        >
          <div className="bg-white rounded-lg shadow-xl p-2 opacity-90 border-2 border-dexin-primary scale-110 animate-pulse">
            <img
              src={draggedItem.src}
              alt={draggedItem.name}
              className="w-16 h-16 object-contain"
            />
            <div className="text-xs text-center mt-1 text-gray-600 font-medium truncate max-w-16">
              {draggedItem.name}
            </div>
          </div>
        </div>
      )}

      <div
        className={classNames(
          "bg-dexin-sidebar flex flex-col transition-all duration-300 flex-shrink-0",
          "fixed left-2 sm:left-4 top-16 sm:top-20 z-50 rounded-xl sm:rounded-2xl md:rounded-3xl shadow-xl",
          isVisible
            ? "opacity-100 p-2 sm:p-3 md:p-4 lg:p-5 w-[250px] h-[400px] sm:w-[280px] sm:h-[450px] md:w-[350px] md:h-[500px] lg:w-[380px] lg:h-[520px] xl:w-[400px] xl:h-[550px] max-h-[calc(100vh-80px)] sm:max-h-[calc(100vh-120px)]"
            : "w-0 h-0 p-0 overflow-hidden opacity-0"
        )}
      >
      {/* Search bar */}
      <div className="mb-1.5 sm:mb-2 md:mb-3 relative">
        <input
          type="text"
          placeholder="Tìm kiếm..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full py-1 sm:py-1.5 md:py-2 pl-6 sm:pl-7 md:pl-8 pr-2 md:pr-3 rounded-full border-none bg-dexin-light-50 text-gray-800 text-xs md:text-sm outline-none"
        />
        <Search className="absolute left-1.5 sm:left-2 top-1/2 transform -translate-y-1/2 text-dark" size={14} />
      </div>

      {/* Categories */}
      <div className="flex flex-wrap gap-1 mb-1.5 sm:mb-2 md:mb-3">
        {categories.map(category => (
          <button
            key={category.id}
            className={classNames(
              "py-0.5 md:py-1 px-1.5 sm:px-2 md:px-3 rounded-full border text-[10px] sm:text-xs transition-all",
              activeCategory === category.id
                ? "bg-dexin-light text-white border-dexin-light"
                : "bg-white border-gray-200 text-gray-800 hover:border-dexin-light hover:text-dexin-light"
            )}
            onClick={() => setActiveCategory(category.id)}
          >
            {category.name}
          </button>
        ))}
      </div>

      {/* Furniture items grid - with fixed height and y-scroll only */}
      <div
        ref={(el) => {
          containerRef.current = el;
        }}
        className="flex-1 overflow-y-auto overflow-x-hidden pr-1 md:pr-1.5 scrollbar scrollbar-thumb-dexin-light scrollbar-track-dexin-light-10"
        style={{
          touchAction: isDragging ? 'none' : 'pan-y' // Cho phép scroll dọc khi không drag
        }}
      >
        <div className="grid grid-cols-3 gap-1 sm:gap-1.5 md:gap-2 lg:gap-3">
          {filteredItems().map(item => (
            <FurnitureItem
              key={item.id}
              item={item}
              isDragging={isDragging && draggedItem?.id === item.id}
              onDragStart={handleDragStart}
              onMobileDragStart={handleMobileDragStart}
              onMobileDragMove={handleMobileDragMove}
              onMobileDragEnd={handleMobileDragEnd}
            />
          ))}
        </div>
      </div>
    </div>
    </>
  );
};

export default Sidebar;
