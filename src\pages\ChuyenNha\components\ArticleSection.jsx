import React from 'react';
import { motion } from 'motion/react';
import { Calendar, User, ArrowDown } from 'lucide-react';
import { Button } from '../../../components/common';

const ArticleSection = () => {
  // Dữ liệu bài viết theo format mẫu
  const featuredArticle = {
    id: 1,
    title: "Góc nhỏ, bình yên và một chút thay đổi",
    content: `<PERSON><PERSON> bao giờ bạn bước vào phòng sau một ngày dài và cảm thấy không gian quanh mình quá đơn điệu, nhưng lại chẳng biết vì sao bạn lại cảm thấy như thế.

Thế là một buổi chiều, mình quyết định thay đổi một chút - không cần gì quá lớn lao. Chỉ là đổi chỗ bàn làm việc để đón nắng sớm hơn, đặt thêm một bình hoa nhỏ, và đổi gối đến từ góc phòng mà thôi. <PERSON><PERSON><PERSON><PERSON> là, chỉ với điều giản đơn mà cảm phòng như thôi thế, nhẹ nhàng đến lạ minh sau những bận bề.

Bạn có một góc nhỏ nào muốn làm mới không? Biết đâu, chỉ cần một sự thay đổi rất nhỏ, bạn đã có thể tìm thấy bình yên trong chính không gian của mình. 😊`,
    author: "@dexinhomyyy",
    date: "14.2.2020, Thứ bảy 16:06",
    avatar: "/images/Avatar.png"
  };

  return (
    <section className="py-16 sm:py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          {/* Article Tag */}
          <div className="inline-flex items-center gap-2 bg-dexin-light-10 text-dexin-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
            <span>Bài viết của tháng</span>
            <span>💖</span>
          </div>

          <h2 className="text-3xl sm:text-4xl font-bold text-dexin-dark mb-4">
            {featuredArticle.title}
          </h2>
        </motion.div>

        {/* Featured Article Card */}
        <motion.article
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto"
        >
          <div className="bg-white rounded-2xl shadow-lg hover:shadow-dexin-light-20 border border-dexin-light overflow-hidden
                         hover:shadow-xl transition-all duration-300">

            {/* Article Header */}
            <div className="p-6 sm:p-8 border-b border-gray-100">
              <div className="flex items-center gap-4 mb-4">
                {/* Author Avatar */}
                <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200">
                  <img
                    src={featuredArticle.avatar}
                    alt={featuredArticle.author}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Author Info */}
                <div className="flex-1">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <User size={14} />
                    <span className="font-medium text-dexin-dark">{featuredArticle.author}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
                    <Calendar size={14} />
                    <span>{featuredArticle.date}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Article Content */}
            <div className="p-6 sm:p-8">
              <div className="prose prose-lg max-w-none">
                {featuredArticle.content.split('\n\n').map((paragraph, index) => (
                  <motion.p
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 * index }}
                    viewport={{ once: true }}
                    className="text-gray-700 leading-relaxed mb-4 last:mb-0"
                  >
                    {paragraph}
                  </motion.p>
                ))}
              </div>

              {/* Read More Button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                viewport={{ once: true }}
                className="flex justify-center mt-8"
              >
                <div className="group">
                  <Button
                    variant="outline"
                    size="lg"
                    className=" border-dexin-primary/20 hover:border-dexin-primary/40
                          transition-all duration-300"
                  >
                    <span>Đọc tiếp</span>
                    <ArrowDown
                      size={16}
                      className="ml-2 group-hover:translate-y-1 transition-transform duration-300"
                    />
                  </Button>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.article>
      </div>
    </section>
  );
};

export default ArticleSection;
