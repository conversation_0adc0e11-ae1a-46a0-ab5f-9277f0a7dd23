import React from 'react';
import { motion } from 'motion/react';
import { HeroSection, ArticleSection, GallerySection } from './components';

const ChuyenNha = () => {
  // Animation variants cho staggered animation
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.25, 1.0]
      }
    }
  };

  return (
    <motion.div
      initial="hidden"
      animate="show"
      variants={container}
      className="min-h-screen bg-white"
    >
      {/* Hero Section */}
      <motion.div variants={item}>
        <HeroSection />
      </motion.div>

      {/* Article Section */}
      <motion.div variants={item}>
        <ArticleSection />
      </motion.div>

      {/* Gallery Section */}
      <motion.div variants={item}>
        <GallerySection />
      </motion.div>
    </motion.div>
  );
};

export default ChuyenNha;
