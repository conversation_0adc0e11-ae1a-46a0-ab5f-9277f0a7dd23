import React, { useState } from 'react';
import { motion } from 'motion/react';
import { MoreHorizontal } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../components/ui/table';
import { Button } from '../../../components/ui/button';

import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '../../../components/ui/pagination';

const CustomerManagement = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Demo data for UI display - replace with API call in production
  const mockCustomers = [
    {
      id: 'DD006SA',
      customerName: '<PERSON><PERSON><PERSON>',
      phone: '0369852874',
      email: '<EMAIL>',
      createdDate: 'Đã tạo cách đây 2 tháng',
      status: 'Bị khoá',
      statusColor: 'text-red-500'
    },
    {
      id: 'DD007SA',
      customerName: 'Hoàng Bảo Ngọc',
      phone: '0369852874',
      email: '<EMAIL>',
      createdDate: 'Đã tạo cách đây 10 ngày',
      status: 'Hoạt động gần đây',
      statusColor: 'text-yellow-500'
    },
    {
      id: 'DD008SA',
      customerName: 'Hoàng Bảo Ngọc',
      phone: '0369852874',
      email: '<EMAIL>',
      createdDate: 'Đã tạo cách đây 1 tháng',
      status: 'Trực tuyến',
      statusColor: 'text-green-500'
    },
    {
      id: 'DD009SA',
      customerName: 'Hoàng Bảo Ngọc',
      phone: '0369852874',
      email: '<EMAIL>',
      createdDate: 'Đã tạo cách đây 20 ngày',
      status: 'Trực tuyến',
      statusColor: 'text-green-500'
    },
    {
      id: 'DD010SA',
      customerName: 'Hoàng Bảo Ngọc',
      phone: '0369852874',
      email: '<EMAIL>',
      createdDate: 'Đã tạo cách đây 5 tháng',
      status: 'Người tuyến',
      statusColor: 'text-gray-500'
    },
    {
      id: 'DD011SA',
      customerName: 'Hoàng Bảo Ngọc',
      phone: '0369852874',
      email: '<EMAIL>',
      createdDate: 'Đã tạo cách đây 12 ngày',
      status: 'Hoạt động gần đây',
      statusColor: 'text-yellow-500'
    },
    {
      id: 'DD012SA',
      customerName: 'Hoàng Bảo Ngọc',
      phone: '0369852874',
      email: '<EMAIL>',
      createdDate: 'Đã tạo cách đây 5 tháng',
      status: 'Chờ phản hồi khách',
      statusColor: 'text-red-500'
    },
    {
      id: 'DD013SA',
      customerName: 'Hoàng Bảo Ngọc',
      phone: '0369852874',
      email: '<EMAIL>',
      createdDate: 'Đã tạo cách đây 20 phút',
      status: 'Bị khoá',
      statusColor: 'text-red-500'
    },
    {
      id: 'DD014SA',
      customerName: 'Hoàng Bảo Ngọc',
      phone: '0369852874',
      email: '<EMAIL>',
      createdDate: 'Đã tạo cách đây 4 tháng',
      status: 'Người tuyến',
      statusColor: 'text-gray-500'
    }
  ];

  const totalPages = Math.ceil(mockCustomers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentItems = mockCustomers.slice(startIndex, startIndex + itemsPerPage);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <motion.div 
      className="p-6 h-full overflow-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Page Title */}
      <CardHeader className="px-0">
        <CardTitle className="text-2xl text-dexin-primary">Khách hàng</CardTitle>
        <CardDescription>Quản lý thông tin khách hàng</CardDescription>
      </CardHeader>

      {/* Table */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mã đơn hàng</TableHead>
                <TableHead>Họ và tên</TableHead>
                <TableHead>SĐT</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Thời gian tạo</TableHead>
                <TableHead>Trạng thái hoạt động</TableHead>
                <TableHead>Thao tác</TableHead>
              </TableRow>
            </TableHeader>

            <TableBody>
              {currentItems.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.id}</TableCell>
                  <TableCell>{item.customerName}</TableCell>
                  <TableCell>{item.phone}</TableCell>
                  <TableCell>{item.email}</TableCell>
                  <TableCell>{item.createdDate}</TableCell>
                  <TableCell>
                    <span
                      className={`text-sm font-medium ${
                        item.status === 'Trực tuyến' ? 'text-green-600' :
                        item.status === 'Hoạt động gần đây' ? 'text-orange-500' :
                        item.status === 'Bị khoá' ? 'text-red-500' :
                        'text-gray-500'
                      }`}
                    >
                      {item.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal size={16} />
                      </Button>
                    </motion.div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      </motion.div>

      {/* Pagination */}
      <motion.div
        className="mt-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>

            {[...Array(totalPages)].map((_, index) => {
              const page = index + 1;
              return (
                <PaginationItem key={page}>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <PaginationLink
                      onClick={() => handlePageChange(page)}
                      isActive={currentPage === page}
                      className={`cursor-pointer ${
                        currentPage === page
                          ? 'bg-dexin-primary text-white hover:bg-dexin-primary/90'
                          : 'hover:bg-dexin-light-10'
                      }`}
                    >
                      {page}
                    </PaginationLink>
                  </motion.div>
                </PaginationItem>
              );
            })}

            <PaginationItem>
              <PaginationNext
                onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
                className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </motion.div>
    </motion.div>
  );
};

export default CustomerManagement;
