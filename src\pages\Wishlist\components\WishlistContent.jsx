import React, { memo, useMemo, useCallback, useState } from 'react';
import { motion } from 'motion/react';
import { Heart, ShoppingCart, Eye, Trash2 } from 'lucide-react';
import { useWishlist } from '../../../context/WishlistContext';
import { useCart } from '../../../context/CartContext';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import ProductVariantModal from '../../../components/common/ProductVariantModal';
import { getProductById } from '../../../data/products';

// Format giá tiền
const formatPrice = (price) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price);
};

// Component hiển thị đánh giá sao
const StarRating = memo(({ rating, reviews }) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;

  return (
    <div className="flex items-center">
      <div className="flex items-center mr-1">
        {[...Array(5)].map((_, i) => (
          <span key={i} className="text-dexin-gold">
            {i < fullStars ? (
              '★'
            ) : i === fullStars && hasHalfStar ? (
              '★'
            ) : (
              '☆'
            )}
          </span>
        ))}
      </div>
      <span className="text-xs text-gray-500">({reviews})</span>
    </div>
  );
});

// Component hiển thị icon
const HeartIcon = memo(() => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
  </svg>
));

const EyeIcon = memo(() => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
    <circle cx="12" cy="12" r="3"></circle>
  </svg>
));

// Product Card Component
const ProductCard = memo(({ product, onOpenVariantModal }) => {
  const { removeFromWishlist } = useWishlist();
  const { addToCart } = useCart();
  const formattedPrice = useMemo(() => formatPrice(product.price), [product.price]);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  const handleRemoveFromWishlist = useCallback((e) => {
    // Ngăn chặn sự kiện lan truyền để không navigate khi xóa sản phẩm
    e.stopPropagation();
    e.preventDefault();
    
    removeFromWishlist(product.id);
    toast.info(`Đã xóa "${product.name}" khỏi mục yêu thích`, {
      icon: () => <span style={{ fontSize: '1.2rem', marginRight: '10px' }}>💔</span>,
      className: 'toast-message'
    });
  }, [product.id, product.name, removeFromWishlist]);

  // Handle Quick View
  const handleQuickView = useCallback((e) => {
    // Ngăn sự kiện click lan ra ngoài
    e.stopPropagation();
    e.preventDefault();
    
    toast.info('Chức năng xem nhanh đang được phát triển', {
      icon: () => <span style={{ fontSize: '1.5rem', marginRight: '10px' }}>👁️</span>,
      className: 'toast-message'
    });
  }, []);

  // Handle Add to Cart
  const handleAddToCart = useCallback((e, product) => {
    // Ngăn sự kiện click lan ra ngoài
    e.stopPropagation();
    e.preventDefault();
    
    // Lấy thông tin sản phẩm đầy đủ từ data
    const fullProduct = getProductById(product.id);
    
    // Kiểm tra xem sản phẩm có variants không
    if (fullProduct && fullProduct.variants && fullProduct.variants.length > 0) {
      // Nếu có variants, mở modal chọn variant
      onOpenVariantModal(fullProduct);
    } else {
      // Nếu không có variants, thêm trực tiếp vào giỏ hàng
      addToCart(product);
      
      toast.success(`Đã thêm "${product.name}" vào giỏ hàng`, {
        icon: () => <span style={{ fontSize: '1.2rem', marginRight: '10px' }}>🛒</span>,
        className: 'toast-message',
        toastId: `wishlist-cart-${product.id}`,
        autoClose: 2000
      });
    }
  }, [addToCart, onOpenVariantModal]);

  return (
    <Link to={`/product/${product.id}`} className="block">
      <div
        className="bg-white rounded-lg shadow-sm overflow-hidden group relative transition-all duration-300 hover:shadow-md"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className="relative">
          {/* Hình ảnh sản phẩm */}
          <div className="overflow-hidden h-64 flex items-center justify-center bg-gray-50">
            <img
              src={product.image}
              alt={product.name}
              className={`w-auto h-auto max-h-60 max-w-[90%] object-contain content-visibility-auto transition-all duration-300 ease-in-out ${imageLoaded ? 'opacity-100' : 'opacity-0'} ${isHovered ? 'scale-105' : 'scale-100'}`}
              loading="lazy"
              decoding="async"
              onLoad={handleImageLoad}
            />
          </div>

          {/* Quick view button - hiển thị khi hover */}
          {imageLoaded && (
            <button
              className="absolute top-2 right-2 p-2 bg-white rounded-full hover:bg-gray-100 transition-all duration-200 opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100"
              aria-label="Quick view"
              onClick={handleQuickView}
            >
              <EyeIcon />
            </button>
          )}

          {/* Add to cart overlay - hiển thị dưới hình ảnh khi hover */}
          <div
            className="w-full bg-dexin-light text-white py-3 text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            onClick={(e) => handleAddToCart(e, product)}
          >
            <button className="flex items-center justify-center w-full">
              <ShoppingCart className="mr-2 h-5 w-5" />
              <span>Thêm vào giỏ hàng</span>
            </button>
          </div>
        </div>

        {/* Thông tin sản phẩm */}
        <div className="p-4">
          <h3 className="text-base font-medium text-gray-800 line-clamp-1">{product.name}</h3>

          {/* Giá */}
          <div className="mt-2 text-base font-bold text-dexin-primary">
            {formattedPrice}
          </div>

          {/* Rating */}
          <div className="flex justify-between items-center">
            <StarRating rating={product.rating} reviews={product.reviews} />

            {/* Nút xóa khỏi danh sách yêu thích */}
            <button
              className="p-1.5 text-dexin-primary hover:text-gray-400 transition-colors duration-200"
              aria-label="Remove from wishlist"
              onClick={handleRemoveFromWishlist}
            >
              <Trash2 size={18} />
            </button>
          </div>
        </div>
      </div>
    </Link>
  );
});

// Empty State Component
const EmptyState = memo(() => {
  return (
    <div className="text-center py-12">
      <div className="inline-flex justify-center items-center w-24 h-24 bg-pink-100 rounded-full mb-6">
        <Heart className="h-12 w-12 text-dexin-primary" />
      </div>
      <h3 className="text-xl font-medium text-gray-800 mb-2">Danh sách yêu thích trống</h3>
      <p className="text-gray-600 mb-6 max-w-md mx-auto">
        Bạn chưa thêm sản phẩm nào vào danh sách yêu thích. Hãy khám phá cửa hàng và thêm các sản phẩm bạn yêu thích.
      </p>
      <Link to="/store">
        <button
          className="bg-dexin-primary text-white px-6 py-2 rounded-full hover:bg-dexin-primary/90 transition-colors"
          onClick={() => {
            toast.success('Chuyển đến trang cửa hàng', {
              icon: () => <span style={{ fontSize: '1.5rem', marginRight: '10px' }}>🛍️</span>,
              className: 'toast-message'
            });
          }}
        >
          Khám phá cửa hàng
        </button>
      </Link>
    </div>
  );
});

// Pagination Component
const Pagination = memo(({ currentPage, setCurrentPage, totalPages }) => {
  const handlePageChange = useCallback((pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    if (pageNumber !== currentPage) {
      toast.info(`Đã chuyển đến trang ${pageNumber}`, {
        icon: () => <span style={{ fontSize: '1.5rem', marginRight: '10px' }}>📄</span>,
        className: 'toast-message',
        autoClose: 2000
      });
    }
  }, [setCurrentPage, currentPage]);

  if (totalPages <= 1) return null;

  return (
    <div className="flex justify-center mt-8 space-x-2">
      {[...Array(totalPages)].map((_, i) => (
        <button
          key={i}
          onClick={() => handlePageChange(i + 1)}
          className={`px-3 py-1 rounded-md transition-colors duration-150 ${
            currentPage === i + 1
              ? 'bg-dexin-primary text-white border border-dexin-primary'
              : 'bg-white border border-gray-300 text-gray-600 hover:bg-dexin-light hover:text-white hover:border-dexin-light'
          }`}
        >
          {i + 1}
        </button>
      ))}
    </div>
  );
});

// Main Content Component
const WishlistContent = ({
  wishlistItems,
  currentPage,
  setCurrentPage,
  totalPages
}) => {
  const { addToCart } = useCart();
  
  // State cho Variant Modal
  const [variantModalOpen, setVariantModalOpen] = useState(false);
  const [selectedProductForVariant, setSelectedProductForVariant] = useState(null);

  // Mở Variant Modal
  const openVariantModal = useCallback((product) => {
    setSelectedProductForVariant(product);
    setVariantModalOpen(true);
  }, []);

  // Đóng Variant Modal
  const closeVariantModal = useCallback(() => {
    setVariantModalOpen(false);
    setTimeout(() => {
      setSelectedProductForVariant(null);
    }, 300);
  }, []);

  // Handle Add to Cart từ Variant Modal
  const handleAddToCartFromModal = useCallback((productWithVariant) => {
    addToCart(productWithVariant, productWithVariant.quantity);
    
    const displayName = productWithVariant.variantName 
      ? `${productWithVariant.name} (${productWithVariant.variantName})`
      : productWithVariant.name;
      
    toast.success(`Đã thêm "${displayName}" vào giỏ hàng`, {
      icon: () => <span style={{ fontSize: '1.2rem', marginRight: '10px' }}>🛒</span>,
      className: 'toast-message',
      toastId: `wishlist-variant-cart-${productWithVariant.id}-${productWithVariant.variantId}`,
      autoClose: 2000
    });
  }, [addToCart]);

  if (wishlistItems.length === 0) {
    return <EmptyState />;
  }

  return (
    <div>
      <h2 className="text-xl font-semibold whitespace-nowrap mb-6">Sản phẩm yêu thích của bạn ❤️</h2>

      {/* Products Grid - 3 sản phẩm trên một hàng */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {wishlistItems.map((product, index) => (
          <motion.div
            key={product.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <ProductCard product={product} onOpenVariantModal={openVariantModal} />
          </motion.div>
        ))}
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
      />

      {/* Product Variant Modal */}
      <ProductVariantModal
        isOpen={variantModalOpen}
        onClose={closeVariantModal}
        product={selectedProductForVariant}
        onAddToCart={handleAddToCartFromModal}
      />
    </div>
  );
};

export default WishlistContent;
