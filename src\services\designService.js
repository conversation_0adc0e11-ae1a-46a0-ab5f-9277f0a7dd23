import axios from 'axios';

// Base URL cho API
const API_BASE_URL = 'https://dexin.onrender.com/api';

// Tạo axios instance với cấu hình chung
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor để thêm token
apiClient.interceptors.request.use(
  (config) => {
    // Sử dụng đúng token key như AuthContext
    const token = localStorage.getItem('dexin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor để xử lý lỗi chung - KHÔNG tự động redirect
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);

    // Không tự động redirect, để component xử lý
    // if (error.response?.status === 401) {
    //   // Token expired hoặc không hợp lệ
    //   localStorage.removeItem('dexin_token');
    //   localStorage.removeItem('dexin_user');
    //   window.location.href = '/login';
    // }

    return Promise.reject(error);
  }
);

/**
 * Design Service - Xử lý các API liên quan đến thiết kế
 */
export const designService = {
  /**
   * Lấy tất cả thiết kế
   * @returns {Promise<Object>} Response với danh sách thiết kế
   */
  async getAllDesigns() {
    try {
      console.log('🔄 Fetching all designs...');
      const response = await apiClient.get('/Designs/getAll');
      
      console.log('✅ Get all designs successful:', response.data);
      return {
        success: true,
        data: response.data.data || [],
        message: response.data.message || 'Lấy danh sách thiết kế thành công'
      };
    } catch (error) {
      console.error('❌ Get all designs error:', error);
      return {
        success: false,
        data: [],
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi lấy danh sách thiết kế'
      };
    }
  },

  /**
   * Tạo thiết kế 3D mới
   * @param {Object} designData - Dữ liệu thiết kế
   * @param {string} designData.title - Tiêu đề thiết kế
   * @param {File} designData.pathUrl - File thiết kế 3D
   * @param {File} designData.thumbnailUrl - File thumbnail
   * @param {string} designData.note - Ghi chú
   * @param {number} designData.price - Giá
   * @param {number} designData.customerId - ID khách hàng
   * @returns {Promise<Object>} Response với thông tin thiết kế đã tạo
   */
  async create3DDesign(designData) {
    try {
      console.log('🔄 Creating 3D design...', designData);

      // Lấy token từ localStorage với đúng key
      const token = localStorage.getItem('dexin_token');
      if (!token) {
        throw new Error('Không tìm thấy token. Vui lòng đăng nhập lại.');
      }

      // Tạo FormData cho multipart upload
      const formData = new FormData();

      // Thử các tên field khác nhau để match với server expectation
      formData.append('Title', designData.title);
      formData.append('PathUrl', designData.pathUrl);
      formData.append('ThumbnailUrl', designData.thumbnailUrl);
      formData.append('Note', designData.note || '');
      formData.append('Price', designData.price.toString());
      formData.append('CustomerId', designData.customerId.toString());

      // Log FormData content for debugging
      console.log('📤 FormData contents:');
      for (let [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`${key}:`, { name: value.name, size: value.size, type: value.type });
        } else {
          console.log(`${key}:`, value);
        }
      }

      // Kiểm tra xem có file nào bị null không
      console.log('🔍 File validation:');
      console.log('PathUrl file:', formData.pathUrl instanceof File ? 'Valid' : 'Invalid');
      console.log('ThumbnailUrl file:', formData.thumbnailUrl instanceof File ? 'Valid' : 'Invalid');

      // Gửi request với headers phù hợp cho multipart
      const response = await apiClient.post('/Designs/create-3d', formData, {
        headers: {
          // Xóa Content-Type để axios tự động set multipart/form-data với boundary
          'Content-Type': undefined,
          'Authorization': `Bearer ${token}`,
        },
      });

      console.log('✅ Create 3D design successful:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Tạo thiết kế 3D thành công!'
      };
    } catch (error) {
      console.error('❌ Create 3D design error:', error);

      // Xử lý lỗi cụ thể
      if (error.response?.status === 401) {
        console.error('🚫 Unauthorized - Token có thể đã hết hạn');
        // Không tự động redirect, để component xử lý
        return {
          success: false,
          data: null,
          message: 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.'
        };
      }

      if (error.response?.status === 403) {
        return {
          success: false,
          data: null,
          message: 'Bạn không có quyền thực hiện thao tác này.'
        };
      }

      return {
        success: false,
        data: null,
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi tạo thiết kế 3D'
      };
    }
  },

  /**
   * Lấy danh sách tất cả khách hàng
   * @returns {Promise<Object>} Response với danh sách khách hàng
   */
  async getAllCustomers() {
    try {
      console.log('🔄 Fetching all customers...');
      const response = await apiClient.get('/SystemUserAccounts/get-all-customers');
      
      console.log('✅ Get all customers successful:', response.data);
      return {
        success: true,
        data: response.data.data || [],
        message: response.data.message || 'Lấy danh sách khách hàng thành công'
      };
    } catch (error) {
      console.error('❌ Get all customers error:', error);
      return {
        success: false,
        data: [],
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi lấy danh sách khách hàng'
      };
    }
  },

  /**
   * Cập nhật trạng thái thiết kế
   * @param {number} designId - ID thiết kế
   * @param {string} status - Trạng thái mới
   * @returns {Promise<Object>} Response
   */
  async updateDesignStatus(designId, status) {
    try {
      console.log('🔄 Updating design status...', { designId, status });
      const response = await apiClient.put(`/Designs/${designId}/status`, { status });
      
      console.log('✅ Update design status successful:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Cập nhật trạng thái thành công'
      };
    } catch (error) {
      console.error('❌ Update design status error:', error);
      return {
        success: false,
        data: null,
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi cập nhật trạng thái'
      };
    }
  },

  /**
   * Xóa thiết kế
   * @param {number} designId - ID thiết kế
   * @returns {Promise<Object>} Response
   */
  async deleteDesign(designId) {
    try {
      console.log('🔄 Deleting design...', designId);
      const response = await apiClient.delete(`/Designs/${designId}`);

      console.log('✅ Delete design successful:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Xóa thiết kế thành công'
      };
    } catch (error) {
      console.error('❌ Delete design error:', error);
      return {
        success: false,
        data: null,
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi xóa thiết kế'
      };
    }
  },

  /**
   * Lấy danh sách thiết kế của user
   * @param {number} userId - ID của user
   * @returns {Promise<Object>} Response với danh sách thiết kế
   */
  async getDesignsByUser(userId) {
    try {
      console.log('🔄 Fetching designs by user...', userId);
      const response = await apiClient.get(`/Designs/getByUser/${userId}`);

      console.log('✅ Get designs by user successful:', response.data);
      return {
        success: true,
        data: response.data.data || [],
        message: response.data.message || 'Lấy danh sách thiết kế thành công'
      };
    } catch (error) {
      console.error('❌ Get designs by user error:', error);
      return {
        success: false,
        data: [],
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi lấy danh sách thiết kế'
      };
    }
  }
};

export default designService;
