import React from 'react';
import { motion } from 'motion/react';
import { CheckCircle, Eye, Download, X, FileText, DollarSign, User, Calendar } from 'lucide-react';

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../../components/ui/dialog';
import { Button } from '../../../components/ui/button';
import { Card, CardContent } from '../../../components/ui/card';
import { Badge } from '../../../components/ui/badge';

const DesignSuccessDialog = ({ isOpen, onClose, designData, customerInfo }) => {
  if (!designData) return null;

  // Format giá tiền
  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  // Xử lý xem file thiết kế
  const handleViewDesign = () => {
    if (designData.pathUrl) {
      window.open(designData.pathUrl, '_blank');
    }
  };

  // Xử lý xem thumbnail
  const handleViewThumbnail = () => {
    if (designData.thumbnailUrl) {
      window.open(designData.thumbnailUrl, '_blank');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-green-600 flex items-center space-x-2">
            <CheckCircle className="w-6 h-6" />
            <span>Tạo thiết kế thành công!</span>
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-6"
        >
          {/* Success Message */}
          <div className="text-center py-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, type: "spring" }}
              className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <CheckCircle className="w-8 h-8 text-green-600" />
            </motion.div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Thiết kế đã được tạo thành công!
            </h3>
            <p className="text-gray-600">
              Thiết kế của bạn đã được lưu và sẵn sàng để sử dụng.
            </p>
          </div>

          {/* Design Details */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                <FileText className="w-4 h-4" />
                <span>Thông tin thiết kế</span>
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Tiêu đề */}
                <div>
                  <label className="text-sm font-medium text-gray-500">Tiêu đề</label>
                  <p className="text-gray-900 font-medium">{designData.title}</p>
                </div>

                {/* Giá */}
                <div>
                  <label className="text-sm font-medium text-gray-500 flex items-center space-x-1">
                    <DollarSign className="w-3 h-3" />
                    <span>Giá</span>
                  </label>
                  <p className="text-green-600 font-semibold text-lg">
                    {formatPrice(designData.price)}
                  </p>
                </div>

                {/* Khách hàng */}
                {designData.customerInfo && (
                  <div>
                    <label className="text-sm font-medium text-gray-500 flex items-center space-x-1">
                      <User className="w-3 h-3" />
                      <span>Khách hàng</span>
                    </label>
                    <p className="text-gray-900 font-medium">
                      {`${designData.customerInfo.firstName} ${designData.customerInfo.lastName}`.trim() || designData.customerInfo.userName}
                    </p>
                    <p className="text-xs text-gray-500">{designData.customerInfo.email}</p>
                  </div>
                )}

                {/* Staff tạo */}
                <div>
                  <label className="text-sm font-medium text-gray-500 flex items-center space-x-1">
                    <User className="w-3 h-3" />
                    <span>Được tạo bởi</span>
                  </label>
                  <p className="text-gray-900">Staff ID: {designData.createdBy}</p>
                </div>

                {/* Thời gian tạo */}
                <div>
                  <label className="text-sm font-medium text-gray-500 flex items-center space-x-1">
                    <Calendar className="w-3 h-3" />
                    <span>Thời gian tạo</span>
                  </label>
                  <p className="text-gray-900">
                    {new Date().toLocaleString('vi-VN')}
                  </p>
                </div>
              </div>

              {/* Ghi chú */}
              {designData.note && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Ghi chú</label>
                  <p className="text-gray-900 bg-gray-50 p-3 rounded-lg mt-1">
                    {designData.note}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Files Preview */}
          <Card>
            <CardContent className="p-6">
              <h4 className="font-semibold text-gray-900 mb-4">Files đã upload</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* File thiết kế 3D */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-gray-900">File thiết kế 3D</h5>
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      Đã upload
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600 break-all">
                      {designData.pathUrl}
                    </p>
                    <Button
                      onClick={handleViewDesign}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Xem file
                    </Button>
                  </div>
                </div>

                {/* Thumbnail */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-gray-900">Ảnh thumbnail</h5>
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      Đã upload
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    {/* Thumbnail preview */}
                    <div className="w-full h-32 bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={designData.thumbnailUrl}
                        alt="Thumbnail"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                      <div className="w-full h-full hidden items-center justify-center text-gray-400">
                        <span className="text-sm">Không thể tải ảnh</span>
                      </div>
                    </div>
                    <Button
                      onClick={handleViewThumbnail}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Xem ảnh gốc
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              onClick={onClose}
              className="bg-dexin-primary hover:bg-dexin-primary/90"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Hoàn tất
            </Button>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};

export default DesignSuccessDialog;
