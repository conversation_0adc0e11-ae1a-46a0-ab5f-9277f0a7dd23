import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'motion/react';
import { Camera, Upload, X, Check, AlertCircle } from 'lucide-react';
import { Avatar, AvatarImage, AvatarFallback } from '../ui/avatar';
import { Button } from '../ui/button';
import { useAuth } from '../../context/AuthContext';
import { toast } from 'react-toastify';
import {
  validateImageFile,
  compressImage,
  createImagePreview,
  cleanupImagePreview,
  formatFileSize,
  SUPPORTED_IMAGE_FORMATS,
  MAX_FILE_SIZE
} from '../../utils/uploadUtils';

const AvatarUpload = ({ 
  size = 'lg', 
  showUploadButton = true,
  className = '',
  onUploadSuccess,
  onUploadError 
}) => {
  const { user, uploadAvatar, loading, getDisplayName, getAvatarUrl } = useAuth();
  const [previewUrl, setPreviewUrl] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  // Tạo initials từ tên user
  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Size configurations
  const sizeConfig = {
    sm: { avatar: 'w-16 h-16', button: 'w-6 h-6', icon: 'w-3 h-3' },
    md: { avatar: 'w-24 h-24', button: 'w-8 h-8', icon: 'w-4 h-4' },
    lg: { avatar: 'w-32 h-32', button: 'w-10 h-10', icon: 'w-5 h-5' },
    xl: { avatar: 'w-40 h-40', button: 'w-12 h-12', icon: 'w-6 h-6' }
  };

  const config = sizeConfig[size] || sizeConfig.lg;

  // Handle file selection
  const handleFileSelect = useCallback(async (files) => {
    const file = files[0];
    if (!file) return;

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    try {
      // Compress image
      const compressedFile = await compressImage(file);
      
      // Create preview
      const preview = createImagePreview(compressedFile);
      
      setSelectedFile(compressedFile);
      setPreviewUrl(preview);
      
      toast.success(`Đã chọn ảnh (${formatFileSize(compressedFile.size)})`);
    } catch (error) {
      console.error('File processing error:', error);
      toast.error(error.message || 'Có lỗi xảy ra khi xử lý ảnh');
    }
  }, []);

  // Handle upload
  const handleUpload = useCallback(async () => {
    if (!selectedFile) {
      toast.error('Vui lòng chọn ảnh trước');
      return;
    }

    setIsUploading(true);

    try {
      console.log('🔄 Starting avatar upload...', {
        fileName: selectedFile.name,
        fileSize: selectedFile.size,
        fileType: selectedFile.type
      });

      const result = await uploadAvatar(selectedFile);

      console.log('📤 Upload result:', result);

      if (result.success) {
        toast.success(result.message);

        // Small delay để đảm bảo UI có thời gian cập nhật
        await new Promise(resolve => setTimeout(resolve, 500));

        // Cleanup
        cleanupImagePreview(previewUrl);
        setPreviewUrl(null);
        setSelectedFile(null);

        // Callback
        if (onUploadSuccess) {
          onUploadSuccess(result);
        }

        console.log('✅ Avatar upload completed successfully');
      } else {
        console.error('❌ Upload failed:', result.message);
        toast.error(result.message);
        if (onUploadError) {
          onUploadError(result);
        }
      }
    } catch (error) {
      console.error('💥 Upload error:', error);
      toast.error('Có lỗi xảy ra khi upload ảnh');
      if (onUploadError) {
        onUploadError({ success: false, message: error.message });
      }
    } finally {
      setIsUploading(false);
    }
  }, [selectedFile, uploadAvatar, previewUrl, onUploadSuccess, onUploadError]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    if (previewUrl) {
      cleanupImagePreview(previewUrl);
    }
    setPreviewUrl(null);
    setSelectedFile(null);
  }, [previewUrl]);

  // Dropzone configuration
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: handleFileSelect,
    accept: {
      'image/*': SUPPORTED_IMAGE_FORMATS.map(format => format.replace('image/', '.'))
    },
    maxSize: MAX_FILE_SIZE,
    multiple: false,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
    onDropAccepted: () => setDragActive(false),
    onDropRejected: (rejectedFiles) => {
      setDragActive(false);
      const file = rejectedFiles[0];
      if (file.errors[0]?.code === 'file-too-large') {
        toast.error('File quá lớn. Vui lòng chọn file nhỏ hơn 5MB');
      } else if (file.errors[0]?.code === 'file-invalid-type') {
        toast.error('Định dạng file không được hỗ trợ');
      } else {
        toast.error('File không hợp lệ');
      }
    }
  });

  const currentAvatarUrl = previewUrl || getAvatarUrl();
  const displayName = getDisplayName();

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      {/* Avatar Display */}
      <div className="relative group">
        <div
          {...getRootProps()}
          className={`
            relative cursor-pointer transition-all duration-200
            ${config.avatar}
            ${(isDragActive || dragActive) ? 'scale-105 ring-4 ring-dexin-primary/30' : ''}
          `}
        >
          <input {...getInputProps()} />
          
          <Avatar className={`${config.avatar} border-4 border-white shadow-lg transition-all duration-200 group-hover:shadow-xl`}>
            <AvatarImage
              src={currentAvatarUrl}
              alt={displayName || 'User Avatar'}
              className="object-cover"
            />
            <AvatarFallback className="bg-dexin-primary text-white text-lg font-semibold">
              {getInitials(displayName)}
            </AvatarFallback>
          </Avatar>

          {/* Upload Overlay */}
          <div className="absolute inset-0 bg-black/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
            <Camera className={`${config.icon} text-white`} />
          </div>

          {/* Upload Button */}
          <div className={`absolute -bottom-1 -right-1 ${config.button} bg-dexin-primary rounded-full border-4 border-white shadow-lg flex items-center justify-center cursor-pointer hover:bg-dexin-primary/90 transition-colors`}>
            <Upload className={`${config.icon} text-white`} />
          </div>
        </div>

        {/* Drag Active Indicator */}
        <AnimatePresence>
          {(isDragActive || dragActive) && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute inset-0 bg-dexin-primary/20 rounded-full border-2 border-dashed border-dexin-primary flex items-center justify-center"
            >
              <Upload className={`${config.icon} text-dexin-primary`} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* File Info & Actions */}
      <AnimatePresence>
        {selectedFile && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex flex-col items-center space-y-3 p-4 bg-gray-50 rounded-lg border"
          >
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <AlertCircle className="w-4 h-4" />
              <span>{selectedFile.name}</span>
              <span>({formatFileSize(selectedFile.size)})</span>
            </div>
            
            <div className="flex space-x-2">
              <Button
                onClick={handleUpload}
                disabled={isUploading || loading}
                size="sm"
                className="bg-dexin-primary hover:bg-dexin-primary/90"
              >
                {isUploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Đang upload...
                  </>
                ) : (
                  <>
                    <Check className="w-4 h-4 mr-2" />
                    Cập nhật
                  </>
                )}
              </Button>
              
              <Button
                onClick={handleCancel}
                disabled={isUploading}
                variant="outline"
                size="sm"
              >
                <X className="w-4 h-4 mr-2" />
                Hủy
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

    
    </div>
  );
};

export default AvatarUpload;
