import React from 'react';
import { motion } from 'motion/react';
import { ChatSidebar } from './index';

const ChatList = React.memo(({
  conversations,
  selectedUser,
  showChatDetail,
  handleAdvisorIconClick
}) => (
  <div className="flex h-screen bg-white">
    {/* Sidebar bên trái với danh sách chat */}
    <ChatSidebar
      isEmpty={false}
      conversations={conversations}
      selectedConversation={selectedUser}
      onSelectConversation={showChatDetail}
      onStartChat={handleAdvisorIconClick}
      className="w-full sm:w-80 lg:w-96 xl:w-80 h-full flex-shrink-0"
    />

    {/* Phần nội dung chính bên phải - Hiển thị hình ảnh tư vấn viên */}
    <motion.div
      className="hidden sm:flex flex-grow bg-gray-50 items-center justify-center overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-3xl lg:max-w-4xl xl:max-w-3xl w-full flex flex-col items-center px-6 lg:px-8 xl:px-12">
        <motion.div
          className="text-center"
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="mb-6 lg:mb-8 xl:mb-6"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            whileHover={{ scale: 1.05, rotate: 2 }}
          >
            <motion.img
              src="/images/ty2.png"
              alt="Tư vấn viên"
              className="max-h-[350px] lg:max-h-[400px] xl:max-h-[380px] w-auto object-contain mx-auto"
              drag
              dragConstraints={{
                top: -30,
                left: -30,
                right: 30,
                bottom: 30,
              }}
              dragElastic={0.4}
            />
          </motion.div>
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-4xl xl:text-3xl text-dexin-primary font-bold leading-tight"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <motion.span
              className="block mb-1 lg:mb-2"
              initial={{ x: -30, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.6 }}
            >
              Yayyyy
            </motion.span>
            <motion.span
              className="block mb-1 lg:mb-2"
              initial={{ x: 30, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.8 }}
            >
              Có tin nhắn mới
            </motion.span>
            <motion.span
              className="block mb-3 lg:mb-4"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 1 }}
            >
              check ngay thôi!
            </motion.span>
          </motion.h2>

          <motion.p
            className="text-lg sm:text-xl lg:text-xl xl:text-lg text-gray-800 font-medium mt-6 lg:mt-8 xl:mt-6 cursor-pointer hover:text-dexin-primary transition-colors leading-relaxed max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4, delay: 1.2 }}
            whileHover={{ scale: 1.02, color: "#FE7CAB" }}
            whileTap={{ scale: 0.98 }}
          >
            Bấm "Chat với tư vấn viên" ngay để cùng nhau tạo nên một không gian thật chill nha! <motion.span
              className="ml-1 text-xl lg:text-2xl"
              animate={{
                rotate: [0, 15, 0, -15, 0],
                scale: [1, 1.2, 1, 1.2, 1]
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                repeatDelay: 1
              }}
            >😊</motion.span>
          </motion.p>
        </motion.div>
      </div>
    </motion.div>
  </div>
));

export default ChatList;