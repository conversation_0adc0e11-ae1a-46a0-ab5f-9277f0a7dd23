import{c as e,r as t,x as n,j as r,ab as a,ac as s,z as o,a9 as i,N as l,m as c,W as d,a as u,O as f,P as m,Q as h,ad as p,G as x,w as g,ae as v,af as y,ag as w,ah as b,ai as j,aj as N,ak as C,al as S,am as R,an as E,ao as k,X as D,R as T,b as A,l as P,U as I,C as O,E as M,y as L,a0 as _,ap as F,a6 as H,aq as B,ar as U,as as W,at as z,au as V}from"./index-DdBL2cja.js";import{B as K,c as $,b as q,d as X}from"./designService--nHfI8ad.js";import{C as Y}from"./check-DdKjhOwX.js";import{C as G}from"./chevron-up-A3k7AcMu.js";import{F as Z}from"./file-text-DkNzifT1.js";import{P as Q}from"./plus-66Jg-RVc.js";import{E as J}from"./ellipsis-DahYom63.js";
/**
 * @license lucide-react v0.484.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=e("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),te=e("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),ne=e("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),re=e("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);function ae(){return"undefined"!=typeof window}function se(e){return le(e)?(e.nodeName||"").toLowerCase():"#document"}function oe(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ie(e){var t;return null==(t=(le(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function le(e){return!!ae()&&(e instanceof Node||e instanceof oe(e).Node)}function ce(e){return!!ae()&&(e instanceof Element||e instanceof oe(e).Element)}function de(e){return!!ae()&&(e instanceof HTMLElement||e instanceof oe(e).HTMLElement)}function ue(e){return!(!ae()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof oe(e).ShadowRoot)}function fe(e){const{overflow:t,overflowX:n,overflowY:r,display:a}=ve(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(a)}function me(e){return["table","td","th"].includes(se(e))}function he(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function pe(e){const t=xe(),n=ce(e)?ve(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function xe(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function ge(e){return["html","body","#document"].includes(se(e))}function ve(e){return oe(e).getComputedStyle(e)}function ye(e){return ce(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function we(e){if("html"===se(e))return e;const t=e.assignedSlot||e.parentNode||ue(e)&&e.host||ie(e);return ue(t)?t.host:t}function be(e){const t=we(e);return ge(t)?e.ownerDocument?e.ownerDocument.body:e.body:de(t)&&fe(t)?t:be(t)}function je(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const a=be(e),s=a===(null==(r=e.ownerDocument)?void 0:r.body),o=oe(a);if(s){const e=Ne(o);return t.concat(o,o.visualViewport||[],fe(a)?a:[],e&&n?je(e):[])}return t.concat(a,je(a,[],n))}function Ne(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}const Ce=["top","right","bottom","left"],Se=Math.min,Re=Math.max,Ee=Math.round,ke=Math.floor,De=e=>({x:e,y:e}),Te={left:"right",right:"left",bottom:"top",top:"bottom"},Ae={start:"end",end:"start"};function Pe(e,t,n){return Re(e,Se(t,n))}function Ie(e,t){return"function"==typeof e?e(t):e}function Oe(e){return e.split("-")[0]}function Me(e){return e.split("-")[1]}function Le(e){return"x"===e?"y":"x"}function _e(e){return"y"===e?"height":"width"}function Fe(e){return["top","bottom"].includes(Oe(e))?"y":"x"}function He(e){return Le(Fe(e))}function Be(e){return e.replace(/start|end/g,(e=>Ae[e]))}function Ue(e){return e.replace(/left|right|bottom|top/g,(e=>Te[e]))}function We(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function ze(e){const{x:t,y:n,width:r,height:a}=e;return{width:r,height:a,top:n,left:t,right:t+r,bottom:n+a,x:t,y:n}}function Ve(e,t,n){let{reference:r,floating:a}=e;const s=Fe(t),o=He(t),i=_e(o),l=Oe(t),c="y"===s,d=r.x+r.width/2-a.width/2,u=r.y+r.height/2-a.height/2,f=r[i]/2-a[i]/2;let m;switch(l){case"top":m={x:d,y:r.y-a.height};break;case"bottom":m={x:d,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:u};break;case"left":m={x:r.x-a.width,y:u};break;default:m={x:r.x,y:r.y}}switch(Me(t)){case"start":m[o]-=f*(n&&c?-1:1);break;case"end":m[o]+=f*(n&&c?-1:1)}return m}async function Ke(e,t){var n;void 0===t&&(t={});const{x:r,y:a,platform:s,rects:o,elements:i,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:f=!1,padding:m=0}=Ie(t,e),h=We(m),p=i[f?"floating"===u?"reference":"floating":u],x=ze(await s.getClippingRect({element:null==(n=await(null==s.isElement?void 0:s.isElement(p)))||n?p:p.contextElement||await(null==s.getDocumentElement?void 0:s.getDocumentElement(i.floating)),boundary:c,rootBoundary:d,strategy:l})),g="floating"===u?{x:r,y:a,width:o.floating.width,height:o.floating.height}:o.reference,v=await(null==s.getOffsetParent?void 0:s.getOffsetParent(i.floating)),y=await(null==s.isElement?void 0:s.isElement(v))&&await(null==s.getScale?void 0:s.getScale(v))||{x:1,y:1},w=ze(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:g,offsetParent:v,strategy:l}):g);return{top:(x.top-w.top+h.top)/y.y,bottom:(w.bottom-x.bottom+h.bottom)/y.y,left:(x.left-w.left+h.left)/y.x,right:(w.right-x.right+h.right)/y.x}}function $e(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function qe(e){return Ce.some((t=>e[t]>=0))}function Xe(e){const t=ve(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const a=de(e),s=a?e.offsetWidth:n,o=a?e.offsetHeight:r,i=Ee(n)!==s||Ee(r)!==o;return i&&(n=s,r=o),{width:n,height:r,$:i}}function Ye(e){return ce(e)?e:e.contextElement}function Ge(e){const t=Ye(e);if(!de(t))return De(1);const n=t.getBoundingClientRect(),{width:r,height:a,$:s}=Xe(t);let o=(s?Ee(n.width):n.width)/r,i=(s?Ee(n.height):n.height)/a;return o&&Number.isFinite(o)||(o=1),i&&Number.isFinite(i)||(i=1),{x:o,y:i}}const Ze=De(0);function Qe(e){const t=oe(e);return xe()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Ze}function Je(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const a=e.getBoundingClientRect(),s=Ye(e);let o=De(1);t&&(r?ce(r)&&(o=Ge(r)):o=Ge(e));const i=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==oe(e))&&t}(s,n,r)?Qe(s):De(0);let l=(a.left+i.x)/o.x,c=(a.top+i.y)/o.y,d=a.width/o.x,u=a.height/o.y;if(s){const e=oe(s),t=r&&ce(r)?oe(r):r;let n=e,a=Ne(n);for(;a&&r&&t!==n;){const e=Ge(a),t=a.getBoundingClientRect(),r=ve(a),s=t.left+(a.clientLeft+parseFloat(r.paddingLeft))*e.x,o=t.top+(a.clientTop+parseFloat(r.paddingTop))*e.y;l*=e.x,c*=e.y,d*=e.x,u*=e.y,l+=s,c+=o,n=oe(a),a=Ne(n)}}return ze({width:d,height:u,x:l,y:c})}function et(e,t){const n=ye(e).scrollLeft;return t?t.left+n:Je(ie(e)).left+n}function tt(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:et(e,r)),y:r.top+t.scrollTop}}function nt(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=oe(e),r=ie(e),a=n.visualViewport;let s=r.clientWidth,o=r.clientHeight,i=0,l=0;if(a){s=a.width,o=a.height;const e=xe();(!e||e&&"fixed"===t)&&(i=a.offsetLeft,l=a.offsetTop)}return{width:s,height:o,x:i,y:l}}(e,n);else if("document"===t)r=function(e){const t=ie(e),n=ye(e),r=e.ownerDocument.body,a=Re(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=Re(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let o=-n.scrollLeft+et(e);const i=-n.scrollTop;return"rtl"===ve(r).direction&&(o+=Re(t.clientWidth,r.clientWidth)-a),{width:a,height:s,x:o,y:i}}(ie(e));else if(ce(t))r=function(e,t){const n=Je(e,!0,"fixed"===t),r=n.top+e.clientTop,a=n.left+e.clientLeft,s=de(e)?Ge(e):De(1);return{width:e.clientWidth*s.x,height:e.clientHeight*s.y,x:a*s.x,y:r*s.y}}(t,n);else{const n=Qe(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ze(r)}function rt(e,t){const n=we(e);return!(n===t||!ce(n)||ge(n))&&("fixed"===ve(n).position||rt(n,t))}function at(e,t,n){const r=de(t),a=ie(t),s="fixed"===n,o=Je(e,!0,s,t);let i={scrollLeft:0,scrollTop:0};const l=De(0);function c(){l.x=et(a)}if(r||!r&&!s)if(("body"!==se(t)||fe(a))&&(i=ye(t)),r){const e=Je(t,!0,s,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else a&&c();s&&!r&&a&&c();const d=!a||r||s?De(0):tt(a,i);return{x:o.left+i.scrollLeft-l.x-d.x,y:o.top+i.scrollTop-l.y-d.y,width:o.width,height:o.height}}function st(e){return"static"===ve(e).position}function ot(e,t){if(!de(e)||"fixed"===ve(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ie(e)===n&&(n=n.ownerDocument.body),n}function it(e,t){const n=oe(e);if(he(e))return n;if(!de(e)){let t=we(e);for(;t&&!ge(t);){if(ce(t)&&!st(t))return t;t=we(t)}return n}let r=ot(e,t);for(;r&&me(r)&&st(r);)r=ot(r,t);return r&&ge(r)&&st(r)&&!pe(r)?n:r||function(e){let t=we(e);for(;de(t)&&!ge(t);){if(pe(t))return t;if(he(t))return null;t=we(t)}return null}(e)||n}const lt={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:a}=e;const s="fixed"===a,o=ie(r),i=!!t&&he(t.floating);if(r===o||i&&s)return n;let l={scrollLeft:0,scrollTop:0},c=De(1);const d=De(0),u=de(r);if((u||!u&&!s)&&(("body"!==se(r)||fe(o))&&(l=ye(r)),de(r))){const e=Je(r);c=Ge(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}const f=!o||u||s?De(0):tt(o,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+f.x,y:n.y*c.y-l.scrollTop*c.y+d.y+f.y}},getDocumentElement:ie,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:a}=e;const s=[..."clippingAncestors"===n?he(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=je(e,[],!1).filter((e=>ce(e)&&"body"!==se(e))),a=null;const s="fixed"===ve(e).position;let o=s?we(e):e;for(;ce(o)&&!ge(o);){const t=ve(o),n=pe(o);n||"fixed"!==t.position||(a=null),(s?!n&&!a:!n&&"static"===t.position&&a&&["absolute","fixed"].includes(a.position)||fe(o)&&!n&&rt(e,o))?r=r.filter((e=>e!==o)):a=t,o=we(o)}return t.set(e,r),r}(t,this._c):[].concat(n),r],o=s[0],i=s.reduce(((e,n)=>{const r=nt(t,n,a);return e.top=Re(r.top,e.top),e.right=Se(r.right,e.right),e.bottom=Se(r.bottom,e.bottom),e.left=Re(r.left,e.left),e}),nt(t,o,a));return{width:i.right-i.left,height:i.bottom-i.top,x:i.left,y:i.top}},getOffsetParent:it,getElementRects:async function(e){const t=this.getOffsetParent||it,n=this.getDimensions,r=await n(e.floating);return{reference:at(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=Xe(e);return{width:t,height:n}},getScale:Ge,isElement:ce,isRTL:function(e){return"rtl"===ve(e).direction}};function ct(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}const dt=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:a,y:s,placement:o,middlewareData:i}=t,l=await async function(e,t){const{placement:n,platform:r,elements:a}=e,s=await(null==r.isRTL?void 0:r.isRTL(a.floating)),o=Oe(n),i=Me(n),l="y"===Fe(n),c=["left","top"].includes(o)?-1:1,d=s&&l?-1:1,u=Ie(t,e);let{mainAxis:f,crossAxis:m,alignmentAxis:h}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return i&&"number"==typeof h&&(m="end"===i?-1*h:h),l?{x:m*d,y:f*c}:{x:f*c,y:m*d}}(t,e);return o===(null==(n=i.offset)?void 0:n.placement)&&null!=(r=i.arrow)&&r.alignmentOffset?{}:{x:a+l.x,y:s+l.y,data:{...l,placement:o}}}}},ut=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:a}=t,{mainAxis:s=!0,crossAxis:o=!1,limiter:i={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=Ie(e,t),c={x:n,y:r},d=await Ke(t,l),u=Fe(Oe(a)),f=Le(u);let m=c[f],h=c[u];if(s){const e="y"===f?"bottom":"right";m=Pe(m+d["y"===f?"top":"left"],m,m-d[e])}if(o){const e="y"===u?"bottom":"right";h=Pe(h+d["y"===u?"top":"left"],h,h-d[e])}const p=i.fn({...t,[f]:m,[u]:h});return{...p,data:{x:p.x-n,y:p.y-r,enabled:{[f]:s,[u]:o}}}}}},ft=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:a,middlewareData:s,rects:o,initialPlacement:i,platform:l,elements:c}=t,{mainAxis:d=!0,crossAxis:u=!0,fallbackPlacements:f,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:p=!0,...x}=Ie(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};const g=Oe(a),v=Fe(i),y=Oe(i)===i,w=await(null==l.isRTL?void 0:l.isRTL(c.floating)),b=f||(y||!p?[Ue(i)]:function(e){const t=Ue(e);return[Be(e),t,Be(t)]}(i)),j="none"!==h;!f&&j&&b.push(...function(e,t,n,r){const a=Me(e);let s=function(e,t,n){const r=["left","right"],a=["right","left"],s=["top","bottom"],o=["bottom","top"];switch(e){case"top":case"bottom":return n?t?a:r:t?r:a;case"left":case"right":return t?s:o;default:return[]}}(Oe(e),"start"===n,r);return a&&(s=s.map((e=>e+"-"+a)),t&&(s=s.concat(s.map(Be)))),s}(i,p,h,w));const N=[i,...b],C=await Ke(t,x),S=[];let R=(null==(r=s.flip)?void 0:r.overflows)||[];if(d&&S.push(C[g]),u){const e=function(e,t,n){void 0===n&&(n=!1);const r=Me(e),a=He(e),s=_e(a);let o="x"===a?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[s]>t.floating[s]&&(o=Ue(o)),[o,Ue(o)]}(a,o,w);S.push(C[e[0]],C[e[1]])}if(R=[...R,{placement:a,overflows:S}],!S.every((e=>e<=0))){var E,k;const e=((null==(E=s.flip)?void 0:E.index)||0)+1,t=N[e];if(t){var D;const n="alignment"===u&&v!==Fe(t),r=(null==(D=R[0])?void 0:D.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:R},reset:{placement:t}}}let n=null==(k=R.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:k.placement;if(!n)switch(m){case"bestFit":{var T;const e=null==(T=R.filter((e=>{if(j){const t=Fe(e.placement);return t===v||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:T[0];e&&(n=e);break}case"initialPlacement":n=i}if(a!==n)return{reset:{placement:n}}}return{}}}},mt=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:a,rects:s,platform:o,elements:i}=t,{apply:l=()=>{},...c}=Ie(e,t),d=await Ke(t,c),u=Oe(a),f=Me(a),m="y"===Fe(a),{width:h,height:p}=s.floating;let x,g;"top"===u||"bottom"===u?(x=u,g=f===(await(null==o.isRTL?void 0:o.isRTL(i.floating))?"start":"end")?"left":"right"):(g=u,x="end"===f?"top":"bottom");const v=p-d.top-d.bottom,y=h-d.left-d.right,w=Se(p-d[x],v),b=Se(h-d[g],y),j=!t.middlewareData.shift;let N=w,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=y),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(N=v),j&&!f){const e=Re(d.left,0),t=Re(d.right,0),n=Re(d.top,0),r=Re(d.bottom,0);m?C=h-2*(0!==e||0!==t?e+t:Re(d.left,d.right)):N=p-2*(0!==n||0!==r?n+r:Re(d.top,d.bottom))}await l({...t,availableWidth:C,availableHeight:N});const S=await o.getDimensions(i.floating);return h!==S.width||p!==S.height?{reset:{rects:!0}}:{}}}},ht=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...a}=Ie(e,t);switch(r){case"referenceHidden":{const e=$e(await Ke(t,{...a,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:qe(e)}}}case"escaped":{const e=$e(await Ke(t,{...a,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:qe(e)}}}default:return{}}}}},pt=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:a,rects:s,platform:o,elements:i,middlewareData:l}=t,{element:c,padding:d=0}=Ie(e,t)||{};if(null==c)return{};const u=We(d),f={x:n,y:r},m=He(a),h=_e(m),p=await o.getDimensions(c),x="y"===m,g=x?"top":"left",v=x?"bottom":"right",y=x?"clientHeight":"clientWidth",w=s.reference[h]+s.reference[m]-f[m]-s.floating[h],b=f[m]-s.reference[m],j=await(null==o.getOffsetParent?void 0:o.getOffsetParent(c));let N=j?j[y]:0;N&&await(null==o.isElement?void 0:o.isElement(j))||(N=i.floating[y]||s.floating[h]);const C=w/2-b/2,S=N/2-p[h]/2-1,R=Se(u[g],S),E=Se(u[v],S),k=R,D=N-p[h]-E,T=N/2-p[h]/2+C,A=Pe(k,T,D),P=!l.arrow&&null!=Me(a)&&T!==A&&s.reference[h]/2-(T<k?R:E)-p[h]/2<0,I=P?T<k?T-k:T-D:0;return{[m]:f[m]+I,data:{[m]:A,centerOffset:T-A-I,...P&&{alignmentOffset:I}},reset:P}}}),xt=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:a,rects:s,middlewareData:o}=t,{offset:i=0,mainAxis:l=!0,crossAxis:c=!0}=Ie(e,t),d={x:n,y:r},u=Fe(a),f=Le(u);let m=d[f],h=d[u];const p=Ie(i,t),x="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(l){const e="y"===f?"height":"width",t=s.reference[f]-s.floating[e]+x.mainAxis,n=s.reference[f]+s.reference[e]-x.mainAxis;m<t?m=t:m>n&&(m=n)}if(c){var g,v;const e="y"===f?"width":"height",t=["top","left"].includes(Oe(a)),n=s.reference[u]-s.floating[e]+(t&&(null==(g=o.offset)?void 0:g[u])||0)+(t?0:x.crossAxis),r=s.reference[u]+s.reference[e]+(t?0:(null==(v=o.offset)?void 0:v[u])||0)-(t?x.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:m,[u]:h}}}};var gt="undefined"!=typeof document?t.useLayoutEffect:t.useEffect;function vt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,a;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!vt(e[r],t[r]))return!1;return!0}if(a=Object.keys(e),n=a.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,a[r]))return!1;for(r=n;0!==r--;){const n=a[r];if(!("_owner"===n&&e.$$typeof||vt(e[n],t[n])))return!1}return!0}return e!=e&&t!=t}function yt(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function wt(e,t){const n=yt(e);return Math.round(t*n)/n}function bt(e){const n=t.useRef(e);return gt((()=>{n.current=e})),n}const jt=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(a=n,{}.hasOwnProperty.call(a,"current"))?null!=n.current?pt({element:n.current,padding:r}).fn(t):{}:n?pt({element:n,padding:r}).fn(t):{};var a}}),Nt=(e,t)=>({...dt(e),options:[e,t]}),Ct=(e,t)=>({...ut(e),options:[e,t]}),St=(e,t)=>({...xt(e),options:[e,t]}),Rt=(e,t)=>({...ft(e),options:[e,t]}),Et=(e,t)=>({...mt(e),options:[e,t]}),kt=(e,t)=>({...ht(e),options:[e,t]}),Dt=(e,t)=>({...jt(e),options:[e,t]}),Tt=t.forwardRef((({className:e,...t},n)=>r.jsx("div",{ref:n,className:a("rounded-xl border bg-card text-card-foreground shadow",e),...t})));Tt.displayName="Card";const At=t.forwardRef((({className:e,...t},n)=>r.jsx("div",{ref:n,className:a("flex flex-col space-y-1.5 p-6",e),...t})));At.displayName="CardHeader";const Pt=t.forwardRef((({className:e,...t},n)=>r.jsx("div",{ref:n,className:a("font-semibold leading-none tracking-tight",e),...t})));Pt.displayName="CardTitle";const It=t.forwardRef((({className:e,...t},n)=>r.jsx("div",{ref:n,className:a("text-sm text-muted-foreground",e),...t})));It.displayName="CardDescription";const Ot=t.forwardRef((({className:e,...t},n)=>r.jsx("div",{ref:n,className:a("p-6 pt-0",e),...t})));Ot.displayName="CardContent",t.forwardRef((({className:e,...t},n)=>r.jsx("div",{ref:n,className:a("flex items-center p-6 pt-0",e),...t}))).displayName="CardFooter";var Mt="horizontal",Lt=["horizontal","vertical"],_t=t.forwardRef(((e,t)=>{const{decorative:n,orientation:a=Mt,...o}=e,i=function(e){return Lt.includes(e)}(a)?a:Mt,l=n?{role:"none"}:{"aria-orientation":"vertical"===i?i:void 0,role:"separator"};return r.jsx(s.div,{"data-orientation":i,...l,...o,ref:t})}));_t.displayName="Separator";var Ft=_t;const Ht=t.forwardRef((({className:e,orientation:t="horizontal",decorative:n=!0,...s},o)=>r.jsx(Ft,{ref:o,decorative:n,orientation:t,className:a("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...s})));Ht.displayName=Ft.displayName;const Bt=({activeTab:e,setActiveTab:t})=>{const n=o(),a=i(),{logout:s}=l(),u=[{id:"designs",label:"Bản thiết kế",icon:te,path:"/staff/designs"},{id:"customers",label:"Khách hàng",icon:re,path:"/staff/customers"}];return r.jsxs(c.div,{className:"w-64 bg-background border-r flex flex-col",initial:{x:-100,opacity:0},animate:{x:0,opacity:1},transition:{duration:.3},children:[r.jsx("div",{className:"p-6",children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("img",{src:"/images/logo-mini.png",alt:"DEXIN Logo",className:"w-10 h-10 object-contain"}),r.jsxs("div",{children:[r.jsx("h2",{className:"text-xl font-bold text-dexin-primary",children:"DEXIN"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Staff Dashboard"})]})]})}),r.jsx(Ht,{}),r.jsx("div",{className:"flex-1 py-6",children:r.jsx("nav",{className:"space-y-2 px-4",children:u.map((e=>{const s=e.icon,o=a.pathname===e.path;return r.jsx(c.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:r.jsxs(K,{onClick:()=>(e=>{t(e.id),n(e.path)})(e),variant:o?"default":"ghost",className:"w-full justify-start space-x-3 h-12 "+(o?"bg-dexin-primary hover:bg-dexin-primary/90 text-white shadow-dexin":"hover:bg-dexin-light-10 hover:text-dexin-primary"),children:[r.jsx(s,{size:20}),r.jsx("span",{className:"font-medium",children:e.label})]})},e.id)}))})}),r.jsxs("div",{className:"p-4",children:[r.jsx(Ht,{className:"mb-4"}),r.jsx(c.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:r.jsxs(K,{onClick:async()=>{try{await s(),n("/login")}catch(e){n("/login")}},variant:"ghost",className:"w-full justify-start space-x-3 h-12 text-destructive hover:bg-destructive/10 hover:text-destructive",children:[r.jsx(d,{size:20}),r.jsx("span",{className:"font-medium",children:"Đăng xuất"})]})})]})]})},Ut=t.forwardRef((({className:e,type:t,...n},s)=>r.jsx("input",{type:t,className:a("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...n})));Ut.displayName="Input";const Wt=$("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function zt({className:e,variant:t,...n}){return r.jsx("div",{className:a(Wt({variant:t}),e),...n})}const Vt=()=>{var e;const{user:t,getDisplayName:n}=l();return r.jsx(c.header,{className:"bg-background border-b px-6 py-4",initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.3},children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex-1 max-w-md",children:r.jsxs("div",{className:"relative",children:[r.jsx(u,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",size:20}),r.jsx(Ut,{type:"text",placeholder:"Tìm kiếm mã đơn hàng...",className:"pl-10"})]})}),r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsx(c.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsxs(K,{variant:"ghost",size:"icon",className:"relative",children:[r.jsx(ee,{size:20}),r.jsx(zt,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center",children:"3"})]})}),r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsxs("div",{className:"text-right",children:[r.jsx("p",{className:"text-sm font-medium",children:n()||"Staff User"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"staff"===(null==(e=null==t?void 0:t.role)?void 0:e.toLowerCase())?"Nhân viên":"Người dùng"})]}),r.jsxs(f,{className:"w-8 h-8",children:[r.jsx(m,{src:(null==t?void 0:t.avatar)&&"default_avatar.png"!==t.avatar?t.avatar:void 0,alt:n()||"User Avatar"}),r.jsx(h,{className:"bg-dexin-primary text-white text-sm font-medium",children:(a=n(),a?a.split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2):"U")})]})]})]})]})});var a},Kt=t.forwardRef((({className:e,...t},n)=>r.jsx("div",{className:"relative w-full overflow-auto",children:r.jsx("table",{ref:n,className:a("w-full caption-bottom text-sm",e),...t})})));Kt.displayName="Table";const $t=t.forwardRef((({className:e,...t},n)=>r.jsx("thead",{ref:n,className:a("[&_tr]:border-b",e),...t})));$t.displayName="TableHeader";const qt=t.forwardRef((({className:e,...t},n)=>r.jsx("tbody",{ref:n,className:a("[&_tr:last-child]:border-0",e),...t})));qt.displayName="TableBody",t.forwardRef((({className:e,...t},n)=>r.jsx("tfoot",{ref:n,className:a("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t}))).displayName="TableFooter";const Xt=t.forwardRef((({className:e,...t},n)=>r.jsx("tr",{ref:n,className:a("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t})));Xt.displayName="TableRow";const Yt=t.forwardRef((({className:e,...t},n)=>r.jsx("th",{ref:n,className:a("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})));Yt.displayName="TableHead";const Gt=t.forwardRef((({className:e,...t},n)=>r.jsx("td",{ref:n,className:a("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})));Gt.displayName="TableCell",t.forwardRef((({className:e,...t},n)=>r.jsx("caption",{ref:n,className:a("mt-4 text-sm text-muted-foreground",e),...t}))).displayName="TableCaption";const Zt=({className:e,...t})=>r.jsx("nav",{role:"navigation","aria-label":"pagination",className:a("mx-auto flex w-full justify-center",e),...t});Zt.displayName="Pagination";const Qt=t.forwardRef((({className:e,...t},n)=>r.jsx("ul",{ref:n,className:a("flex flex-row items-center gap-1",e),...t})));Qt.displayName="PaginationContent";const Jt=t.forwardRef((({className:e,...t},n)=>r.jsx("li",{ref:n,className:a("",e),...t})));Jt.displayName="PaginationItem";const en=({className:e,isActive:t,size:n="icon",...s})=>r.jsx("a",{"aria-current":t?"page":void 0,className:a(q({variant:t?"outline":"ghost",size:n}),e),...s});en.displayName="PaginationLink";const tn=({className:e,...t})=>r.jsxs(en,{"aria-label":"Go to previous page",size:"default",className:a("gap-1 pl-2.5",e),...t,children:[r.jsx(p,{className:"h-4 w-4"}),r.jsx("span",{children:"Previous"})]});tn.displayName="PaginationPrevious";const nn=({className:e,...t})=>r.jsxs(en,{"aria-label":"Go to next page",size:"default",className:a("gap-1 pr-2.5",e),...t,children:[r.jsx("span",{children:"Next"}),r.jsx(x,{className:"h-4 w-4"})]});function rn(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}nn.displayName="PaginationNext";var an=g[" useId ".trim().toString()]||(()=>{}),sn=0;function on(e){const[n,r]=t.useState(an());return v((()=>{r((e=>e??String(sn++)))}),[e]),e||(n?`radix-${n}`:"")}var ln=g[" useInsertionEffect ".trim().toString()]||v;function cn({prop:e,defaultProp:n,onChange:r=()=>{},caller:a}){const[s,o,i]=function({defaultProp:e,onChange:n}){const[r,a]=t.useState(e),s=t.useRef(r),o=t.useRef(n);return ln((()=>{o.current=n}),[n]),t.useEffect((()=>{var e;s.current!==r&&(null==(e=o.current)||e.call(o,r),s.current=r)}),[r,s]),[r,a,o]}({defaultProp:n,onChange:r}),l=void 0!==e,c=l?e:s;{const n=t.useRef(void 0!==e);t.useEffect((()=>{n.current;n.current=l}),[l,a])}const d=t.useCallback((t=>{var n;if(l){const r=function(e){return"function"==typeof e}(t)?t(e):t;r!==e&&(null==(n=i.current)||n.call(i,r))}else o(t)}),[l,e,o,i]);return[c,d]}var dn,un="dismissableLayer.update",fn=t.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),mn=t.forwardRef(((e,n)=>{const{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:l,onInteractOutside:c,onDismiss:d,...u}=e,f=t.useContext(fn),[m,h]=t.useState(null),p=(null==m?void 0:m.ownerDocument)??(null==globalThis?void 0:globalThis.document),[,x]=t.useState({}),g=w(n,(e=>h(e))),v=Array.from(f.layers),[b]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),j=v.indexOf(b),N=m?v.indexOf(m):-1,C=f.layersWithOutsidePointerEventsDisabled.size>0,S=N>=j,R=function(e,n=(null==globalThis?void 0:globalThis.document)){const r=y(e),a=t.useRef(!1),s=t.useRef((()=>{}));return t.useEffect((()=>{const e=e=>{if(e.target&&!a.current){let t=function(){pn("dismissableLayer.pointerDownOutside",r,a,{discrete:!0})};const a={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);a.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}}),[n,r]),{onPointerDownCapture:()=>a.current=!0}}((e=>{const t=e.target,n=[...f.branches].some((e=>e.contains(t)));S&&!n&&(null==i||i(e),null==c||c(e),e.defaultPrevented||null==d||d())}),p),E=function(e,n=(null==globalThis?void 0:globalThis.document)){const r=y(e),a=t.useRef(!1);return t.useEffect((()=>{const e=e=>{e.target&&!a.current&&pn("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}((e=>{const t=e.target;[...f.branches].some((e=>e.contains(t)))||(null==l||l(e),null==c||c(e),e.defaultPrevented||null==d||d())}),p);return function(e,n=(null==globalThis?void 0:globalThis.document)){const r=y(e);t.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})}),[r,n])}((e=>{N===f.layers.size-1&&(null==o||o(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))}),p),t.useEffect((()=>{if(m)return a&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(dn=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(m)),f.layers.add(m),hn(),()=>{a&&1===f.layersWithOutsidePointerEventsDisabled.size&&(p.body.style.pointerEvents=dn)}}),[m,p,a,f]),t.useEffect((()=>()=>{m&&(f.layers.delete(m),f.layersWithOutsidePointerEventsDisabled.delete(m),hn())}),[m,f]),t.useEffect((()=>{const e=()=>x({});return document.addEventListener(un,e),()=>document.removeEventListener(un,e)}),[]),r.jsx(s.div,{...u,ref:g,style:{pointerEvents:C?S?"auto":"none":void 0,...e.style},onFocusCapture:rn(e.onFocusCapture,E.onFocusCapture),onBlurCapture:rn(e.onBlurCapture,E.onBlurCapture),onPointerDownCapture:rn(e.onPointerDownCapture,R.onPointerDownCapture)})}));function hn(){const e=new CustomEvent(un);document.dispatchEvent(e)}function pn(e,t,n,{discrete:r}){const a=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),r?b(a,s):a.dispatchEvent(s)}mn.displayName="DismissableLayer",t.forwardRef(((e,n)=>{const a=t.useContext(fn),o=t.useRef(null),i=w(n,o);return t.useEffect((()=>{const e=o.current;if(e)return a.branches.add(e),()=>{a.branches.delete(e)}}),[a.branches]),r.jsx(s.div,{...e,ref:i})})).displayName="DismissableLayerBranch";var xn="focusScope.autoFocusOnMount",gn="focusScope.autoFocusOnUnmount",vn={bubbles:!1,cancelable:!0},yn=t.forwardRef(((e,n)=>{const{loop:a=!1,trapped:o=!1,onMountAutoFocus:i,onUnmountAutoFocus:l,...c}=e,[d,u]=t.useState(null),f=y(i),m=y(l),h=t.useRef(null),p=w(n,(e=>u(e))),x=t.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;t.useEffect((()=>{if(o){let e=function(e){if(x.paused||!d)return;const t=e.target;d.contains(t)?h.current=t:Nn(h.current,{select:!0})},t=function(e){if(x.paused||!d)return;const t=e.relatedTarget;null!==t&&(d.contains(t)||Nn(h.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&Nn(d)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return d&&r.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}}),[o,d,x.paused]),t.useEffect((()=>{if(d){Cn.add(x);const e=document.activeElement;if(!d.contains(e)){const t=new CustomEvent(xn,vn);d.addEventListener(xn,f),d.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Nn(r,{select:t}),document.activeElement!==n)return}(wn(d).filter((e=>"A"!==e.tagName)),{select:!0}),document.activeElement===e&&Nn(d))}return()=>{d.removeEventListener(xn,f),setTimeout((()=>{const t=new CustomEvent(gn,vn);d.addEventListener(gn,m),d.dispatchEvent(t),t.defaultPrevented||Nn(e??document.body,{select:!0}),d.removeEventListener(gn,m),Cn.remove(x)}),0)}}}),[d,f,m,x]);const g=t.useCallback((e=>{if(!a&&!o)return;if(x.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[r,s]=function(e){const t=wn(e);return[bn(t,e),bn(t.reverse(),e)]}(t);r&&s?e.shiftKey||n!==s?e.shiftKey&&n===r&&(e.preventDefault(),a&&Nn(s,{select:!0})):(e.preventDefault(),a&&Nn(r,{select:!0})):n===t&&e.preventDefault()}}),[a,o,x.paused]);return r.jsx(s.div,{tabIndex:-1,...c,ref:p,onKeyDown:g})}));function wn(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function bn(e,t){for(const n of e)if(!jn(n,{upTo:t}))return n}function jn(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function Nn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}yn.displayName="FocusScope";var Cn=function(){let e=[];return{add(t){const n=e[0];t!==n&&(null==n||n.pause()),e=Sn(e,t),e.unshift(t)},remove(t){var n;e=Sn(e,t),null==(n=e[0])||n.resume()}}}();function Sn(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}var Rn=t.forwardRef(((e,n)=>{var a;const{container:o,...i}=e,[l,c]=t.useState(!1);v((()=>c(!0)),[]);const d=o||l&&(null==(a=null==globalThis?void 0:globalThis.document)?void 0:a.body);return d?j.createPortal(r.jsx(s.div,{...i,ref:n}),d):null}));Rn.displayName="Portal";var En=e=>{const{present:n,children:r}=e,a=function(e){const[n,r]=t.useState(),a=t.useRef(null),s=t.useRef(e),o=t.useRef("none"),i=e?"mounted":"unmounted",[l,c]=function(e,n){return t.useReducer(((e,t)=>n[e][t]??e),e)}(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return t.useEffect((()=>{const e=kn(a.current);o.current="mounted"===l?e:"none"}),[l]),v((()=>{const t=a.current,n=s.current;if(n!==e){const r=o.current,a=kn(t);e?c("MOUNT"):"none"===a||"none"===(null==t?void 0:t.display)?c("UNMOUNT"):c(n&&r!==a?"ANIMATION_OUT":"UNMOUNT"),s.current=e}}),[e,c]),v((()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const o=kn(a.current).includes(r.animationName);if(r.target===n&&o&&(c("ANIMATION_END"),!s.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout((()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)}))}},i=e=>{e.target===n&&(o.current=kn(a.current))};return n.addEventListener("animationstart",i),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",i),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}c("ANIMATION_END")}),[n,c]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:t.useCallback((e=>{a.current=e?getComputedStyle(e):null,r(e)}),[])}}(n),s="function"==typeof r?r({present:a.isPresent}):t.Children.only(r),o=w(a.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,a=r&&"isReactWarning"in r&&r.isReactWarning;return a?e.ref:(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get,a=r&&"isReactWarning"in r&&r.isReactWarning,a?e.props.ref:e.props.ref||e.ref)}(s));return"function"==typeof r||a.isPresent?t.cloneElement(s,{ref:o}):null};function kn(e){return(null==e?void 0:e.animationName)||"none"}En.displayName="Presence";var Dn=0;function Tn(){t.useEffect((()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??An()),document.body.insertAdjacentElement("beforeend",e[1]??An()),Dn++,()=>{1===Dn&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),Dn--}}),[])}function An(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Pn="right-scroll-bar-position",In="width-before-scroll-bar";function On(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var Mn="undefined"!=typeof window?t.useLayoutEffect:t.useEffect,Ln=new WeakMap;function _n(e){return e}var Fn=function(e){var n=e.sideCar,r=C(e,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var a=n.read();if(!a)throw new Error("Sidecar medium not found");return t.createElement(a,N({},r))};Fn.isSideCarExport=!0;var Hn=function(e){void 0===e&&(e={});var t=function(e,t){void 0===t&&(t=_n);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var a=t(e,r);return n.push(a),function(){n=n.filter((function(e){return e!==a}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var a=n;n=[],a.forEach(e),t=n}var s=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(s)};o(),n={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),n}}}}}();return t.options=N({async:!0,ssr:!1},e),t}(),Bn=function(){},Un=t.forwardRef((function(e,n){var r=t.useRef(null),a=t.useState({onScrollCapture:Bn,onWheelCapture:Bn,onTouchMoveCapture:Bn}),s=a[0],o=a[1],i=e.forwardProps,l=e.children,c=e.className,d=e.removeScrollBar,u=e.enabled,f=e.shards,m=e.sideCar,h=e.noRelative,p=e.noIsolation,x=e.inert,g=e.allowPinchZoom,v=e.as,y=void 0===v?"div":v,w=e.gapMode,b=C(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=m,S=function(e){var n,r,a=(n=function(t){return e.forEach((function(e){return On(e,t)}))},(r=t.useState((function(){return{value:null,callback:n,facade:{get current(){return r.value},set current(e){var t=r.value;t!==e&&(r.value=e,r.callback(e,t))}}}}))[0]).callback=n,r.facade);return Mn((function(){var t=Ln.get(a);if(t){var n=new Set(t),r=new Set(e),s=a.current;n.forEach((function(e){r.has(e)||On(e,null)})),r.forEach((function(e){n.has(e)||On(e,s)}))}Ln.set(a,e)}),[e]),a}([r,n]),R=N(N({},b),s);return t.createElement(t.Fragment,null,u&&t.createElement(j,{sideCar:Hn,removeScrollBar:d,shards:f,noRelative:h,noIsolation:p,inert:x,setCallbacks:o,allowPinchZoom:!!g,lockRef:r,gapMode:w}),i?t.cloneElement(t.Children.only(l),N(N({},R),{ref:S})):t.createElement(y,N({},R,{className:c,ref:S}),l))}));Un.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Un.classNames={fullWidth:In,zeroRight:Pn};var Wn=function(){var e=0,t=null;return{add:function(n){var r,a;0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}())&&(a=n,(r=t).styleSheet?r.styleSheet.cssText=a:r.appendChild(document.createTextNode(a)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},zn=function(){var e,n=(e=Wn(),function(n,r){t.useEffect((function(){return e.add(n),function(){e.remove()}}),[n&&r])});return function(e){var t=e.styles,r=e.dynamic;return n(t,r),null}},Vn={left:0,top:0,right:0,gap:0},Kn=function(e){return parseInt(e||"",10)||0},$n=zn(),qn="data-scroll-locked",Xn=function(e,t,n,r){var a=e.left,s=e.top,o=e.right,i=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(qn,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(a,"px;\n    padding-top: ").concat(s,"px;\n    padding-right: ").concat(o,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(Pn," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(In," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(Pn," .").concat(Pn," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(In," .").concat(In," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(qn,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},Yn=function(){var e=parseInt(document.body.getAttribute(qn)||"0",10);return isFinite(e)?e:0},Gn=function(e){var n=e.noRelative,r=e.noImportant,a=e.gapMode,s=void 0===a?"margin":a;t.useEffect((function(){return document.body.setAttribute(qn,(Yn()+1).toString()),function(){var e=Yn()-1;e<=0?document.body.removeAttribute(qn):document.body.setAttribute(qn,e.toString())}}),[]);var o=t.useMemo((function(){return function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Vn;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],a=t["padding"===e?"paddingRight":"marginRight"];return[Kn(n),Kn(r),Kn(a)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}}(s)}),[s]);return t.createElement($n,{styles:Xn(o,!n,s,r?"":"!important")})},Zn=!1;if("undefined"!=typeof window)try{var Qn=Object.defineProperty({},"passive",{get:function(){return Zn=!0,!0}});window.addEventListener("test",Qn,Qn),window.removeEventListener("test",Qn,Qn)}catch(po){Zn=!1}var Jn=!!Zn&&{passive:!1},er=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},tr=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nr(e,r)){var a=rr(e,r);if(a[1]>a[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nr=function(e,t){return"v"===e?function(e){return er(e,"overflowY")}(t):function(e){return er(e,"overflowX")}(t)},rr=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},ar=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},sr=function(e){return[e.deltaX,e.deltaY]},or=function(e){return e&&"current"in e?e.current:e},ir=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},lr=0,cr=[];function dr(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ur=(fr=function(e){var n=t.useRef([]),r=t.useRef([0,0]),a=t.useRef(),s=t.useState(lr++)[0],o=t.useState(zn)[0],i=t.useRef(e);t.useEffect((function(){i.current=e}),[e]),t.useEffect((function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var t=S([e.lockRef.current],(e.shards||[]).map(or),!0).filter(Boolean);return t.forEach((function(e){return e.classList.add("allow-interactivity-".concat(s))})),function(){document.body.classList.remove("block-interactivity-".concat(s)),t.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(s))}))}}}),[e.inert,e.lockRef.current,e.shards]);var l=t.useCallback((function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var n,s=ar(e),o=r.current,l="deltaX"in e?e.deltaX:o[0]-s[0],c="deltaY"in e?e.deltaY:o[1]-s[1],d=e.target,u=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===u&&"range"===d.type)return!1;var f=tr(u,d);if(!f)return!0;if(f?n=u:(n="v"===u?"h":"v",f=tr(u,d)),!f)return!1;if(!a.current&&"changedTouches"in e&&(l||c)&&(a.current=n),!n)return!0;var m=a.current||n;return function(e,t,n,r){var a=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),s=a*r,o=n.target,i=t.contains(o),l=!1,c=s>0,d=0,u=0;do{if(!o)break;var f=rr(e,o),m=f[0],h=f[1]-f[2]-a*m;(m||h)&&nr(e,o)&&(d+=h,u+=m);var p=o.parentNode;o=p&&p.nodeType===Node.DOCUMENT_FRAGMENT_NODE?p.host:p}while(!i&&o!==document.body||i&&(t.contains(o)||t===o));return(c&&Math.abs(d)<1||!c&&Math.abs(u)<1)&&(l=!0),l}(m,t,e,"h"===m?l:c)}),[]),c=t.useCallback((function(e){var t=e;if(cr.length&&cr[cr.length-1]===o){var r="deltaY"in t?sr(t):ar(t),a=n.current.filter((function(e){return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(n=e.delta,a=r,n[0]===a[0]&&n[1]===a[1]);var n,a}))[0];if(a&&a.should)t.cancelable&&t.preventDefault();else if(!a){var s=(i.current.shards||[]).map(or).filter(Boolean).filter((function(e){return e.contains(t.target)}));(s.length>0?l(t,s[0]):!i.current.noIsolation)&&t.cancelable&&t.preventDefault()}}}),[]),d=t.useCallback((function(e,t,r,a){var s={name:e,delta:t,target:r,should:a,shadowParent:dr(r)};n.current.push(s),setTimeout((function(){n.current=n.current.filter((function(e){return e!==s}))}),1)}),[]),u=t.useCallback((function(e){r.current=ar(e),a.current=void 0}),[]),f=t.useCallback((function(t){d(t.type,sr(t),t.target,l(t,e.lockRef.current))}),[]),m=t.useCallback((function(t){d(t.type,ar(t),t.target,l(t,e.lockRef.current))}),[]);t.useEffect((function(){return cr.push(o),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:m}),document.addEventListener("wheel",c,Jn),document.addEventListener("touchmove",c,Jn),document.addEventListener("touchstart",u,Jn),function(){cr=cr.filter((function(e){return e!==o})),document.removeEventListener("wheel",c,Jn),document.removeEventListener("touchmove",c,Jn),document.removeEventListener("touchstart",u,Jn)}}),[]);var h=e.removeScrollBar,p=e.inert;return t.createElement(t.Fragment,null,p?t.createElement(o,{styles:ir(s)}):null,h?t.createElement(Gn,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},Hn.useMedium(fr),Fn);var fr,mr=t.forwardRef((function(e,n){return t.createElement(Un,N({},e,{ref:n,sideCar:ur}))}));mr.classNames=Un.classNames;var hr=new WeakMap,pr=new WeakMap,xr={},gr=0,vr=function(e){return e&&(e.host||vr(e.parentNode))},yr=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),a=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return a?(r.push.apply(r,Array.from(a.querySelectorAll("[aria-live], script"))),function(e,t,n,r){var a=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=vr(t);return n&&e.contains(n)?n:null})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);xr[n]||(xr[n]=new WeakMap);var s=xr[n],o=[],i=new Set,l=new Set(a),c=function(e){e&&!i.has(e)&&(i.add(e),c(e.parentNode))};a.forEach(c);var d=function(e){e&&!l.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(i.has(e))d(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(hr.get(e)||0)+1,c=(s.get(e)||0)+1;hr.set(e,l),s.set(e,c),o.push(e),1===l&&a&&pr.set(e,!0),1===c&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(u){}}))};return d(t),i.clear(),gr++,function(){o.forEach((function(e){var t=hr.get(e)-1,a=s.get(e)-1;hr.set(e,t),s.set(e,a),t||(pr.has(e)||e.removeAttribute(r),pr.delete(e)),a||e.removeAttribute(n)})),--gr||(hr=new WeakMap,hr=new WeakMap,pr=new WeakMap,xr={})}}(r,a,n,"aria-hidden")):function(){return null}},wr="Dialog",[br,jr]=R(wr),[Nr,Cr]=br(wr),Sr=e=>{const{__scopeDialog:n,children:a,open:s,defaultOpen:o,onOpenChange:i,modal:l=!0}=e,c=t.useRef(null),d=t.useRef(null),[u,f]=cn({prop:s,defaultProp:o??!1,onChange:i,caller:wr});return r.jsx(Nr,{scope:n,triggerRef:c,contentRef:d,contentId:on(),titleId:on(),descriptionId:on(),open:u,onOpenChange:f,onOpenToggle:t.useCallback((()=>f((e=>!e))),[f]),modal:l,children:a})};Sr.displayName=wr;var Rr="DialogTrigger";t.forwardRef(((e,t)=>{const{__scopeDialog:n,...a}=e,o=Cr(Rr,n),i=w(t,o.triggerRef);return r.jsx(s.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":$r(o.open),...a,ref:i,onClick:rn(e.onClick,o.onOpenToggle)})})).displayName=Rr;var Er="DialogPortal",[kr,Dr]=br(Er,{forceMount:void 0}),Tr=e=>{const{__scopeDialog:n,forceMount:a,children:s,container:o}=e,i=Cr(Er,n);return r.jsx(kr,{scope:n,forceMount:a,children:t.Children.map(s,(e=>r.jsx(En,{present:a||i.open,children:r.jsx(Rn,{asChild:!0,container:o,children:e})})))})};Tr.displayName=Er;var Ar="DialogOverlay",Pr=t.forwardRef(((e,t)=>{const n=Dr(Ar,e.__scopeDialog),{forceMount:a=n.forceMount,...s}=e,o=Cr(Ar,e.__scopeDialog);return o.modal?r.jsx(En,{present:a||o.open,children:r.jsx(Or,{...s,ref:t})}):null}));Pr.displayName=Ar;var Ir=E("DialogOverlay.RemoveScroll"),Or=t.forwardRef(((e,t)=>{const{__scopeDialog:n,...a}=e,o=Cr(Ar,n);return r.jsx(mr,{as:Ir,allowPinchZoom:!0,shards:[o.contentRef],children:r.jsx(s.div,{"data-state":$r(o.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})})),Mr="DialogContent",Lr=t.forwardRef(((e,t)=>{const n=Dr(Mr,e.__scopeDialog),{forceMount:a=n.forceMount,...s}=e,o=Cr(Mr,e.__scopeDialog);return r.jsx(En,{present:a||o.open,children:o.modal?r.jsx(_r,{...s,ref:t}):r.jsx(Fr,{...s,ref:t})})}));Lr.displayName=Mr;var _r=t.forwardRef(((e,n)=>{const a=Cr(Mr,e.__scopeDialog),s=t.useRef(null),o=w(n,a.contentRef,s);return t.useEffect((()=>{const e=s.current;if(e)return yr(e)}),[]),r.jsx(Hr,{...e,ref:o,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:rn(e.onCloseAutoFocus,(e=>{var t;e.preventDefault(),null==(t=a.triggerRef.current)||t.focus()})),onPointerDownOutside:rn(e.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:rn(e.onFocusOutside,(e=>e.preventDefault()))})})),Fr=t.forwardRef(((e,n)=>{const a=Cr(Mr,e.__scopeDialog),s=t.useRef(!1),o=t.useRef(!1);return r.jsx(Hr,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,r;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(s.current||null==(r=a.triggerRef.current)||r.focus(),t.preventDefault()),s.current=!1,o.current=!1},onInteractOutside:t=>{var n,r;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(s.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));const i=t.target;(null==(r=a.triggerRef.current)?void 0:r.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})})),Hr=t.forwardRef(((e,n)=>{const{__scopeDialog:a,trapFocus:s,onOpenAutoFocus:o,onCloseAutoFocus:i,...l}=e,c=Cr(Mr,a),d=t.useRef(null),u=w(n,d);return Tn(),r.jsxs(r.Fragment,{children:[r.jsx(yn,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:o,onUnmountAutoFocus:i,children:r.jsx(mn,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":$r(c.open),...l,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),r.jsxs(r.Fragment,{children:[r.jsx(Gr,{titleId:c.titleId}),r.jsx(Zr,{contentRef:d,descriptionId:c.descriptionId})]})]})})),Br="DialogTitle",Ur=t.forwardRef(((e,t)=>{const{__scopeDialog:n,...a}=e,o=Cr(Br,n);return r.jsx(s.h2,{id:o.titleId,...a,ref:t})}));Ur.displayName=Br;var Wr="DialogDescription",zr=t.forwardRef(((e,t)=>{const{__scopeDialog:n,...a}=e,o=Cr(Wr,n);return r.jsx(s.p,{id:o.descriptionId,...a,ref:t})}));zr.displayName=Wr;var Vr="DialogClose",Kr=t.forwardRef(((e,t)=>{const{__scopeDialog:n,...a}=e,o=Cr(Vr,n);return r.jsx(s.button,{type:"button",...a,ref:t,onClick:rn(e.onClick,(()=>o.onOpenChange(!1)))})}));function $r(e){return e?"open":"closed"}Kr.displayName=Vr;var qr="DialogTitleWarning",[Xr,Yr]=k(qr,{contentName:Mr,titleName:Br,docsSlug:"dialog"}),Gr=({titleId:e})=>{const n=Yr(qr),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return t.useEffect((()=>{e&&document.getElementById(e)}),[r,e]),null},Zr=({contentRef:e,descriptionId:n})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Yr("DialogDescriptionWarning").contentName}}.`;return t.useEffect((()=>{var t;const r=null==(t=e.current)?void 0:t.getAttribute("aria-describedby");n&&r&&document.getElementById(n)}),[r,e,n]),null},Qr=Pr,Jr=Lr,ea=Ur,ta=zr,na=Kr;const ra=Sr,aa=Tr,sa=t.forwardRef((({className:e,...t},n)=>r.jsx(Qr,{ref:n,className:a("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t})));sa.displayName=Qr.displayName;const oa=t.forwardRef((({className:e,children:t,...n},s)=>r.jsxs(aa,{children:[r.jsx(sa,{}),r.jsxs(Jr,{ref:s,className:a("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,r.jsxs(na,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[r.jsx(D,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]})));oa.displayName=Jr.displayName;const ia=({className:e,...t})=>r.jsx("div",{className:a("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});ia.displayName="DialogHeader";const la=t.forwardRef((({className:e,...t},n)=>r.jsx(ea,{ref:n,className:a("text-lg font-semibold leading-none tracking-tight",e),...t})));la.displayName=ea.displayName,t.forwardRef((({className:e,...t},n)=>r.jsx(ta,{ref:n,className:a("text-sm text-muted-foreground",e),...t}))).displayName=ta.displayName;var ca=t.forwardRef(((e,t)=>r.jsx(s.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}})));ca.displayName="Label";var da=ca;const ua=$("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),fa=t.forwardRef((({className:e,...t},n)=>r.jsx(da,{ref:n,className:a(ua(),e),...t})));fa.displayName=da.displayName;const ma=t.forwardRef((({className:e,...t},n)=>r.jsx("textarea",{className:a("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...t})));function ha(e,[t,n]){return Math.min(n,Math.max(t,e))}ma.displayName="Textarea";var pa=t.createContext(void 0),xa=t.forwardRef(((e,t)=>{const{children:n,width:a=10,height:o=5,...i}=e;return r.jsx(s.svg,{...i,ref:t,width:a,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:r.jsx("polygon",{points:"0,0 30,0 15,10"})})}));xa.displayName="Arrow";var ga=xa,va="Popper",[ya,wa]=R(va),[ba,ja]=ya(va),Na=e=>{const{__scopePopper:n,children:a}=e,[s,o]=t.useState(null);return r.jsx(ba,{scope:n,anchor:s,onAnchorChange:o,children:a})};Na.displayName=va;var Ca="PopperAnchor",Sa=t.forwardRef(((e,n)=>{const{__scopePopper:a,virtualRef:o,...i}=e,l=ja(Ca,a),c=t.useRef(null),d=w(n,c);return t.useEffect((()=>{l.onAnchorChange((null==o?void 0:o.current)||c.current)})),o?null:r.jsx(s.div,{...i,ref:d})}));Sa.displayName=Ca;var Ra="PopperContent",[Ea,ka]=ya(Ra),Da=t.forwardRef(((e,a)=>{var o,i,l,c,d,u;const{__scopePopper:f,side:m="bottom",sideOffset:h=0,align:p="center",alignOffset:x=0,arrowPadding:g=0,avoidCollisions:b=!0,collisionBoundary:j=[],collisionPadding:N=0,sticky:C="partial",hideWhenDetached:S=!1,updatePositionStrategy:R="optimized",onPlaced:E,...k}=e,D=ja(Ra,f),[T,A]=t.useState(null),P=w(a,(e=>A(e))),[I,O]=t.useState(null),M=function(e){const[n,r]=t.useState(void 0);return v((()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver((t=>{if(!Array.isArray(t))return;if(!t.length)return;const n=t[0];let a,s;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;a=t.inlineSize,s=t.blockSize}else a=e.offsetWidth,s=e.offsetHeight;r({width:a,height:s})}));return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)}),[e]),n}(I),L=(null==M?void 0:M.width)??0,_=(null==M?void 0:M.height)??0,F=m+("center"!==p?"-"+p:""),H="number"==typeof N?N:{top:0,right:0,bottom:0,left:0,...N},B=Array.isArray(j)?j:[j],U=B.length>0,W={padding:H,boundary:B.filter(Ia),altBoundary:U},{refs:z,floatingStyles:V,placement:K,isPositioned:$,middlewareData:q}=function(e){void 0===e&&(e={});const{placement:r="bottom",strategy:a="absolute",middleware:s=[],platform:o,elements:{reference:i,floating:l}={},transform:c=!0,whileElementsMounted:d,open:u}=e,[f,m]=t.useState({x:0,y:0,strategy:a,placement:r,middlewareData:{},isPositioned:!1}),[h,p]=t.useState(s);vt(h,s)||p(s);const[x,g]=t.useState(null),[v,y]=t.useState(null),w=t.useCallback((e=>{e!==C.current&&(C.current=e,g(e))}),[]),b=t.useCallback((e=>{e!==S.current&&(S.current=e,y(e))}),[]),j=i||x,N=l||v,C=t.useRef(null),S=t.useRef(null),R=t.useRef(f),E=null!=d,k=bt(d),D=bt(o),T=bt(u),A=t.useCallback((()=>{if(!C.current||!S.current)return;const e={placement:r,strategy:a,middleware:h};D.current&&(e.platform=D.current),((e,t,n)=>{const r=new Map,a={platform:lt,...n},s={...a.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:a="absolute",middleware:s=[],platform:o}=n,i=s.filter(Boolean),l=await(null==o.isRTL?void 0:o.isRTL(t));let c=await o.getElementRects({reference:e,floating:t,strategy:a}),{x:d,y:u}=Ve(c,r,l),f=r,m={},h=0;for(let p=0;p<i.length;p++){const{name:n,fn:s}=i[p],{x:x,y:g,data:v,reset:y}=await s({x:d,y:u,initialPlacement:r,placement:f,strategy:a,middlewareData:m,rects:c,platform:o,elements:{reference:e,floating:t}});d=null!=x?x:d,u=null!=g?g:u,m={...m,[n]:{...m[n],...v}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(c=!0===y.rects?await o.getElementRects({reference:e,floating:t,strategy:a}):y.rects),({x:d,y:u}=Ve(c,f,l))),p=-1)}return{x:d,y:u,placement:f,strategy:a,middlewareData:m}})(e,t,{...a,platform:s})})(C.current,S.current,e).then((e=>{const t={...e,isPositioned:!1!==T.current};P.current&&!vt(R.current,t)&&(R.current=t,n.flushSync((()=>{m(t)})))}))}),[h,r,a,D,T]);gt((()=>{!1===u&&R.current.isPositioned&&(R.current.isPositioned=!1,m((e=>({...e,isPositioned:!1}))))}),[u]);const P=t.useRef(!1);gt((()=>(P.current=!0,()=>{P.current=!1})),[]),gt((()=>{if(j&&(C.current=j),N&&(S.current=N),j&&N){if(k.current)return k.current(j,N,A);A()}}),[j,N,A,k,E]);const I=t.useMemo((()=>({reference:C,floating:S,setReference:w,setFloating:b})),[w,b]),O=t.useMemo((()=>({reference:j,floating:N})),[j,N]),M=t.useMemo((()=>{const e={position:a,left:0,top:0};if(!O.floating)return e;const t=wt(O.floating,f.x),n=wt(O.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+n+"px)",...yt(O.floating)>=1.5&&{willChange:"transform"}}:{position:a,left:t,top:n}}),[a,c,O.floating,f.x,f.y]);return t.useMemo((()=>({...f,update:A,refs:I,elements:O,floatingStyles:M})),[f,A,I,O,M])}({strategy:"fixed",placement:F,whileElementsMounted:(...e)=>function(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:o="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:l=!1}=r,c=Ye(e),u=a||s?[...c?je(c):[],...je(t)]:[];u.forEach((e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)}));const f=c&&i?function(e,t){let n,r=null;const a=ie(e);function s(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function o(i,l){void 0===i&&(i=!1),void 0===l&&(l=1),s();const c=e.getBoundingClientRect(),{left:u,top:f,width:m,height:h}=c;if(i||t(),!m||!h)return;const p={rootMargin:-ke(f)+"px "+-ke(a.clientWidth-(u+m))+"px "+-ke(a.clientHeight-(f+h))+"px "+-ke(u)+"px",threshold:Re(0,Se(1,l))||1};let x=!0;function g(t){const r=t[0].intersectionRatio;if(r!==l){if(!x)return o();r?o(!1,r):n=setTimeout((()=>{o(!1,1e-7)}),1e3)}1!==r||ct(c,e.getBoundingClientRect())||o(),x=!1}try{r=new IntersectionObserver(g,{...p,root:a.ownerDocument})}catch(d){r=new IntersectionObserver(g,p)}r.observe(e)}(!0),s}(c,n):null;let m,h=-1,p=null;o&&(p=new ResizeObserver((e=>{let[r]=e;r&&r.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame((()=>{var e;null==(e=p)||e.observe(t)}))),n()})),c&&!l&&p.observe(c),p.observe(t));let x=l?Je(e):null;return l&&function t(){const r=Je(e);x&&!ct(x,r)&&n(),x=r,m=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach((e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)})),null==f||f(),null==(e=p)||e.disconnect(),p=null,l&&cancelAnimationFrame(m)}}(...e,{animationFrame:"always"===R}),elements:{reference:D.anchor},middleware:[Nt({mainAxis:h+_,alignmentAxis:x}),b&&Ct({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?St():void 0,...W}),b&&Rt({...W}),Et({...W,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:a,height:s}=t.reference,o=e.floating.style;o.setProperty("--radix-popper-available-width",`${n}px`),o.setProperty("--radix-popper-available-height",`${r}px`),o.setProperty("--radix-popper-anchor-width",`${a}px`),o.setProperty("--radix-popper-anchor-height",`${s}px`)}}),I&&Dt({element:I,padding:g}),Oa({arrowWidth:L,arrowHeight:_}),S&&kt({strategy:"referenceHidden",...W})]}),[X,Y]=Ma(K),G=y(E);v((()=>{$&&(null==G||G())}),[$,G]);const Z=null==(o=q.arrow)?void 0:o.x,Q=null==(i=q.arrow)?void 0:i.y,J=0!==(null==(l=q.arrow)?void 0:l.centerOffset),[ee,te]=t.useState();return v((()=>{T&&te(window.getComputedStyle(T).zIndex)}),[T]),r.jsx("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:$?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ee,"--radix-popper-transform-origin":[null==(c=q.transformOrigin)?void 0:c.x,null==(d=q.transformOrigin)?void 0:d.y].join(" "),...(null==(u=q.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:r.jsx(Ea,{scope:f,placedSide:X,onArrowChange:O,arrowX:Z,arrowY:Q,shouldHideArrow:J,children:r.jsx(s.div,{"data-side":X,"data-align":Y,...k,ref:P,style:{...k.style,animation:$?void 0:"none"}})})})}));Da.displayName=Ra;var Ta="PopperArrow",Aa={top:"bottom",right:"left",bottom:"top",left:"right"},Pa=t.forwardRef((function(e,t){const{__scopePopper:n,...a}=e,s=ka(Ta,n),o=Aa[s.placedSide];return r.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:r.jsx(ga,{...a,ref:t,style:{...a.style,display:"block"}})})}));function Ia(e){return null!==e}Pa.displayName=Ta;var Oa=e=>({name:"transformOrigin",options:e,fn(t){var n,r,a;const{placement:s,rects:o,middlewareData:i}=t,l=0!==(null==(n=i.arrow)?void 0:n.centerOffset),c=l?0:e.arrowWidth,d=l?0:e.arrowHeight,[u,f]=Ma(s),m={start:"0%",center:"50%",end:"100%"}[f],h=((null==(r=i.arrow)?void 0:r.x)??0)+c/2,p=((null==(a=i.arrow)?void 0:a.y)??0)+d/2;let x="",g="";return"bottom"===u?(x=l?m:`${h}px`,g=-d+"px"):"top"===u?(x=l?m:`${h}px`,g=`${o.floating.height+d}px`):"right"===u?(x=-d+"px",g=l?m:`${p}px`):"left"===u&&(x=`${o.floating.width+d}px`,g=l?m:`${p}px`),{data:{x:x,y:g}}}});function Ma(e){const[t,n="center"]=e.split("-");return[t,n]}var La=Na,_a=Sa,Fa=Da,Ha=Pa,Ba=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});t.forwardRef(((e,t)=>r.jsx(s.span,{...e,ref:t,style:{...Ba,...e.style}}))).displayName="VisuallyHidden";var Ua=[" ","Enter","ArrowUp","ArrowDown"],Wa=[" ","Enter"],za="Select",[Va,Ka,$a]=function(e){const t=e+"CollectionProvider",[n,a]=R(t),[s,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{const{scope:t,children:n}=e,a=T.useRef(null),o=T.useRef(new Map).current;return r.jsx(s,{scope:t,itemMap:o,collectionRef:a,children:n})};i.displayName=t;const l=e+"CollectionSlot",c=E(l),d=T.forwardRef(((e,t)=>{const{scope:n,children:a}=e,s=o(l,n),i=w(t,s.collectionRef);return r.jsx(c,{ref:i,children:a})}));d.displayName=l;const u=e+"CollectionItemSlot",f="data-radix-collection-item",m=E(u),h=T.forwardRef(((e,t)=>{const{scope:n,children:a,...s}=e,i=T.useRef(null),l=w(t,i),c=o(u,n);return T.useEffect((()=>(c.itemMap.set(i,{ref:i,...s}),()=>{c.itemMap.delete(i)}))),r.jsx(m,{[f]:"",ref:l,children:a})}));return h.displayName=u,[{Provider:i,Slot:d,ItemSlot:h},function(t){const n=o(e+"CollectionConsumer",t);return T.useCallback((()=>{const e=n.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${f}]`));return Array.from(n.itemMap.values()).sort(((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current)))}),[n.collectionRef,n.itemMap])},a]}(za),[qa,Xa]=R(za,[$a,wa]),Ya=wa(),[Ga,Za]=qa(za),[Qa,Ja]=qa(za),es=e=>{const{__scopeSelect:n,children:a,open:s,defaultOpen:o,onOpenChange:i,value:l,defaultValue:c,onValueChange:d,dir:u,name:f,autoComplete:m,disabled:h,required:p,form:x}=e,g=Ya(n),[v,y]=t.useState(null),[w,b]=t.useState(null),[j,N]=t.useState(!1),C=function(e){const n=t.useContext(pa);return e||n||"ltr"}(u),[S,R]=cn({prop:s,defaultProp:o??!1,onChange:i,caller:za}),[E,k]=cn({prop:l,defaultProp:c,onChange:d,caller:za}),D=t.useRef(null),T=!v||x||!!v.closest("form"),[A,P]=t.useState(new Set),I=Array.from(A).map((e=>e.props.value)).join(";");return r.jsx(La,{...g,children:r.jsxs(Ga,{required:p,scope:n,trigger:v,onTriggerChange:y,valueNode:w,onValueNodeChange:b,valueNodeHasChildren:j,onValueNodeHasChildrenChange:N,contentId:on(),value:E,onValueChange:k,open:S,onOpenChange:R,dir:C,triggerPointerDownPosRef:D,disabled:h,children:[r.jsx(Va.Provider,{scope:n,children:r.jsx(Qa,{scope:e.__scopeSelect,onNativeOptionAdd:t.useCallback((e=>{P((t=>new Set(t).add(e)))}),[]),onNativeOptionRemove:t.useCallback((e=>{P((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),children:a})}),T?r.jsxs(Bs,{"aria-hidden":!0,required:p,tabIndex:-1,name:f,autoComplete:m,value:E,onChange:e=>k(e.target.value),disabled:h,form:x,children:[void 0===E?r.jsx("option",{value:""}):null,Array.from(A)]},I):null]})})};es.displayName=za;var ts="SelectTrigger",ns=t.forwardRef(((e,n)=>{const{__scopeSelect:a,disabled:o=!1,...i}=e,l=Ya(a),c=Za(ts,a),d=c.disabled||o,u=w(n,c.onTriggerChange),f=Ka(a),m=t.useRef("touch"),[h,p,x]=Ws((e=>{const t=f().filter((e=>!e.disabled)),n=t.find((e=>e.value===c.value)),r=zs(t,e,n);void 0!==r&&c.onValueChange(r.value)})),g=e=>{d||(c.onOpenChange(!0),x()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return r.jsx(_a,{asChild:!0,...l,children:r.jsx(s.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":Us(c.value)?"":void 0,...i,ref:u,onClick:rn(i.onClick,(e=>{e.currentTarget.focus(),"mouse"!==m.current&&g(e)})),onPointerDown:rn(i.onPointerDown,(e=>{m.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())})),onKeyDown:rn(i.onKeyDown,(e=>{const t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),t&&" "===e.key||Ua.includes(e.key)&&(g(),e.preventDefault())}))})})}));ns.displayName=ts;var rs="SelectValue",as=t.forwardRef(((e,t)=>{const{__scopeSelect:n,className:a,style:o,children:i,placeholder:l="",...c}=e,d=Za(rs,n),{onValueNodeHasChildrenChange:u}=d,f=void 0!==i,m=w(t,d.onValueNodeChange);return v((()=>{u(f)}),[u,f]),r.jsx(s.span,{...c,ref:m,style:{pointerEvents:"none"},children:Us(d.value)?r.jsx(r.Fragment,{children:l}):i})}));as.displayName=rs;var ss=t.forwardRef(((e,t)=>{const{__scopeSelect:n,children:a,...o}=e;return r.jsx(s.span,{"aria-hidden":!0,...o,ref:t,children:a||"▼"})}));ss.displayName="SelectIcon";var os=e=>r.jsx(Rn,{asChild:!0,...e});os.displayName="SelectPortal";var is="SelectContent",ls=t.forwardRef(((e,a)=>{const s=Za(is,e.__scopeSelect),[o,i]=t.useState();if(v((()=>{i(new DocumentFragment)}),[]),!s.open){const t=o;return t?n.createPortal(r.jsx(ds,{scope:e.__scopeSelect,children:r.jsx(Va.Slot,{scope:e.__scopeSelect,children:r.jsx("div",{children:e.children})})}),t):null}return r.jsx(ms,{...e,ref:a})}));ls.displayName=is;var cs=10,[ds,us]=qa(is),fs=E("SelectContent.RemoveScroll"),ms=t.forwardRef(((e,n)=>{const{__scopeSelect:a,position:s="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:c,sideOffset:d,align:u,alignOffset:f,arrowPadding:m,collisionBoundary:h,collisionPadding:p,sticky:x,hideWhenDetached:g,avoidCollisions:v,...y}=e,b=Za(is,a),[j,N]=t.useState(null),[C,S]=t.useState(null),R=w(n,(e=>N(e))),[E,k]=t.useState(null),[D,T]=t.useState(null),A=Ka(a),[P,I]=t.useState(!1),O=t.useRef(!1);t.useEffect((()=>{if(j)return yr(j)}),[j]),Tn();const M=t.useCallback((e=>{const[t,...n]=A().map((e=>e.ref.current)),[r]=n.slice(-1),a=document.activeElement;for(const s of e){if(s===a)return;if(null==s||s.scrollIntoView({block:"nearest"}),s===t&&C&&(C.scrollTop=0),s===r&&C&&(C.scrollTop=C.scrollHeight),null==s||s.focus(),document.activeElement!==a)return}}),[A,C]),L=t.useCallback((()=>M([E,j])),[M,E,j]);t.useEffect((()=>{P&&L()}),[P,L]);const{onOpenChange:_,triggerPointerDownPosRef:F}=b;t.useEffect((()=>{if(j){let e={x:0,y:0};const t=t=>{var n,r;e={x:Math.abs(Math.round(t.pageX)-((null==(n=F.current)?void 0:n.x)??0)),y:Math.abs(Math.round(t.pageY)-((null==(r=F.current)?void 0:r.y)??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():j.contains(n.target)||_(!1),document.removeEventListener("pointermove",t),F.current=null};return null!==F.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}}),[j,_,F]),t.useEffect((()=>{const e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}}),[_]);const[H,B]=Ws((e=>{const t=A().filter((e=>!e.disabled)),n=t.find((e=>e.ref.current===document.activeElement)),r=zs(t,e,n);r&&setTimeout((()=>r.ref.current.focus()))})),U=t.useCallback(((e,t,n)=>{const r=!O.current&&!n;(void 0!==b.value&&b.value===t||r)&&(k(e),r&&(O.current=!0))}),[b.value]),W=t.useCallback((()=>null==j?void 0:j.focus()),[j]),z=t.useCallback(((e,t,n)=>{const r=!O.current&&!n;(void 0!==b.value&&b.value===t||r)&&T(e)}),[b.value]),V="popper"===s?ps:hs,K=V===ps?{side:c,sideOffset:d,align:u,alignOffset:f,arrowPadding:m,collisionBoundary:h,collisionPadding:p,sticky:x,hideWhenDetached:g,avoidCollisions:v}:{};return r.jsx(ds,{scope:a,content:j,viewport:C,onViewportChange:S,itemRefCallback:U,selectedItem:E,onItemLeave:W,itemTextRefCallback:z,focusSelectedItem:L,selectedItemText:D,position:s,isPositioned:P,searchRef:H,children:r.jsx(mr,{as:fs,allowPinchZoom:!0,children:r.jsx(yn,{asChild:!0,trapped:b.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:rn(o,(e=>{var t;null==(t=b.trigger)||t.focus({preventScroll:!0}),e.preventDefault()})),children:r.jsx(mn,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>b.onOpenChange(!1),children:r.jsx(V,{role:"listbox",id:b.contentId,"data-state":b.open?"open":"closed",dir:b.dir,onContextMenu:e=>e.preventDefault(),...y,...K,onPlaced:()=>I(!0),ref:R,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:rn(y.onKeyDown,(e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||B(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter((e=>!e.disabled)).map((e=>e.ref.current));if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout((()=>M(t))),e.preventDefault()}}))})})})})})}));ms.displayName="SelectContentImpl";var hs=t.forwardRef(((e,n)=>{const{__scopeSelect:a,onPlaced:o,...i}=e,l=Za(is,a),c=us(is,a),[d,u]=t.useState(null),[f,m]=t.useState(null),h=w(n,(e=>m(e))),p=Ka(a),x=t.useRef(!1),g=t.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:j,focusSelectedItem:N}=c,C=t.useCallback((()=>{if(l.trigger&&l.valueNode&&d&&f&&y&&b&&j){const e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),r=j.getBoundingClientRect();if("rtl"!==l.dir){const a=r.left-t.left,s=n.left-a,o=e.left-s,i=e.width+o,l=Math.max(i,t.width),c=window.innerWidth-cs,u=ha(s,[cs,Math.max(cs,c-l)]);d.style.minWidth=i+"px",d.style.left=u+"px"}else{const a=t.right-r.right,s=window.innerWidth-n.right-a,o=window.innerWidth-e.right-s,i=e.width+o,l=Math.max(i,t.width),c=window.innerWidth-cs,u=ha(s,[cs,Math.max(cs,c-l)]);d.style.minWidth=i+"px",d.style.right=u+"px"}const a=p(),s=window.innerHeight-2*cs,i=y.scrollHeight,c=window.getComputedStyle(f),u=parseInt(c.borderTopWidth,10),m=parseInt(c.paddingTop,10),h=parseInt(c.borderBottomWidth,10),g=u+m+i+parseInt(c.paddingBottom,10)+h,v=Math.min(5*b.offsetHeight,g),w=window.getComputedStyle(y),N=parseInt(w.paddingTop,10),C=parseInt(w.paddingBottom,10),S=e.top+e.height/2-cs,R=s-S,E=b.offsetHeight/2,k=u+m+(b.offsetTop+E),D=g-k;if(k<=S){const e=a.length>0&&b===a[a.length-1].ref.current;d.style.bottom="0px";const t=f.clientHeight-y.offsetTop-y.offsetHeight,n=k+Math.max(R,E+(e?C:0)+t+h);d.style.height=n+"px"}else{const e=a.length>0&&b===a[0].ref.current;d.style.top="0px";const t=Math.max(S,u+y.offsetTop+(e?N:0)+E)+D;d.style.height=t+"px",y.scrollTop=k-S+y.offsetTop}d.style.margin=`${cs}px 0`,d.style.minHeight=v+"px",d.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame((()=>x.current=!0))}}),[p,l.trigger,l.valueNode,d,f,y,b,j,l.dir,o]);v((()=>C()),[C]);const[S,R]=t.useState();v((()=>{f&&R(window.getComputedStyle(f).zIndex)}),[f]);const E=t.useCallback((e=>{e&&!0===g.current&&(C(),null==N||N(),g.current=!1)}),[C,N]);return r.jsx(xs,{scope:a,contentWrapper:d,shouldExpandOnScrollRef:x,onScrollButtonChange:E,children:r.jsx("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:r.jsx(s.div,{...i,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})}));hs.displayName="SelectItemAlignedPosition";var ps=t.forwardRef(((e,t)=>{const{__scopeSelect:n,align:a="start",collisionPadding:s=cs,...o}=e,i=Ya(n);return r.jsx(Fa,{...i,...o,ref:t,align:a,collisionPadding:s,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})}));ps.displayName="SelectPopperPosition";var[xs,gs]=qa(is,{}),vs="SelectViewport",ys=t.forwardRef(((e,n)=>{const{__scopeSelect:a,nonce:o,...i}=e,l=us(vs,a),c=gs(vs,a),d=w(n,l.onViewportChange),u=t.useRef(0);return r.jsxs(r.Fragment,{children:[r.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),r.jsx(Va.Slot,{scope:a,children:r.jsx(s.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:rn(i.onScroll,(e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if((null==r?void 0:r.current)&&n){const e=Math.abs(u.current-t.scrollTop);if(e>0){const r=window.innerHeight-2*cs,a=parseFloat(n.style.minHeight),s=parseFloat(n.style.height),o=Math.max(a,s);if(o<r){const a=o+e,s=Math.min(r,a),i=a-s;n.style.height=s+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop}))})})]})}));ys.displayName=vs;var ws="SelectGroup",[bs,js]=qa(ws);t.forwardRef(((e,t)=>{const{__scopeSelect:n,...a}=e,o=on();return r.jsx(bs,{scope:n,id:o,children:r.jsx(s.div,{role:"group","aria-labelledby":o,...a,ref:t})})})).displayName=ws;var Ns="SelectLabel",Cs=t.forwardRef(((e,t)=>{const{__scopeSelect:n,...a}=e,o=js(Ns,n);return r.jsx(s.div,{id:o.id,...a,ref:t})}));Cs.displayName=Ns;var Ss="SelectItem",[Rs,Es]=qa(Ss),ks=t.forwardRef(((e,n)=>{const{__scopeSelect:a,value:o,disabled:i=!1,textValue:l,...c}=e,d=Za(Ss,a),u=us(Ss,a),f=d.value===o,[m,h]=t.useState(l??""),[p,x]=t.useState(!1),g=w(n,(e=>{var t;return null==(t=u.itemRefCallback)?void 0:t.call(u,e,o,i)})),v=on(),y=t.useRef("touch"),b=()=>{i||(d.onValueChange(o),d.onOpenChange(!1))};if(""===o)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return r.jsx(Rs,{scope:a,value:o,disabled:i,textId:v,isSelected:f,onItemTextChange:t.useCallback((e=>{h((t=>t||((null==e?void 0:e.textContent)??"").trim()))}),[]),children:r.jsx(Va.ItemSlot,{scope:a,value:o,disabled:i,textValue:m,children:r.jsx(s.div,{role:"option","aria-labelledby":v,"data-highlighted":p?"":void 0,"aria-selected":f&&p,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:g,onFocus:rn(c.onFocus,(()=>x(!0))),onBlur:rn(c.onBlur,(()=>x(!1))),onClick:rn(c.onClick,(()=>{"mouse"!==y.current&&b()})),onPointerUp:rn(c.onPointerUp,(()=>{"mouse"===y.current&&b()})),onPointerDown:rn(c.onPointerDown,(e=>{y.current=e.pointerType})),onPointerMove:rn(c.onPointerMove,(e=>{var t;y.current=e.pointerType,i?null==(t=u.onItemLeave)||t.call(u):"mouse"===y.current&&e.currentTarget.focus({preventScroll:!0})})),onPointerLeave:rn(c.onPointerLeave,(e=>{var t;e.currentTarget===document.activeElement&&(null==(t=u.onItemLeave)||t.call(u))})),onKeyDown:rn(c.onKeyDown,(e=>{var t;""!==(null==(t=u.searchRef)?void 0:t.current)&&" "===e.key||(Wa.includes(e.key)&&b()," "===e.key&&e.preventDefault())}))})})})}));ks.displayName=Ss;var Ds="SelectItemText",Ts=t.forwardRef(((e,a)=>{const{__scopeSelect:o,className:i,style:l,...c}=e,d=Za(Ds,o),u=us(Ds,o),f=Es(Ds,o),m=Ja(Ds,o),[h,p]=t.useState(null),x=w(a,(e=>p(e)),f.onItemTextChange,(e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,f.value,f.disabled)})),g=null==h?void 0:h.textContent,y=t.useMemo((()=>r.jsx("option",{value:f.value,disabled:f.disabled,children:g},f.value)),[f.disabled,f.value,g]),{onNativeOptionAdd:b,onNativeOptionRemove:j}=m;return v((()=>(b(y),()=>j(y))),[b,j,y]),r.jsxs(r.Fragment,{children:[r.jsx(s.span,{id:f.textId,...c,ref:x}),f.isSelected&&d.valueNode&&!d.valueNodeHasChildren?n.createPortal(c.children,d.valueNode):null]})}));Ts.displayName=Ds;var As="SelectItemIndicator",Ps=t.forwardRef(((e,t)=>{const{__scopeSelect:n,...a}=e;return Es(As,n).isSelected?r.jsx(s.span,{"aria-hidden":!0,...a,ref:t}):null}));Ps.displayName=As;var Is="SelectScrollUpButton",Os=t.forwardRef(((e,n)=>{const a=us(Is,e.__scopeSelect),s=gs(Is,e.__scopeSelect),[o,i]=t.useState(!1),l=w(n,s.onScrollButtonChange);return v((()=>{if(a.viewport&&a.isPositioned){let e=function(){const e=t.scrollTop>0;i(e)};const t=a.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[a.viewport,a.isPositioned]),o?r.jsx(_s,{...e,ref:l,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=a;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}));Os.displayName=Is;var Ms="SelectScrollDownButton",Ls=t.forwardRef(((e,n)=>{const a=us(Ms,e.__scopeSelect),s=gs(Ms,e.__scopeSelect),[o,i]=t.useState(!1),l=w(n,s.onScrollButtonChange);return v((()=>{if(a.viewport&&a.isPositioned){let e=function(){const e=t.scrollHeight-t.clientHeight,n=Math.ceil(t.scrollTop)<e;i(n)};const t=a.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[a.viewport,a.isPositioned]),o?r.jsx(_s,{...e,ref:l,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=a;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}));Ls.displayName=Ms;var _s=t.forwardRef(((e,n)=>{const{__scopeSelect:a,onAutoScroll:o,...i}=e,l=us("SelectScrollButton",a),c=t.useRef(null),d=Ka(a),u=t.useCallback((()=>{null!==c.current&&(window.clearInterval(c.current),c.current=null)}),[]);return t.useEffect((()=>()=>u()),[u]),v((()=>{var e;const t=d().find((e=>e.ref.current===document.activeElement));null==(e=null==t?void 0:t.ref.current)||e.scrollIntoView({block:"nearest"})}),[d]),r.jsx(s.div,{"aria-hidden":!0,...i,ref:n,style:{flexShrink:0,...i.style},onPointerDown:rn(i.onPointerDown,(()=>{null===c.current&&(c.current=window.setInterval(o,50))})),onPointerMove:rn(i.onPointerMove,(()=>{var e;null==(e=l.onItemLeave)||e.call(l),null===c.current&&(c.current=window.setInterval(o,50))})),onPointerLeave:rn(i.onPointerLeave,(()=>{u()}))})})),Fs=t.forwardRef(((e,t)=>{const{__scopeSelect:n,...a}=e;return r.jsx(s.div,{"aria-hidden":!0,...a,ref:t})}));Fs.displayName="SelectSeparator";var Hs="SelectArrow";t.forwardRef(((e,t)=>{const{__scopeSelect:n,...a}=e,s=Ya(n),o=Za(Hs,n),i=us(Hs,n);return o.open&&"popper"===i.position?r.jsx(Ha,{...s,...a,ref:t}):null})).displayName=Hs;var Bs=t.forwardRef((({__scopeSelect:e,value:n,...a},o)=>{const i=t.useRef(null),l=w(o,i),c=function(e){const n=t.useRef({value:e,previous:e});return t.useMemo((()=>(n.current.value!==e&&(n.current.previous=n.current.value,n.current.value=e),n.current.previous)),[e])}(n);return t.useEffect((()=>{const e=i.current;if(!e)return;const t=window.HTMLSelectElement.prototype,r=Object.getOwnPropertyDescriptor(t,"value").set;if(c!==n&&r){const t=new Event("change",{bubbles:!0});r.call(e,n),e.dispatchEvent(t)}}),[c,n]),r.jsx(s.select,{...a,style:{...Ba,...a.style},ref:l,defaultValue:n})}));function Us(e){return""===e||void 0===e}function Ws(e){const n=y(e),r=t.useRef(""),a=t.useRef(0),s=t.useCallback((e=>{const t=r.current+e;n(t),function e(t){r.current=t,window.clearTimeout(a.current),""!==t&&(a.current=window.setTimeout((()=>e("")),1e3))}(t)}),[n]),o=t.useCallback((()=>{r.current="",window.clearTimeout(a.current)}),[]);return t.useEffect((()=>()=>window.clearTimeout(a.current)),[]),[r,s,o]}function zs(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,a=n?e.indexOf(n):-1;let s=(o=e,i=Math.max(a,0),o.map(((e,t)=>o[(i+t)%o.length])));var o,i;1===r.length&&(s=s.filter((e=>e!==n)));const l=s.find((e=>e.textValue.toLowerCase().startsWith(r.toLowerCase())));return l!==n?l:void 0}Bs.displayName="SelectBubbleInput";var Vs=ns,Ks=ss,$s=os,qs=ls,Xs=ys,Ys=Cs,Gs=ks,Zs=Ts,Qs=Ps,Js=Os,eo=Ls,to=Fs;const no=es,ro=as,ao=t.forwardRef((({className:e,children:t,...n},s)=>r.jsxs(Vs,{ref:s,className:a("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,r.jsx(Ks,{asChild:!0,children:r.jsx(A,{className:"h-4 w-4 opacity-50"})})]})));ao.displayName=Vs.displayName;const so=t.forwardRef((({className:e,...t},n)=>r.jsx(Js,{ref:n,className:a("flex cursor-default items-center justify-center py-1",e),...t,children:r.jsx(G,{className:"h-4 w-4"})})));so.displayName=Js.displayName;const oo=t.forwardRef((({className:e,...t},n)=>r.jsx(eo,{ref:n,className:a("flex cursor-default items-center justify-center py-1",e),...t,children:r.jsx(A,{className:"h-4 w-4"})})));oo.displayName=eo.displayName;const io=t.forwardRef((({className:e,children:t,position:n="popper",...s},o)=>r.jsx($s,{children:r.jsxs(qs,{ref:o,className:a("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...s,children:[r.jsx(so,{}),r.jsx(Xs,{className:a("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),r.jsx(oo,{})]})})));io.displayName=qs.displayName,t.forwardRef((({className:e,...t},n)=>r.jsx(Ys,{ref:n,className:a("px-2 py-1.5 text-sm font-semibold",e),...t}))).displayName=Ys.displayName;const lo=t.forwardRef((({className:e,children:t,...n},s)=>r.jsxs(Gs,{ref:s,className:a("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[r.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(Qs,{children:r.jsx(Y,{className:"h-4 w-4"})})}),r.jsx(Zs,{children:t})]})));lo.displayName=Gs.displayName,t.forwardRef((({className:e,...t},n)=>r.jsx(to,{ref:n,className:a("-mx-1 my-1 h-px bg-muted",e),...t}))).displayName=to.displayName;const co=({isOpen:e,onClose:t,designData:n,customerInfo:a})=>{return n?r.jsx(ra,{open:e,onOpenChange:t,children:r.jsxs(oa,{className:"max-w-2xl",children:[r.jsx(ia,{children:r.jsxs(la,{className:"text-xl font-semibold text-green-600 flex items-center space-x-2",children:[r.jsx(P,{className:"w-6 h-6"}),r.jsx("span",{children:"Tạo thiết kế thành công!"})]})}),r.jsxs(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"space-y-6",children:[r.jsxs("div",{className:"text-center py-4",children:[r.jsx(c.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.5,type:"spring"},className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(P,{className:"w-8 h-8 text-green-600"})}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Thiết kế đã được tạo thành công!"}),r.jsx("p",{className:"text-gray-600",children:"Thiết kế của bạn đã được lưu và sẵn sàng để sử dụng."})]}),r.jsx(Tt,{children:r.jsxs(Ot,{className:"p-6 space-y-4",children:[r.jsxs("h4",{className:"font-semibold text-gray-900 flex items-center space-x-2",children:[r.jsx(Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Thông tin thiết kế"})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Tiêu đề"}),r.jsx("p",{className:"text-gray-900 font-medium",children:n.title})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"text-sm font-medium text-gray-500 flex items-center space-x-1",children:[r.jsx(ne,{className:"w-3 h-3"}),r.jsx("span",{children:"Giá"})]}),r.jsx("p",{className:"text-green-600 font-semibold text-lg",children:(s=n.price,new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(s))})]}),n.customerInfo&&r.jsxs("div",{children:[r.jsxs("label",{className:"text-sm font-medium text-gray-500 flex items-center space-x-1",children:[r.jsx(I,{className:"w-3 h-3"}),r.jsx("span",{children:"Khách hàng"})]}),r.jsx("p",{className:"text-gray-900 font-medium",children:`${n.customerInfo.firstName} ${n.customerInfo.lastName}`.trim()||n.customerInfo.userName}),r.jsx("p",{className:"text-xs text-gray-500",children:n.customerInfo.email})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"text-sm font-medium text-gray-500 flex items-center space-x-1",children:[r.jsx(I,{className:"w-3 h-3"}),r.jsx("span",{children:"Được tạo bởi"})]}),r.jsxs("p",{className:"text-gray-900",children:["Staff ID: ",n.createdBy]})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"text-sm font-medium text-gray-500 flex items-center space-x-1",children:[r.jsx(O,{className:"w-3 h-3"}),r.jsx("span",{children:"Thời gian tạo"})]}),r.jsx("p",{className:"text-gray-900",children:(new Date).toLocaleString("vi-VN")})]})]}),n.note&&r.jsxs("div",{children:[r.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Ghi chú"}),r.jsx("p",{className:"text-gray-900 bg-gray-50 p-3 rounded-lg mt-1",children:n.note})]})]})}),r.jsx(Tt,{children:r.jsxs(Ot,{className:"p-6",children:[r.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Files đã upload"}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[r.jsxs("div",{className:"border rounded-lg p-4",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h5",{className:"font-medium text-gray-900",children:"File thiết kế 3D"}),r.jsx(zt,{variant:"outline",className:"text-green-600 border-green-600",children:"Đã upload"})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx("p",{className:"text-sm text-gray-600 break-all",children:n.pathUrl}),r.jsxs(K,{onClick:()=>{n.pathUrl&&window.open(n.pathUrl,"_blank")},variant:"outline",size:"sm",className:"w-full",children:[r.jsx(M,{className:"w-4 h-4 mr-2"}),"Xem file"]})]})]}),r.jsxs("div",{className:"border rounded-lg p-4",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h5",{className:"font-medium text-gray-900",children:"Ảnh thumbnail"}),r.jsx(zt,{variant:"outline",className:"text-green-600 border-green-600",children:"Đã upload"})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs("div",{className:"w-full h-32 bg-gray-100 rounded-lg overflow-hidden",children:[r.jsx("img",{src:n.thumbnailUrl,alt:"Thumbnail",className:"w-full h-full object-cover",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}),r.jsx("div",{className:"w-full h-full hidden items-center justify-center text-gray-400",children:r.jsx("span",{className:"text-sm",children:"Không thể tải ảnh"})})]}),r.jsxs(K,{onClick:()=>{n.thumbnailUrl&&window.open(n.thumbnailUrl,"_blank")},variant:"outline",size:"sm",className:"w-full",children:[r.jsx(M,{className:"w-4 h-4 mr-2"}),"Xem ảnh gốc"]})]})]})]})]})}),r.jsx("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:r.jsxs(K,{onClick:t,className:"bg-dexin-primary hover:bg-dexin-primary/90",children:[r.jsx(P,{className:"w-4 h-4 mr-2"}),"Hoàn tất"]})})]})]})}):null;var s},uo=({isOpen:e,onClose:n,onSuccess:a})=>{const[s,o]=t.useState(!1),[i,l]=t.useState([]),[c,d]=t.useState(!1),[u,p]=t.useState(!1),[x,g]=t.useState(null),[v,y]=t.useState({title:"",pathUrl:null,thumbnailUrl:null,note:"",price:"",customerId:""}),[w,b]=t.useState({pathUrl:null,thumbnailUrl:null});t.useEffect((()=>{e&&j()}),[e]);const j=async()=>{d(!0);try{const e=await X.getAllCustomers();e.success?l(e.data):L.error(e.message)}catch(e){L.error("Có lỗi xảy ra khi tải danh sách khách hàng")}finally{d(!1)}},N=(e,t)=>{y((n=>({...n,[e]:t})))},C=(e,t)=>{if(t)if(t.size>10485760)L.error("File quá lớn. Vui lòng chọn file nhỏ hơn 10MB");else if(y((n=>({...n,[e]:t}))),t.type.startsWith("image/")){const n=new FileReader;n.onload=t=>{b((n=>({...n,[e]:t.target.result})))},n.readAsDataURL(t)}else b((t=>({...t,[e]:null})))},S=()=>{y({title:"",pathUrl:null,thumbnailUrl:null,note:"",price:"",customerId:""}),b({pathUrl:null,thumbnailUrl:null}),n()},R=e=>`${e.firstName} ${e.lastName}`.trim()||e.userName,E=e=>R(e).split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2);return r.jsxs(r.Fragment,{children:[r.jsx(ra,{open:e,onOpenChange:S,children:r.jsxs(oa,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[r.jsx(ia,{children:r.jsxs(la,{className:"text-xl font-semibold text-dexin-primary flex items-center space-x-2",children:[r.jsx(_,{className:"w-5 h-5"}),r.jsx("span",{children:"Thêm thiết kế 3D mới"})]})}),r.jsxs("form",{onSubmit:async e=>{var t,n,r;if(e.preventDefault(),v.title.trim()?v.pathUrl?v.thumbnailUrl?!v.price||isNaN(v.price)||Number(v.price)<=0?(L.error("Vui lòng nhập giá hợp lệ"),0):v.customerId||(L.error("Vui lòng chọn khách hàng"),0):(L.error("Vui lòng chọn ảnh thumbnail"),0):(L.error("Vui lòng chọn file thiết kế 3D"),0):(L.error("Vui lòng nhập tiêu đề thiết kế"),0)){o(!0);try{const e=await X.create3DDesign({title:v.title.trim(),pathUrl:v.pathUrl,thumbnailUrl:v.thumbnailUrl,note:v.note.trim(),price:Number(v.price),customerId:Number(v.customerId)});if(e.success){const t=i.find((e=>e.userAccountId.toString()===v.customerId));g({...e.data,customerInfo:t}),p(!0),S(),a&&a(e.data)}else e.message.includes("Phiên đăng nhập đã hết hạn")?L.error("Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."):e.message.includes("không có quyền")?L.error("Bạn không có quyền thực hiện thao tác này."):L.error(e.message)}catch(s){401===(null==(t=s.response)?void 0:t.status)?L.error("Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."):403===(null==(n=s.response)?void 0:n.status)?L.error("Bạn không có quyền thực hiện thao tác này."):(null==(r=s.response)?void 0:r.status)>=500?L.error("Lỗi server. Vui lòng thử lại sau."):"NETWORK_ERROR"===s.code?L.error("Lỗi kết nối mạng. Vui lòng kiểm tra kết nối."):L.error("Có lỗi xảy ra khi tạo thiết kế. Vui lòng thử lại.")}finally{o(!1)}}},className:"space-y-6",children:[r.jsxs("div",{className:"space-y-2",children:[r.jsxs(fa,{htmlFor:"title",className:"flex items-center space-x-2",children:[r.jsx(Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Tiêu đề thiết kế *"})]}),r.jsx(Ut,{id:"title",value:v.title,onChange:e=>N("title",e.target.value),placeholder:"Nhập tiêu đề thiết kế...",disabled:s})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(fa,{className:"flex items-center space-x-2",children:[r.jsx(I,{className:"w-4 h-4"}),r.jsx("span",{children:"Khách hàng *"})]}),r.jsxs(no,{value:v.customerId,onValueChange:e=>N("customerId",e),disabled:s||c,children:[r.jsx(ao,{children:r.jsx(ro,{placeholder:c?"Đang tải...":"Chọn khách hàng"})}),r.jsx(io,{children:i.map((e=>r.jsx(lo,{value:e.userAccountId.toString(),children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsxs(f,{className:"w-6 h-6",children:[r.jsx(m,{src:e.avartar,alt:R(e)}),r.jsx(h,{className:"text-xs",children:E(e)})]}),r.jsxs("div",{children:[r.jsx("div",{className:"font-medium",children:R(e)}),r.jsx("div",{className:"text-xs text-gray-500",children:e.email})]})]})},e.userAccountId)))})]})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[r.jsxs("div",{className:"space-y-2",children:[r.jsxs(fa,{className:"flex items-center space-x-2",children:[r.jsx(F,{className:"w-4 h-4"}),r.jsx("span",{children:"File thiết kế 3D *"})]}),r.jsx(Tt,{className:"border-dashed border-2 hover:border-dexin-primary transition-colors",children:r.jsxs(Ot,{className:"p-4",children:[r.jsx("input",{type:"file",id:"pathUrl",accept:".obj,.fbx,.gltf,.glb,image/*",onChange:e=>C("pathUrl",e.target.files[0]),className:"hidden",disabled:s}),r.jsx("label",{htmlFor:"pathUrl",className:"cursor-pointer flex flex-col items-center space-y-2 text-center",children:v.pathUrl?r.jsxs("div",{className:"text-green-600",children:[r.jsx(F,{className:"w-8 h-8 mx-auto mb-2"}),r.jsx("div",{className:"text-sm font-medium",children:v.pathUrl.name}),r.jsxs("div",{className:"text-xs text-gray-500",children:[(v.pathUrl.size/1024/1024).toFixed(2)," MB"]})]}):r.jsxs("div",{className:"text-gray-500",children:[r.jsx(_,{className:"w-8 h-8 mx-auto mb-2"}),r.jsx("div",{className:"text-sm",children:"Chọn file thiết kế"}),r.jsx("div",{className:"text-xs",children:"OBJ, FBX, GLTF, hoặc ảnh"})]})})]})})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs(fa,{className:"flex items-center space-x-2",children:[r.jsx(H,{className:"w-4 h-4"}),r.jsx("span",{children:"Ảnh thumbnail *"})]}),r.jsx(Tt,{className:"border-dashed border-2 hover:border-dexin-primary transition-colors",children:r.jsxs(Ot,{className:"p-4",children:[r.jsx("input",{type:"file",id:"thumbnailUrl",accept:"image/*",onChange:e=>C("thumbnailUrl",e.target.files[0]),className:"hidden",disabled:s}),r.jsx("label",{htmlFor:"thumbnailUrl",className:"cursor-pointer flex flex-col items-center space-y-2 text-center",children:w.thumbnailUrl?r.jsxs("div",{children:[r.jsx("img",{src:w.thumbnailUrl,alt:"Thumbnail preview",className:"w-16 h-16 object-cover rounded mx-auto mb-2"}),r.jsx("div",{className:"text-sm font-medium text-green-600",children:v.thumbnailUrl.name})]}):r.jsxs("div",{className:"text-gray-500",children:[r.jsx(H,{className:"w-8 h-8 mx-auto mb-2"}),r.jsx("div",{className:"text-sm",children:"Chọn ảnh thumbnail"}),r.jsx("div",{className:"text-xs",children:"JPG, PNG, WebP"})]})})]})})]})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:r.jsxs("div",{className:"space-y-2",children:[r.jsxs(fa,{htmlFor:"price",className:"flex items-center space-x-2",children:[r.jsx(ne,{className:"w-4 h-4"}),r.jsx("span",{children:"Giá (VNĐ) *"})]}),r.jsx(Ut,{id:"price",type:"number",value:v.price,onChange:e=>N("price",e.target.value),placeholder:"Nhập giá...",min:"0",disabled:s})]})}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(fa,{htmlFor:"note",children:"Ghi chú"}),r.jsx(ma,{id:"note",value:v.note,onChange:e=>N("note",e.target.value),placeholder:"Nhập ghi chú về thiết kế...",rows:3,disabled:s})]}),r.jsxs("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:[r.jsx(K,{type:"button",variant:"outline",onClick:S,disabled:s,children:"Hủy"}),r.jsx(K,{type:"submit",disabled:s,className:"bg-dexin-primary hover:bg-dexin-primary/90",children:s?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Đang tạo..."]}):r.jsxs(r.Fragment,{children:[r.jsx(_,{className:"w-4 h-4 mr-2"}),"Tạo thiết kế"]})})]})]})]})}),r.jsx(co,{isOpen:u,onClose:()=>{p(!1),g(null)},designData:x})]})},fo=()=>{const[e,n]=t.useState(1),[a,s]=t.useState([]),[o,i]=t.useState(!1),[l,d]=t.useState(!1);t.useEffect((()=>{u()}),[]);const u=async()=>{i(!0);try{const e=await X.getAllDesigns();e.success?s(e.data):(L.error(e.message),s([]))}catch(e){L.error("Có lỗi xảy ra khi tải danh sách thiết kế"),s([])}finally{i(!1)}},p=Math.ceil(a.length/10),x=10*(e-1),g=a.slice(x,x+10),v=e=>{n(e)},y=e=>{try{return new Date(e).toLocaleDateString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric"})}catch(t){return"N/A"}},w=e=>e?`${e.firstName} ${e.lastName}`.trim()||e.userName:"N/A",b=e=>e?`${e.firstName} ${e.lastName}`.trim()||e.userName:"Chưa phân công",j=e=>{switch(e){case"Hoàn tất thanh toán":return"default";case"Chưa trả":return"secondary";case"Hủy":return"destructive";default:return"outline"}},N=e=>"Hoàn tất thanh toán"===e.status?r.jsx(M,{size:16,className:"text-gray-600"}):r.jsx(U,{size:16,className:"text-gray-600"});return r.jsxs(c.div,{className:"p-6 h-full overflow-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[r.jsx(At,{className:"px-0",children:r.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[r.jsxs("div",{children:[r.jsx(Pt,{className:"text-2xl text-dexin-primary",children:"Bản thiết kế 3D"}),r.jsx(It,{children:"Quản lý các bản thiết kế 3D của khách hàng"})]}),r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsxs(K,{onClick:u,variant:"outline",size:"sm",disabled:o,className:"flex items-center space-x-2",children:[r.jsx(B,{className:"w-4 h-4 "+(o?"animate-spin":"")}),r.jsx("span",{children:"Làm mới"})]}),r.jsxs(K,{onClick:()=>d(!0),className:"bg-dexin-primary hover:bg-dexin-primary/90 flex items-center space-x-2",children:[r.jsx(Q,{className:"w-4 h-4"}),r.jsx("span",{children:"Thêm thiết kế"})]})]})]})}),r.jsx(c.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.3,delay:.1},children:r.jsx(Tt,{children:r.jsxs(Kt,{children:[r.jsx($t,{children:r.jsxs(Xt,{children:[r.jsx(Yt,{children:"ID"}),r.jsx(Yt,{children:"Khách hàng"}),r.jsx(Yt,{children:"Tiêu đề thiết kế"}),r.jsx(Yt,{children:"Thumbnail"}),r.jsx(Yt,{children:"Giá"}),r.jsx(Yt,{children:"Staff phụ trách"}),r.jsx(Yt,{children:"Trạng thái"}),r.jsx(Yt,{children:"Ngày tạo"}),r.jsx(Yt,{children:"Thao tác"})]})}),r.jsx(qt,{children:o?r.jsx(Xt,{children:r.jsx(Gt,{colSpan:9,className:"text-center py-8",children:r.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[r.jsx(B,{className:"w-4 h-4 animate-spin"}),r.jsx("span",{children:"Đang tải..."})]})})}):0===g.length?r.jsx(Xt,{children:r.jsx(Gt,{colSpan:9,className:"text-center py-8 text-gray-500",children:"Chưa có thiết kế nào"})}):g.map((e=>{var t,n,a,s;return r.jsxs(Xt,{children:[r.jsxs(Gt,{className:"font-medium",children:["#",e.designId]}),r.jsx(Gt,{children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsxs(f,{className:"w-8 h-8",children:[r.jsx(m,{src:null==(t=e.customer)?void 0:t.avartar,alt:w(e.customer)}),r.jsx(h,{className:"text-xs",children:(s=e.customer,s?w(s).split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2):"N/A")})]}),r.jsxs("div",{children:[r.jsx("div",{className:"font-medium",children:w(e.customer)}),r.jsx("div",{className:"text-xs text-gray-500",children:null==(n=e.customer)?void 0:n.email})]})]})}),r.jsx(Gt,{children:r.jsxs("div",{className:"max-w-[200px]",children:[r.jsx("div",{className:"font-medium truncate",children:e.title}),e.note&&r.jsx("div",{className:"text-xs text-gray-500 truncate",children:e.note})]})}),r.jsx(Gt,{children:e.thumbnailUrl?r.jsx("img",{src:e.thumbnailUrl,alt:e.title,className:"w-12 h-12 object-cover rounded border"}):r.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded border flex items-center justify-center",children:r.jsx("span",{className:"text-xs text-gray-400",children:"No img"})})}),r.jsx(Gt,{className:"font-medium",children:(a=e.price,new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(a))}),r.jsx(Gt,{children:e.staff?r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsxs(f,{className:"w-6 h-6",children:[r.jsx(m,{src:e.staff.avartar,alt:b(e.staff)}),r.jsx(h,{className:"text-xs",children:b(e.staff).charAt(0)})]}),r.jsx("span",{className:"text-sm",children:b(e.staff)})]}):r.jsx("span",{className:"text-gray-500 text-sm",children:"Chưa phân công"})}),r.jsx(Gt,{children:r.jsx(zt,{variant:j(e.status),children:e.status})}),r.jsx(Gt,{className:"text-sm",children:y(e.createdAt)}),r.jsx(Gt,{children:r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(c.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(K,{variant:"ghost",size:"icon",onClick:()=>(e=>{e.pathUrl?window.open(e.pathUrl,"_blank"):L.error("Không có file thiết kế để xem")})(e),title:"Xem thiết kế",children:N(e)})}),r.jsx(c.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(K,{variant:"ghost",size:"icon",title:"Thêm tùy chọn",children:r.jsx(J,{size:16})})})]})})]},e.designId)}))})]})})}),r.jsx(c.div,{className:"mt-6",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.2},children:r.jsx(Zt,{children:r.jsxs(Qt,{children:[r.jsx(Jt,{children:r.jsx(tn,{onClick:()=>e>1&&v(e-1),className:1===e?"pointer-events-none opacity-50":"cursor-pointer"})}),[...Array(p)].map(((t,n)=>{const a=n+1;return r.jsx(Jt,{children:r.jsx(c.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:r.jsx(en,{onClick:()=>v(a),isActive:e===a,className:"cursor-pointer "+(e===a?"bg-dexin-primary text-white hover:bg-dexin-primary/90":"hover:bg-dexin-light-10"),children:a})})},a)})),r.jsx(Jt,{children:r.jsx(nn,{onClick:()=>e<p&&v(e+1),className:e===p?"pointer-events-none opacity-50":"cursor-pointer"})})]})})}),r.jsx(uo,{isOpen:l,onClose:()=>d(!1),onSuccess:e=>{u(),d(!1)}})]})},mo=()=>{const[e,n]=t.useState(1),a=[{id:"DD006SA",customerName:"Hoàng Bảo Ngọc",phone:"0369852874",email:"<EMAIL>",createdDate:"Đã tạo cách đây 2 tháng",status:"Bị khoá",statusColor:"text-red-500"},{id:"DD007SA",customerName:"Hoàng Bảo Ngọc",phone:"0369852874",email:"<EMAIL>",createdDate:"Đã tạo cách đây 10 ngày",status:"Hoạt động gần đây",statusColor:"text-yellow-500"},{id:"DD008SA",customerName:"Hoàng Bảo Ngọc",phone:"0369852874",email:"<EMAIL>",createdDate:"Đã tạo cách đây 1 tháng",status:"Trực tuyến",statusColor:"text-green-500"},{id:"DD009SA",customerName:"Hoàng Bảo Ngọc",phone:"0369852874",email:"<EMAIL>",createdDate:"Đã tạo cách đây 20 ngày",status:"Trực tuyến",statusColor:"text-green-500"},{id:"DD010SA",customerName:"Hoàng Bảo Ngọc",phone:"0369852874",email:"<EMAIL>",createdDate:"Đã tạo cách đây 5 tháng",status:"Người tuyến",statusColor:"text-gray-500"},{id:"DD011SA",customerName:"Hoàng Bảo Ngọc",phone:"0369852874",email:"<EMAIL>",createdDate:"Đã tạo cách đây 12 ngày",status:"Hoạt động gần đây",statusColor:"text-yellow-500"},{id:"DD012SA",customerName:"Hoàng Bảo Ngọc",phone:"0369852874",email:"<EMAIL>",createdDate:"Đã tạo cách đây 5 tháng",status:"Chờ phản hồi khách",statusColor:"text-red-500"},{id:"DD013SA",customerName:"Hoàng Bảo Ngọc",phone:"0369852874",email:"<EMAIL>",createdDate:"Đã tạo cách đây 20 phút",status:"Bị khoá",statusColor:"text-red-500"},{id:"DD014SA",customerName:"Hoàng Bảo Ngọc",phone:"0369852874",email:"<EMAIL>",createdDate:"Đã tạo cách đây 4 tháng",status:"Người tuyến",statusColor:"text-gray-500"}],s=Math.ceil(a.length/10),o=10*(e-1),i=a.slice(o,o+10),l=e=>{n(e)};return r.jsxs(c.div,{className:"p-6 h-full overflow-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[r.jsxs(At,{className:"px-0",children:[r.jsx(Pt,{className:"text-2xl text-dexin-primary",children:"Khách hàng"}),r.jsx(It,{children:"Quản lý thông tin khách hàng"})]}),r.jsx(c.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.3,delay:.1},children:r.jsx(Tt,{children:r.jsxs(Kt,{children:[r.jsx($t,{children:r.jsxs(Xt,{children:[r.jsx(Yt,{children:"Mã đơn hàng"}),r.jsx(Yt,{children:"Họ và tên"}),r.jsx(Yt,{children:"SĐT"}),r.jsx(Yt,{children:"Email"}),r.jsx(Yt,{children:"Thời gian tạo"}),r.jsx(Yt,{children:"Trạng thái hoạt động"}),r.jsx(Yt,{children:"Thao tác"})]})}),r.jsx(qt,{children:i.map((e=>r.jsxs(Xt,{children:[r.jsx(Gt,{className:"font-medium",children:e.id}),r.jsx(Gt,{children:e.customerName}),r.jsx(Gt,{children:e.phone}),r.jsx(Gt,{children:e.email}),r.jsx(Gt,{children:e.createdDate}),r.jsx(Gt,{children:r.jsx("span",{className:"text-sm font-medium "+("Trực tuyến"===e.status?"text-green-600":"Hoạt động gần đây"===e.status?"text-orange-500":"Bị khoá"===e.status?"text-red-500":"text-gray-500"),children:e.status})}),r.jsx(Gt,{children:r.jsx(c.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(K,{variant:"ghost",size:"icon",children:r.jsx(J,{size:16})})})})]},e.id)))})]})})}),r.jsx(c.div,{className:"mt-6",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.2},children:r.jsx(Zt,{children:r.jsxs(Qt,{children:[r.jsx(Jt,{children:r.jsx(tn,{onClick:()=>e>1&&l(e-1),className:1===e?"pointer-events-none opacity-50":"cursor-pointer"})}),[...Array(s)].map(((t,n)=>{const a=n+1;return r.jsx(Jt,{children:r.jsx(c.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:r.jsx(en,{onClick:()=>l(a),isActive:e===a,className:"cursor-pointer "+(e===a?"bg-dexin-primary text-white hover:bg-dexin-primary/90":"hover:bg-dexin-light-10"),children:a})})},a)})),r.jsx(Jt,{children:r.jsx(nn,{onClick:()=>e<s&&l(e+1),className:e===s?"pointer-events-none opacity-50":"cursor-pointer"})})]})})})]})},ho=()=>{const[e,n]=t.useState("designs");return r.jsxs("div",{className:"h-screen w-full bg-background flex overflow-hidden",children:[r.jsx(Bt,{activeTab:e,setActiveTab:n}),r.jsxs("div",{className:"flex-1 flex flex-col bg-muted/30",children:[r.jsx(Vt,{}),r.jsx(c.div,{className:"flex-1 overflow-hidden",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:r.jsxs(W,{children:[r.jsx(z,{path:"/",element:r.jsx(V,{to:"/staff/designs",replace:!0})}),r.jsx(z,{path:"/designs",element:r.jsx(fo,{})}),r.jsx(z,{path:"/customers",element:r.jsx(mo,{})})]})})]})]})};export{ho as default};
