import React, { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { Eye, Download, MoreHorizontal, Plus, RefreshCw, ExternalLink } from 'lucide-react';
import { toast } from 'react-toastify';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../components/ui/table';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '../../../components/ui/avatar';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '../../../components/ui/pagination';

import { designService } from '../../../services/designService';
import AddDesignDialog from './AddDesignDialog';

const DesignManagement = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [designs, setDesigns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const itemsPerPage = 10;

  // Load designs khi component mount
  useEffect(() => {
    loadDesigns();
  }, []);

  // Load danh sách thiết kế từ API
  const loadDesigns = async () => {
    setLoading(true);
    try {
      const result = await designService.getAllDesigns();
      if (result.success) {
        setDesigns(result.data);
        console.log('✅ Loaded designs:', result.data);
      } else {
        toast.error(result.message);
        setDesigns([]);
      }
    } catch (error) {
      console.error('Load designs error:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách thiết kế');
      setDesigns([]);
    } finally {
      setLoading(false);
    }
  };

  const totalPages = Math.ceil(designs.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentItems = designs.slice(startIndex, startIndex + itemsPerPage);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Xử lý thêm thiết kế thành công
  const handleAddSuccess = (newDesign) => {
    console.log('✅ Design added successfully:', newDesign);
    loadDesigns(); // Reload danh sách
    setShowAddDialog(false);
  };

  // Format ngày tháng
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      return 'N/A';
    }
  };

  // Format giá tiền
  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  // Get customer display name
  const getCustomerDisplayName = (customer) => {
    if (!customer) return 'N/A';
    return `${customer.firstName} ${customer.lastName}`.trim() || customer.userName;
  };

  // Get staff display name
  const getStaffDisplayName = (staff) => {
    if (!staff) return 'Chưa phân công';
    return `${staff.firstName} ${staff.lastName}`.trim() || staff.userName;
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'Hoàn tất thanh toán':
        return 'default'; // green
      case 'Chưa trả':
        return 'secondary'; // gray
      case 'Hủy':
        return 'destructive'; // red
      default:
        return 'outline';
    }
  };

  // Get customer initials
  const getCustomerInitials = (customer) => {
    if (!customer) return 'N/A';
    const name = getCustomerDisplayName(customer);
    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);
  };

  // Xử lý xem chi tiết thiết kế
  const handleViewDesign = (design) => {
    if (design.pathUrl) {
      window.open(design.pathUrl, '_blank');
    } else {
      toast.error('Không có file thiết kế để xem');
    }
  };

  const getActionIcon = (design) => {
    if (design.status === 'Hoàn tất thanh toán') {
      return <Eye size={16} className="text-gray-600" />;
    }
    return <ExternalLink size={16} className="text-gray-600" />;
  };

  return (
    <motion.div 
      className="p-6 h-full overflow-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Page Title & Actions */}
      <CardHeader className="px-0">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <CardTitle className="text-2xl text-dexin-primary">Bản thiết kế 3D</CardTitle>
            <CardDescription>Quản lý các bản thiết kế 3D của khách hàng</CardDescription>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              onClick={loadDesigns}
              variant="outline"
              size="sm"
              disabled={loading}
              className="flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Làm mới</span>
            </Button>
            <Button
              onClick={() => setShowAddDialog(true)}
              className="bg-dexin-primary hover:bg-dexin-primary/90 flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Thêm thiết kế</span>
            </Button>
          </div>
        </div>
      </CardHeader>

      {/* Table */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Khách hàng</TableHead>
                <TableHead>Tiêu đề thiết kế</TableHead>
                <TableHead>Thumbnail</TableHead>
                <TableHead>Giá</TableHead>
                <TableHead>Staff phụ trách</TableHead>
                <TableHead>Trạng thái</TableHead>
                <TableHead>Ngày tạo</TableHead>
                <TableHead>Thao tác</TableHead>
              </TableRow>
            </TableHeader>

            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <div className="flex items-center justify-center space-x-2">
                      <RefreshCw className="w-4 h-4 animate-spin" />
                      <span>Đang tải...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : currentItems.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-gray-500">
                    Chưa có thiết kế nào
                  </TableCell>
                </TableRow>
              ) : (
                currentItems.map((design) => (
                  <TableRow key={design.designId}>
                    <TableCell className="font-medium">#{design.designId}</TableCell>

                    {/* Khách hàng */}
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="w-8 h-8">
                          <AvatarImage
                            src={design.customer?.avartar}
                            alt={getCustomerDisplayName(design.customer)}
                          />
                          <AvatarFallback className="text-xs">
                            {getCustomerInitials(design.customer)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{getCustomerDisplayName(design.customer)}</div>
                          <div className="text-xs text-gray-500">{design.customer?.email}</div>
                        </div>
                      </div>
                    </TableCell>

                    {/* Tiêu đề thiết kế */}
                    <TableCell>
                      <div className="max-w-[200px]">
                        <div className="font-medium truncate">{design.title}</div>
                        {design.note && (
                          <div className="text-xs text-gray-500 truncate">{design.note}</div>
                        )}
                      </div>
                    </TableCell>

                    {/* Thumbnail */}
                    <TableCell>
                      {design.thumbnailUrl ? (
                        <img
                          src={design.thumbnailUrl}
                          alt={design.title}
                          className="w-12 h-12 object-cover rounded border"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-100 rounded border flex items-center justify-center">
                          <span className="text-xs text-gray-400">No img</span>
                        </div>
                      )}
                    </TableCell>

                    {/* Giá */}
                    <TableCell className="font-medium">
                      {formatPrice(design.price)}
                    </TableCell>

                    {/* Staff phụ trách */}
                    <TableCell>
                      {design.staff ? (
                        <div className="flex items-center space-x-2">
                          <Avatar className="w-6 h-6">
                            <AvatarImage src={design.staff.avartar} alt={getStaffDisplayName(design.staff)} />
                            <AvatarFallback className="text-xs">
                              {getStaffDisplayName(design.staff).charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm">{getStaffDisplayName(design.staff)}</span>
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm">Chưa phân công</span>
                      )}
                    </TableCell>

                    {/* Trạng thái */}
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(design.status)}>
                        {design.status}
                      </Badge>
                    </TableCell>

                    {/* Ngày tạo */}
                    <TableCell className="text-sm">
                      {formatDate(design.createdAt)}
                    </TableCell>

                    {/* Thao tác */}
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewDesign(design)}
                            title="Xem thiết kế"
                          >
                            {getActionIcon(design)}
                          </Button>
                        </motion.div>
                        <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                          <Button variant="ghost" size="icon" title="Thêm tùy chọn">
                            <MoreHorizontal size={16} />
                          </Button>
                        </motion.div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Card>
      </motion.div>

      {/* Pagination */}
      <motion.div
        className="mt-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>

            {[...Array(totalPages)].map((_, index) => {
              const page = index + 1;
              return (
                <PaginationItem key={page}>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <PaginationLink
                      onClick={() => handlePageChange(page)}
                      isActive={currentPage === page}
                      className={`cursor-pointer ${
                        currentPage === page
                          ? 'bg-dexin-primary text-white hover:bg-dexin-primary/90'
                          : 'hover:bg-dexin-light-10'
                      }`}
                    >
                      {page}
                    </PaginationLink>
                  </motion.div>
                </PaginationItem>
              );
            })}

            <PaginationItem>
              <PaginationNext
                onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
                className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </motion.div>

      {/* Add Design Dialog */}
      <AddDesignDialog
        isOpen={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        onSuccess={handleAddSuccess}
      />
    </motion.div>
  );
};

export default DesignManagement;
