import { apiClient } from '../config/api';

/**
 * Service xử lý các API liên quan đến payment
 */
const paymentService = {
  /**
   * Tạo payment order với PayOS
   * @param {number} designId - ID của design cần thanh toán
   * @param {Object} paymentData - Dữ liệu payment
   * @param {string} paymentData.productName - Tên sản phẩm
   * @param {string} paymentData.description - Mô tả
   * @param {number} paymentData.price - Giá
   * @param {string} paymentData.returnUrl - URL khi thanh toán thành công
   * @param {string} paymentData.cancelUrl - URL khi hủy thanh toán
   * @param {number} paymentData.expiredAt - Th<PERSON>i gian hết hạn (timestamp)
   * @returns {Promise<Object>} Response với thông tin payment
   */
  async createPayment(designId, paymentData) {
    try {
      console.log('🔄 Creating payment order...', { designId, paymentData });
      
      // Validate required fields
      const requiredFields = ['productName', 'description', 'price', 'returnUrl', 'cancelUrl', 'expiredAt'];
      for (const field of requiredFields) {
        if (!paymentData[field]) {
          throw new Error(`Thiếu thông tin bắt buộc: ${field}`);
        }
      }

      const response = await apiClient.post(`/PayOSPayments/create?designId=${designId}`, {
        productName: paymentData.productName,
        description: paymentData.description,
        price: Number(paymentData.price),
        returnUrl: paymentData.returnUrl,
        cancelUrl: paymentData.cancelUrl,
        expiredAt: Number(paymentData.expiredAt)
      });

      console.log('✅ Create payment successful:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Tạo đơn thanh toán thành công!'
      };
    } catch (error) {
      console.error('❌ Create payment error:', error);
      
      // Xử lý lỗi cụ thể
      if (error.response?.status === 401) {
        return {
          success: false,
          data: null,
          message: 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.'
        };
      }

      if (error.response?.status === 403) {
        return {
          success: false,
          data: null,
          message: 'Bạn không có quyền thực hiện thao tác này.'
        };
      }

      if (error.response?.status === 404) {
        return {
          success: false,
          data: null,
          message: 'Không tìm thấy thiết kế cần thanh toán.'
        };
      }

      return {
        success: false,
        data: null,
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi tạo đơn thanh toán'
      };
    }
  },

  /**
   * Kiểm tra trạng thái thanh toán
   * @param {string} orderId - ID đơn hàng
   * @returns {Promise<Object>} Response với trạng thái thanh toán
   */
  async checkPaymentStatus(orderId) {
    try {
      console.log('🔄 Checking payment status...', orderId);
      const response = await apiClient.get(`/PayOSPayments/status/${orderId}`);

      console.log('✅ Check payment status successful:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Kiểm tra trạng thái thanh toán thành công'
      };
    } catch (error) {
      console.error('❌ Check payment status error:', error);
      return {
        success: false,
        data: null,
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi kiểm tra trạng thái thanh toán'
      };
    }
  },

  /**
   * Cập nhật trạng thái thanh toán design
   * @param {string} transactionIdGateway - orderCode từ PayOS (không phải paymentLinkId)
   * @param {string} status - Trạng thái thanh toán (PAID, CANCELLED)
   * @returns {Promise<Object>} Response với kết quả cập nhật
   */
  async updateDesignPaymentStatus(transactionIdGateway, status) {
    try {
      console.log('🔄 Updating design payment status...', { transactionIdGateway, status });

      const response = await apiClient.put('/DesignPayments/update-design-payment-status', null, {
        params: {
          transactionIdGateway,
          status
        }
      });

      console.log('✅ Update design payment status successful:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Cập nhật trạng thái thanh toán thành công'
      };
    } catch (error) {
      console.error('❌ Update design payment status error:', error);
      return {
        success: false,
        data: null,
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi cập nhật trạng thái thanh toán'
      };
    }
  },

  /**
   * Lấy lịch sử thanh toán của user
   * @returns {Promise<Object>} Response với lịch sử thanh toán
   */
  async getPaymentHistory() {
    try {
      console.log('🔄 Fetching payment history...');
      const response = await apiClient.get('/PayOSPayments/history');
      
      console.log('✅ Get payment history successful:', response.data);
      return {
        success: true,
        data: response.data.data || [],
        message: response.data.message || 'Lấy lịch sử thanh toán thành công'
      };
    } catch (error) {
      console.error('❌ Get payment history error:', error);
      return {
        success: false,
        data: [],
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi lấy lịch sử thanh toán'
      };
    }
  }
};

export default paymentService;
