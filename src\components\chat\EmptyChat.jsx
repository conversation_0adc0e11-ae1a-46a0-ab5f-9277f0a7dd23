import React from 'react';
import { motion } from 'motion/react';
import { ChatSidebar } from './index';

const EmptyChat = React.memo(({ showChatList }) => (
  <div className="flex h-screen bg-white">
    {/* Sidebar bên trái */}
    <ChatSidebar
      isEmpty={true}
      onStartChat={showChatList}
      className="w-full sm:w-1/3 md:w-1/4 h-full"
    />

    {/* Phần nội dung chính bên phải */}
    <motion.div
      className="hidden sm:flex flex-grow bg-gray-50 items-center justify-center"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-5xl w-full flex flex-col items-center px-8">
        {/* Phần tiêu đề phía trên */}
        <motion.div
          className="mb-10 text-center"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.h2
            className="text-3xl sm:text-4xl text-dexin-primary font-bold mb-4"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            Rất vui được đồng hành cùng bạn!
          </motion.h2>
          <motion.p
            className="text-2xl sm:text-3xl text-pink-400 font-medium"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.6 }}
          >
            Có ý tưởng decor nhưng chưa rõ bắt đầu từ đâu? <motion.span
              className="text-3xl"
              animate={{
                rotate: [0, 10, 0, -10, 0],
                scale: [1, 1.2, 1, 1.2, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatDelay: 1
              }}
            >🤔</motion.span>
          </motion.p>
        </motion.div>

        {/* Phần nội dung chính - sử dụng layout theo hàng ngang */}
        <div className="flex w-full justify-between items-center mt-10">
          {/* Cột bên trái - Thông điệp */}
          <motion.div
            className="w-1/2 pr-8"
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="space-y-6">
              <motion.h3
                className="text-4xl sm:text-5xl text-dexin-primary font-bold"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                Không sao cả,
              </motion.h3>
              <motion.h3
                className="text-4xl sm:text-5xl text-dexin-primary font-bold leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                tụi mình ở đây để giúp bạn
              </motion.h3>
            </div>

            {/* Phần "Bấm chat..." ở dưới bên trái */}
            <motion.div
              className="mt-24"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1 }}
            >
              <motion.h3
                className="text-xl sm:text-2xl text-gray-800 font-medium cursor-pointer hover:text-dexin-primary transition-colors"
                onClick={showChatList}
                whileHover={{ scale: 1.03, color: "#FE7CAB" }}
                whileTap={{ scale: 0.98 }}
              >
                Bấm "Chat với tư vấn viên" ngay để cùng nhau<br />
                tạo nên một không gian thật chill nha! <motion.span
                  className="ml-1 text-2xl"
                  animate={{
                    rotate: [0, 10, 0, -10, 0],
                  }}
                  transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 2 }}
                >😊</motion.span>
              </motion.h3>
            </motion.div>
          </motion.div>

          {/* Cột bên phải - Hình ảnh */}
          <motion.div
            className="w-1/2 flex items-center justify-center"
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <motion.img
              src="/images/ty1.png"
              alt="Tư vấn viên"
              className="max-h-[500px] w-auto object-contain"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.7 }}
              whileHover={{ scale: 1.05, rotate: 2 }}
            />
          </motion.div>
        </div>
      </div>
    </motion.div>
  </div>
));

export default EmptyChat;