import{av as e,r as s,j as t,aw as a,ab as n,ax as r}from"./index-DdBL2cja.js";const i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=e,c=(e,s)=>t=>{var a;if(null==(null==s?void 0:s.variants))return o(e,null==t?void 0:t.class,null==t?void 0:t.className);const{variants:n,defaultVariants:r}=s,c=Object.keys(n).map((e=>{const s=null==t?void 0:t[e],a=null==r?void 0:r[e];if(null===s)return null;const o=i(s)||i(a);return n[e][o]})),d=t&&Object.entries(t).reduce(((e,s)=>{let[t,a]=s;return void 0===a||(e[t]=a),e}),{}),l=null==s||null===(a=s.compoundVariants)||void 0===a?void 0:a.reduce(((e,s)=>{let{class:t,className:a,...n}=s;return Object.entries(n).every((e=>{let[s,t]=e;return Array.isArray(t)?t.includes({...r,...d}[s]):{...r,...d}[s]===t}))?[...e,t,a]:e}),[]);return o(e,c,l,null==t?void 0:t.class,null==t?void 0:t.className)},d=c("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((({className:e,variant:s,size:r,asChild:i=!1,...o},c)=>{const l=i?a:"button";return t.jsx(l,{className:n(d({variant:s,size:r,className:e})),ref:c,...o})}));l.displayName="Button";const u=r.create({baseURL:"https://dexin.onrender.com/api",timeout:3e4,headers:{"Content-Type":"application/json"}});u.interceptors.request.use((e=>{const s=localStorage.getItem("dexin_token");return s&&(e.headers.Authorization=`Bearer ${s}`),e}),(e=>Promise.reject(e))),u.interceptors.response.use((e=>e),(e=>Promise.reject(e)));const g={async getAllDesigns(){var e,s;try{const e=await u.get("/Designs/getAll");return{success:!0,data:e.data.data||[],message:e.data.message||"Lấy danh sách thiết kế thành công"}}catch(t){return{success:!1,data:[],message:(null==(s=null==(e=t.response)?void 0:e.data)?void 0:s.message)||t.message||"Có lỗi xảy ra khi lấy danh sách thiết kế"}}},async create3DDesign(e){var s,t,a,n;try{const s=localStorage.getItem("dexin_token");if(!s)throw new Error("Không tìm thấy token. Vui lòng đăng nhập lại.");const t=new FormData;t.append("Title",e.title),t.append("PathUrl",e.pathUrl),t.append("ThumbnailUrl",e.thumbnailUrl),t.append("Note",e.note||""),t.append("Price",e.price.toString()),t.append("CustomerId",e.customerId.toString());for(let[e,n]of t.entries())File;const a=await u.post("/Designs/create-3d",t,{headers:{"Content-Type":void 0,Authorization:`Bearer ${s}`}});return{success:!0,data:a.data.data,message:a.data.message||"Tạo thiết kế 3D thành công!"}}catch(r){return 401===(null==(s=r.response)?void 0:s.status)?{success:!1,data:null,message:"Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."}:403===(null==(t=r.response)?void 0:t.status)?{success:!1,data:null,message:"Bạn không có quyền thực hiện thao tác này."}:{success:!1,data:null,message:(null==(n=null==(a=r.response)?void 0:a.data)?void 0:n.message)||r.message||"Có lỗi xảy ra khi tạo thiết kế 3D"}}},async getAllCustomers(){var e,s;try{const e=await u.get("/SystemUserAccounts/get-all-customers");return{success:!0,data:e.data.data||[],message:e.data.message||"Lấy danh sách khách hàng thành công"}}catch(t){return{success:!1,data:[],message:(null==(s=null==(e=t.response)?void 0:e.data)?void 0:s.message)||t.message||"Có lỗi xảy ra khi lấy danh sách khách hàng"}}},async updateDesignStatus(e,s){var t,a;try{const t=await u.put(`/Designs/${e}/status`,{status:s});return{success:!0,data:t.data.data,message:t.data.message||"Cập nhật trạng thái thành công"}}catch(n){return{success:!1,data:null,message:(null==(a=null==(t=n.response)?void 0:t.data)?void 0:a.message)||n.message||"Có lỗi xảy ra khi cập nhật trạng thái"}}},async deleteDesign(e){var s,t;try{const s=await u.delete(`/Designs/${e}`);return{success:!0,data:s.data.data,message:s.data.message||"Xóa thiết kế thành công"}}catch(a){return{success:!1,data:null,message:(null==(t=null==(s=a.response)?void 0:s.data)?void 0:t.message)||a.message||"Có lỗi xảy ra khi xóa thiết kế"}}},async getDesignsByUser(e){var s,t;try{const s=await u.get(`/Designs/getByUser/${e}`);return{success:!0,data:s.data.data||[],message:s.data.message||"Lấy danh sách thiết kế thành công"}}catch(a){return{success:!1,data:[],message:(null==(t=null==(s=a.response)?void 0:s.data)?void 0:t.message)||a.message||"Có lỗi xảy ra khi lấy danh sách thiết kế"}}}};export{l as B,d as b,c,g as d};
