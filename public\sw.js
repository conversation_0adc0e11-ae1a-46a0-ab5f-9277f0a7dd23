// DEXIN Service Worker - Fixed Version
const CACHE_NAME = 'dexin-cache-v4';
const STATIC_CACHE_NAME = 'dexin-static-v4';
const DYNAMIC_CACHE_NAME = 'dexin-dynamic-v4';

// Essential files to cache
const urlsToCache = [
  '/',
  '/manifest.json',
  '/images/icons/icon-48x48.png',
  '/images/icons/icon-72x72.png',
  '/images/icons/icon-96x96.png',
  '/images/icons/icon-144x144.png',
  '/images/icons/icon-192x192.png',
  '/images/icons/icon-256x256.png',
  '/images/icons/icon-384x384.png',
  '/images/icons/icon-512x512.png'
];

// Install event - Force clear old caches
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.all([
      // Clear all old caches first
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName.includes('dexin-cache') || 
                cacheName.includes('dexin-static') || 
                cacheName.includes('dexin-dynamic')) {
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Then create new cache
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        return cache.addAll(urlsToCache).catch((error) => {
          console.warn('Cache addAll failed:', error);
          return Promise.resolve();
        });
      }),
      self.skipWaiting() // Force activation immediately
    ])
  );
});

// Activate event - Clean up old caches and take control
self.addEventListener('activate', (event) => {
  event.waitUntil(
    Promise.all([
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME) {
              return caches.delete(cacheName);
            }
          })
        );
      }),
      self.clients.claim() // Take control immediately
    ])
  );
});

// Fetch event - Network-first strategy for JS modules
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip external requests
  if (url.origin !== location.origin) {
    return;
  }

  // Network-first for JS/CSS files to avoid MIME type issues
  if (request.url.includes('.js') || 
      request.url.includes('.css') || 
      request.url.includes('.jsx') ||
      request.url.includes('/src/')) {
    
    event.respondWith(
      fetch(request).then((response) => {
        if (response && response.status === 200) {
          const responseClone = response.clone();
          caches.open(DYNAMIC_CACHE_NAME).then((cache) => {
            cache.put(request, responseClone);
          });
        }
        return response;
      }).catch(() => {
        return caches.match(request).then((cachedResponse) => {
          return cachedResponse || new Response('Network Error', { status: 503 });
        });
      })
    );
    return;
  }

  // Cache-first for other resources
  event.respondWith(
    caches.match(request).then((cachedResponse) => {
      if (cachedResponse) {
        return cachedResponse;
      }

      return fetch(request).then((response) => {
        if (!response || response.status !== 200 || response.type !== 'basic') {
          return response;
        }

        // Cache images and static assets
        if (request.destination === 'image' || 
            request.url.includes('/images/') ||
            request.url.includes('.png') ||
            request.url.includes('.jpg') ||
            request.url.includes('.jpeg') ||
            request.url.includes('.svg') ||
            request.url.includes('.ico')) {
          
          const responseClone = response.clone();
          caches.open(DYNAMIC_CACHE_NAME).then((cache) => {
            cache.put(request, responseClone);
          });
        }

        return response;
      }).catch(() => {
        if (request.mode === 'navigate') {
          return caches.match('/').then((fallback) => {
            return fallback || new Response('Offline', { 
              status: 200, 
              headers: { 'Content-Type': 'text/html' } 
            });
          });
        }
        
        return new Response('Offline', { status: 503 });
      });
    })
  );
});

// Message event - Handle cache clearing
self.addEventListener('message', (event) => {
  const { data } = event;
  
  if (data && data.type === 'SKIP_WAITING') {
    self.skipWaiting();
    event.ports[0]?.postMessage({ success: true });
    return;
  }
  
  if (data && data.type === 'CLEAR_CACHE') {
    event.waitUntil(
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => caches.delete(cacheName))
        );
      })
    );
    event.ports[0]?.postMessage({ cleared: true });
    return;
  }
  
  if (data && data.type === 'GET_VERSION') {
    event.ports[0]?.postMessage({ version: CACHE_NAME });
    return;
  }
  
  event.ports[0]?.postMessage({ received: true });
});

// Background sync for better offline experience
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      Promise.resolve()
    );
  }
}); 