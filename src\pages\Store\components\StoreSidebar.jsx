import { useState, useMemo, useCallback, memo, useEffect, createContext, useContext } from 'react';
import { motion, AnimatePresence, useReducedMotion } from 'motion/react';
import { Search, ChevronDown, ChevronUp, SlidersVertical, Check } from 'lucide-react';
import { Range, getTrackBackground } from 'react-range';
import Button from '../../../components/common/Button';
import { PRICE_RANGE, SORT_OPTIONS, SORT_LABELS } from '../../../constants';

// Tạo context để chia sẻ các giá trị cần thiết giữa các component
const SidebarContext = createContext({
  prefersReducedMotion: false,
  localSearchTerm: '',
  setLocalSearchTerm: () => {},
});

// Hàm định dạng giá tiền theo định dạng tiền Việt Nam
const formatPrice = (price) => {
  // Làm tròn giá trị để tránh số thập phân
  const roundedPrice = Math.round(price);
  return new Intl.NumberFormat('vi-VN').format(roundedPrice);
};

// Tạo component con cho danh mục để tránh re-render không cần thiết
const CategoryItem = memo(({ category, selectedCategory, setSelectedCategory }) => {
  // Kiểm tra nếu người dùng ưu tiên giảm chuyển động
  const prefersReducedMotion = useReducedMotion();

  // Xử lý click vào danh mục - chỉ cập nhật state nội bộ, không cập nhật state cha
  const handleClick = () => {
    setSelectedCategory(category.id);
  };

  return (
    <motion.div
      className="flex justify-between items-center py-2 border-b border-gray-100 cursor-pointer hover:text-dexin-primary"
      onClick={handleClick}
      whileHover={prefersReducedMotion ? {} : { x: 5 }}
      whileTap={prefersReducedMotion ? {} : { scale: 0.98 }}
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.2 }}
    >
      <span className={selectedCategory === category.id ? 'text-dexin-primary font-medium' : ''}>
        {category.name}
      </span>
      <motion.span
        animate={selectedCategory === category.id && !prefersReducedMotion ? { x: [0, 3, 0] } : {}}
        transition={{ duration: 0.3 }}
      >
        {category.icon}
      </motion.span>
    </motion.div>
  );
});

// Tạo component con cho thanh trượt giá
const PriceRangeSlider = memo(({ priceRange, localPriceRange, setLocalPriceRange }) => {
  // Sử dụng giá trị từ constants
  const MIN_PRICE = PRICE_RANGE.MIN;
  const MAX_PRICE = PRICE_RANGE.MAX;

  // Cập nhật localRange khi priceRange thay đổi từ bên ngoài (chỉ khi áp dụng bộ lọc)
  useEffect(() => {
    if (priceRange && priceRange.length === 2) {
      setLocalPriceRange(priceRange);
    }
  }, [priceRange, setLocalPriceRange]);

  // Xử lý khi giá trị thay đổi - chỉ cập nhật state nội bộ
  const handleChange = (values) => {
    setLocalPriceRange(values);
  };

  // Xử lý khi kết thúc kéo - chỉ cập nhật state nội bộ
  const handleFinalChange = (values) => {
    setLocalPriceRange(values);
  };

  return (
    <motion.div
      className="mt-4 px-4 py-2"
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="h-10">
        <Range
          step={10000}
          min={MIN_PRICE}
          max={MAX_PRICE}
          values={localPriceRange}
          onChange={handleChange}
          onFinalChange={handleFinalChange}
          renderTrack={({ props, children }) => (
            <div
              className="w-full h-2 rounded-full bg-gray-200 flex"
              style={{
                ...props.style,
              }}
              ref={props.ref}
            >
              <div
                className="h-2 rounded-full absolute"
                style={{
                  background: getTrackBackground({
                    values: localPriceRange,
                    colors: ["#F3F4F6", "#FE7CAB", "#F3F4F6"],
                    min: MIN_PRICE,
                    max: MAX_PRICE
                  }),
                  width: '100%',
                  height: '100%'
                }}
              />
              {children}
            </div>
          )}
          renderThumb={({ props, isDragged }) => (
            <div
              key={`thumb-${props["aria-valuenow"]}`}
              className="w-5 h-5 bg-dexin-light rounded-full focus:outline-none shadow-md flex items-center justify-center cursor-grab active:cursor-grabbing"
              style={{
                ...props.style,
                boxShadow: isDragged ? "0 0 0 5px rgba(255, 107, 107, 0.2)" : undefined,
                zIndex: 10
              }}
              tabIndex={props.tabIndex}
              aria-valuemax={props["aria-valuemax"]}
              aria-valuemin={props["aria-valuemin"]}
              aria-valuenow={props["aria-valuenow"]}
              draggable={props.draggable}
              ref={props.ref}
              role={props.role}
              aria-label={props["aria-label"]}
              aria-labelledby={props["aria-labelledby"]}
              onKeyDown={props.onKeyDown}
              onKeyUp={props.onKeyUp}
              onKeyPress={props.onKeyPress}
              onClick={props.onClick}
              onMouseDown={props.onMouseDown}
              onMouseUp={props.onMouseUp}
              onTouchStart={props.onTouchStart}
              onTouchEnd={props.onTouchEnd}
            />
          )}
        />
      </div>

      {/* Hiển thị giá trị */}
      <div className="flex justify-between mt-2">
        <span className="text-sm">
          {formatPrice(localPriceRange[0])}
        </span>
        <span className="text-sm">
          {formatPrice(localPriceRange[1])}
        </span>
      </div>
    </motion.div>
  );
});

// Tạo component con cho nhóm để tránh re-render không cần thiết
const GroupItem = memo(({ group }) => {
  // Kiểm tra nếu người dùng ưu tiên giảm chuyển động
  const prefersReducedMotion = useReducedMotion();

  return (
    <motion.button
      className="px-3 py-1 bg-gray-100 rounded-full text-sm hover:bg-gray-200"
      whileHover={prefersReducedMotion ? {} : { scale: 1.05, backgroundColor: '#f3f4f6' }}
      whileTap={prefersReducedMotion ? {} : { scale: 0.95 }}
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      {group.name}
    </motion.button>
  );
});

// Tạo component con cho thanh tìm kiếm để tránh re-render không cần thiết
const SearchInput = memo(({ localSearchTerm, setLocalSearchTerm }) => {
  // Kiểm tra nếu người dùng ưu tiên giảm chuyển động
  const prefersReducedMotion = useReducedMotion();

  // Xử lý thay đổi giá trị tìm kiếm - chỉ cập nhật state nội bộ
  const handleSearchChange = useCallback((e) => {
    setLocalSearchTerm(e.target.value);
  }, [setLocalSearchTerm]);

  return (
    <motion.div
      className="mb-6"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="relative">
        <motion.input
          type="text"
          placeholder="Tìm kiếm..."
          className="w-full pl-10 pr-4 py-2 bg-pink-100 rounded-full focus:outline-none focus:ring-2 focus:ring-dexin-light focus:border-transparent"
          value={localSearchTerm}
          onChange={handleSearchChange}
          whileFocus={prefersReducedMotion ? {} : { scale: 1.01 }}
          transition={{ duration: 0.2 }}
        />
        <motion.div
          className="absolute left-3 top-2.5 text-gray-400"
          animate={prefersReducedMotion ? {} : {
            rotate: localSearchTerm ? [0, -10, 10, -5, 5, 0] : 0
          }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Search className="h-5 w-5" />
        </motion.div>
      </div>
    </motion.div>
  );
});

// Tạo component con cho tiêu đề bộ lọc để tránh re-render không cần thiết
const FilterTitle = memo(() => {
  // Kiểm tra nếu người dùng ưu tiên giảm chuyển động
  const prefersReducedMotion = useReducedMotion();

  return (
    <motion.div
      className="flex justify-between items-center mb-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3, delay: 0.1 }}
    >
      <motion.h2
        className="text-xl font-bold"
        initial={{ x: -10 }}
        animate={{ x: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        Bộ Lọc
      </motion.h2>
      <motion.button
        className="text-gray-400"
        whileHover={prefersReducedMotion ? {} : { rotate: 180 }}
        transition={{ duration: 0.3 }}
      >
        <SlidersVertical className="h-5 w-5" />
      </motion.button>
    </motion.div>
  );
});

// Tạo component con cho phần sắp xếp để tránh re-render không cần thiết
const SortSection = memo(({ sortOption, setSortOption }) => {
  // Kiểm tra nếu người dùng ưu tiên giảm chuyển động
  const prefersReducedMotion = useReducedMotion();

  // State để theo dõi trạng thái mở/đóng của dropdown
  const [isOpen, setIsOpen] = useState(false);

  // Xử lý khi chọn một tùy chọn sắp xếp
  const handleSelectOption = useCallback((option) => {
    setSortOption(option);
    setIsOpen(false);
  }, [setSortOption]);

  // Đóng dropdown khi click ra ngoài
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen && !event.target.closest('.sort-dropdown')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <motion.div
      className="mb-6 relative sort-dropdown"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    >
      <div className="flex flex-col space-y-2 w-full">
        <div className="flex items-center justify-between w-full">
          <span className="text-gray-600 whitespace-nowrap">Lọc theo:</span>
          <motion.button
            className="flex items-center justify-between text-dexin-light min-w-[160px] px-3 py-1.5 border border-transparent hover:border-gray-200 rounded-md"
            whileHover={prefersReducedMotion ? {} : { scale: 1.02 }}
            whileTap={prefersReducedMotion ? {} : { scale: 0.98 }}
            onClick={() => setIsOpen(!isOpen)}
            aria-expanded={isOpen}
            aria-haspopup="true"
          >
            <span className="truncate">{SORT_LABELS[sortOption]}</span>
            <motion.div
              animate={prefersReducedMotion ? {} : {
                y: isOpen ? 0 : [0, 2, 0],
                rotate: isOpen ? 180 : 0
              }}
              transition={{
                y: { repeat: isOpen ? 0 : Infinity, repeatDelay: 1.5, duration: 0.5 },
                rotate: { duration: 0.3 }
              }}
              className="flex-shrink-0"
            >
              <ChevronDown className="h-4 w-4 ml-1" />
            </motion.div>
          </motion.button>
        </div>
      </div>

      {/* Dropdown menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute z-10 mt-1 right-0 w-64 bg-white rounded-md shadow-lg overflow-hidden ring-1 ring-black ring-opacity-5 focus:outline-none"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="sort-menu"
          >
            {Object.entries(SORT_LABELS).map(([option, label]) => (
              <motion.button
                key={`sort-option-${option}`}
                className={`w-full px-3 py-2 text-left hover:bg-gray-50 ${sortOption === option ? 'text-dexin-primary' : 'text-gray-700'}`}
                onClick={() => handleSelectOption(option)}
                whileHover={prefersReducedMotion ? {} : { x: 2 }}
                whileTap={prefersReducedMotion ? {} : { scale: 0.98 }}
              >
                <div className="flex items-center justify-between">
                  <span>{label}</span>
                  {sortOption === option && <Check className="h-4 w-4" />}
                </div>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
});

const StoreSidebar = ({
  searchTerm,
  setSearchTerm,
  activeCategory,
  setActiveCategory,
  priceRange,
  setPriceRange,
  sortOption,
  setSortOption,
  applyFilters
}) => {
  // State nội bộ cho sidebar - chỉ áp dụng khi nhấn nút "Áp dụng bộ lọc"
  const [localSearchTerm, setLocalSearchTerm] = useState('');
  const [localCategory, setLocalCategory] = useState('all');
  const [localPriceRange, setLocalPriceRange] = useState([PRICE_RANGE.MIN, PRICE_RANGE.MAX]);
  const [localSortOption, setLocalSortOption] = useState(SORT_OPTIONS.NEWEST);

  const [expandedSections, setExpandedSections] = useState({
    price: true,
    group: true
  });

  // Khởi tạo state nội bộ từ props khi component được mount
  useEffect(() => {
    setLocalSearchTerm(searchTerm);
    setLocalCategory(activeCategory);
    setLocalPriceRange(priceRange);
    setLocalSortOption(sortOption);
  }, [searchTerm, activeCategory, priceRange, sortOption]);

  // Danh sách danh mục - sử dụng useMemo để tránh tạo lại mỗi lần render
  const categories = useMemo(() => [
    { id: 'all', name: 'Tất cả', icon: '>' },
    { id: 'ban', name: 'Bàn', icon: '>' },
    { id: 'ghe', name: 'Ghế', icon: '>' },
    { id: 'tu', name: 'Tủ', icon: '>' },
    { id: 'den', name: 'Đèn', icon: '>' },
    { id: 'giuong', name: 'Giường', icon: '>' },
    { id: 'ke', name: 'Kệ', icon: '>' },
  ], []);

  // Danh sách nhóm - sử dụng useMemo để tránh tạo lại mỗi lần render
  const groups = useMemo(() => [
    { id: 'dongho', name: 'Đồng hồ' },
    { id: 'tham', name: 'Thảm' },
    { id: 'guong', name: 'Gương' },
    { id: 'rem', name: 'Rèm' },
    { id: 'giaydan', name: 'Giấy dán tường' },
    { id: 'khac', name: 'Khác' },
  ], []);

  // Toggle section expansion - sử dụng useCallback để tránh tạo lại mỗi lần render
  const toggleSection = useCallback((section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  }, []);

  // Xử lý áp dụng bộ lọc - chỉ khi nhấn nút "Áp dụng bộ lọc"
  const handleApplyFilters = useCallback(() => {
    // Cập nhật state cha từ state nội bộ
    setSearchTerm(localSearchTerm);
    setActiveCategory(localCategory);
    setPriceRange(localPriceRange);
    setSortOption(localSortOption);

    // Gọi hàm applyFilters từ props
    applyFilters();
  }, [localSearchTerm, localCategory, localPriceRange, localSortOption, setSearchTerm, setActiveCategory, setPriceRange, setSortOption, applyFilters]);

  // Kiểm tra nếu người dùng ưu tiên giảm chuyển động
  const prefersReducedMotion = useReducedMotion();

  // Sử dụng useMemo để tránh tạo lại các component con mỗi lần render
  const memoizedSearchInput = useMemo(() => (
    <SearchInput
      localSearchTerm={localSearchTerm}
      setLocalSearchTerm={setLocalSearchTerm}
    />
  ), [localSearchTerm, setLocalSearchTerm]);

  const memoizedSortSection = useMemo(() => (
    <SortSection
      sortOption={localSortOption}
      setSortOption={setLocalSortOption}
    />
  ), [localSortOption, setLocalSortOption]);

  const memoizedFilterTitle = useMemo(() => (
    <FilterTitle />
  ), []);

  const memoizedCategories = useMemo(() => (
    <motion.div
      className="mb-6"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.25 }}
    >
      {categories.map((category, index) => (
        <motion.div
          key={`category-${category.id}`}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
        >
          <CategoryItem
            category={category}
            selectedCategory={localCategory}
            setSelectedCategory={setLocalCategory}
          />
        </motion.div>
      ))}
    </motion.div>
  ), [categories, localCategory, setLocalCategory]);

  const memoizedPriceRange = useMemo(() => (
    <motion.div
      className="mb-6"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.3 }}
    >
      <motion.div
        className="flex justify-between items-center mb-2 cursor-pointer"
        onClick={() => toggleSection('price')}
        whileHover={prefersReducedMotion ? {} : { x: 3 }}
        whileTap={prefersReducedMotion ? {} : { scale: 0.98 }}
      >
        <h3 className="font-bold">Khoảng giá (VND)</h3>
        <AnimatePresence mode="wait">
          {expandedSections.price ? (
            <motion.div
              key="up"
              initial={{ opacity: 0, rotate: 180 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: 180 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronUp className="h-5 w-5" />
            </motion.div>
          ) : (
            <motion.div
              key="down"
              initial={{ opacity: 0, rotate: -180 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: -180 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="h-5 w-5" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      <AnimatePresence>
        {expandedSections.price && (
          <PriceRangeSlider
            priceRange={priceRange}
            localPriceRange={localPriceRange}
            setLocalPriceRange={setLocalPriceRange}
          />
        )}
      </AnimatePresence>
    </motion.div>
  ), [expandedSections.price, localPriceRange, priceRange, prefersReducedMotion, setLocalPriceRange, toggleSection]);

  const memoizedGroups = useMemo(() => (
    <motion.div
      className="mb-6"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.4 }}
    >
      <motion.div
        className="flex justify-between items-center mb-2 cursor-pointer"
        onClick={() => toggleSection('group')}
        whileHover={prefersReducedMotion ? {} : { x: 3 }}
        whileTap={prefersReducedMotion ? {} : { scale: 0.98 }}
      >
        <h3 className="font-bold">Nhóm</h3>
        <AnimatePresence mode="wait">
          {expandedSections.group ? (
            <motion.div
              key="up"
              initial={{ opacity: 0, rotate: 180 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: 180 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronUp className="h-5 w-5" />
            </motion.div>
          ) : (
            <motion.div
              key="down"
              initial={{ opacity: 0, rotate: -180 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: -180 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="h-5 w-5" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      <AnimatePresence>
        {expandedSections.group && (
          <motion.div
            className="mt-4 flex flex-wrap gap-2"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            {groups.map((group, index) => (
              <GroupItem key={`group-${group.id}`} group={group} />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  ), [expandedSections.group, groups, prefersReducedMotion, toggleSection]);

  const memoizedApplyButton = useMemo(() => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.5 }}
      className="w-full"
    >
      <Button
        variant="click"
        size="md"
        rounded={true}
        onClick={handleApplyFilters}
        className="w-full shadow-md"
      >
        Áp Dụng Bộ Lọc
      </Button>
    </motion.div>
  ), [handleApplyFilters]);

  return (
    <motion.div
      className="bg-white rounded-lg shadow-md p-5 w-full max-w-xl"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      layout
    >
      {/* Search */}
      {memoizedSearchInput}

      {/* Sort */}
      {memoizedSortSection}

      <motion.div
        className="border-t border-gray-200 my-5"
        initial={{ scaleX: 0 }}
        animate={{ scaleX: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      />

      {/* Filter Title */}
      {memoizedFilterTitle}

      {/* Categories */}
      {memoizedCategories}

      {/* Price Range */}
      {memoizedPriceRange}

      {/* Groups */}
      {memoizedGroups}

      {/* Apply Button */}
      {memoizedApplyButton}
    </motion.div>
  );
};

export default StoreSidebar;
