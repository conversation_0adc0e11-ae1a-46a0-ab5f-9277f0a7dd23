/**
 * Utility functions for exporting canvas content
 */
import { jsPDF } from 'jspdf';

/**
 * Export Konva stage as PNG image
 * @param {Object} stage - Konva stage reference
 * @param {string} fileName - Name of the file to download (without extension)
 * @param {number} pixelRatio - Quality of the export (default: 2 for high quality)
 * @returns {Promise<{success: boolean, message: string, fileName: string}>} - Result of the export operation
 */
export const exportAsPNG = (stage, fileName = 'phac-y-canvas', pixelRatio = 2) => {
  return new Promise((resolve) => {
    if (!stage) {
      console.error('Stage reference is required for PNG export');
      resolve({
        success: false,
        message: 'Không thể xuất ảnh: Không tìm thấy canvas',
        fileName: null
      });
      return;
    }

    try {
      // Tìm và lưu trạng thái hiển thị của tất cả transformers
      const transformers = stage.find('Transformer');
      const transformersVisibility = transformers.map(tr => tr.visible());

      // Ẩn tất cả transformers trước khi xuất
      transformers.forEach(tr => tr.visible(false));

      // Buộc cập nhật lại stage
      stage.batchDraw();

      // Get data URL with high quality
      const dataURL = stage.toDataURL({
        pixelRatio: pixelRatio,
        mimeType: 'image/png'
      });

      // Khôi phục trạng thái hiển thị của transformers
      transformers.forEach((tr, i) => tr.visible(transformersVisibility[i]));

      // Buộc cập nhật lại stage
      stage.batchDraw();

      // Create download link
      const link = document.createElement('a');
      link.download = `${fileName}.png`;
      link.href = dataURL;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Return success
      resolve({
        success: true,
        message: 'Xuất ảnh PNG thành công!',
        fileName: `${fileName}.png`
      });
    } catch (error) {
      console.error('Error exporting as PNG:', error);
      resolve({
        success: false,
        message: `Lỗi khi xuất ảnh: ${error.message || 'Lỗi không xác định'}`,
        fileName: null
      });
    }
  });
};

/**
 * Export Konva stage as PDF
 * @param {Object} stage - Konva stage reference
 * @param {string} fileName - Name of the file to download (without extension)
 * @param {number} pixelRatio - Quality of the export (default: 2 for high quality)
 * @returns {Promise<{success: boolean, message: string, fileName: string}>} - Result of the export operation
 */
export const exportAsPDF = (stage, fileName = 'phac-y-canvas', pixelRatio = 2) => {
  return new Promise((resolve) => {
    if (!stage) {
      console.error('Stage reference is required for PDF export');
      resolve({
        success: false,
        message: 'Không thể xuất PDF: Không tìm thấy canvas',
        fileName: null
      });
      return;
    }

    try {
      // Tìm và lưu trạng thái hiển thị của tất cả transformers
      const transformers = stage.find('Transformer');
      const transformersVisibility = transformers.map(tr => tr.visible());

      // Ẩn tất cả transformers trước khi xuất
      transformers.forEach(tr => tr.visible(false));

      // Buộc cập nhật lại stage
      stage.batchDraw();

      // Get stage dimensions
      const width = stage.width();
      const height = stage.height();

      // Create PDF with appropriate orientation
      const orientation = width > height ? 'l' : 'p'; // landscape or portrait
      const pdf = new jsPDF(orientation, 'px', [width, height]);

      // Get stage as image data URL
      const dataURL = stage.toDataURL({
        pixelRatio: pixelRatio,
        mimeType: 'image/jpeg',
        quality: 0.9
      });

      // Khôi phục trạng thái hiển thị của transformers
      transformers.forEach((tr, i) => tr.visible(transformersVisibility[i]));

      // Buộc cập nhật lại stage
      stage.batchDraw();

      // Add image to PDF
      pdf.addImage(
        dataURL,
        'JPEG',
        0,
        0,
        width,
        height
      );

      // Save PDF
      pdf.save(`${fileName}.pdf`);

      // Return success
      resolve({
        success: true,
        message: 'Xuất file PDF thành công!',
        fileName: `${fileName}.pdf`
      });
    } catch (error) {
      console.error('Error exporting as PDF:', error);
      resolve({
        success: false,
        message: `Lỗi khi xuất PDF: ${error.message || 'Lỗi không xác định'}`,
        fileName: null
      });
    }
  });
};
