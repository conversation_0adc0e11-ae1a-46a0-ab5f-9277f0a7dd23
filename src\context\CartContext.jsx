import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';

// Tạo context cho giỏ hàng
export const CartContext = createContext();

// Custom hook để sử dụng context
export const useCart = () => {
  return useContext(CartContext);
};

export const CartProvider = ({ children }) => {
  // State cho giỏ hàng
  const [cartItems, setCartItems] = useState([]);
  const [cartCount, setCartCount] = useState(0);
  const [cartTotal, setCartTotal] = useState(0);
  
  // Tải giỏ hàng từ localStorage khi component được mount
  useEffect(() => {
    const storedCart = localStorage.getItem('dexin-cart');
    if (storedCart) {
      const parsedCart = JSON.parse(storedCart);
      setCartItems(parsedCart);
    }
  }, []);

  // Cập nhật localStorage và tính toán khi cartItems thay đổi
  useEffect(() => {
    localStorage.setItem('dexin-cart', JSON.stringify(cartItems));
    
    const itemCount = cartItems.reduce((total, item) => total + item.quantity, 0);
    const totalPrice = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
    
    setCartCount(itemCount);
    setCartTotal(totalPrice);
  }, [cartItems]);

  // Tạo unique key cho sản phẩm dựa trên id và variant
  const createCartItemKey = (productId, variantId) => {
    return variantId ? `${productId}-${variantId}` : `${productId}`;
  };

  // Thêm sản phẩm vào giỏ hàng
  const addToCart = useCallback((product, quantity = 1) => {
    if (!product) return;
    
    setCartItems(prevItems => {
      const cartKey = createCartItemKey(product.id, product.variantId);
      
      // Kiểm tra xem sản phẩm với variant này đã có trong giỏ hàng chưa
      const existingItemIndex = prevItems.findIndex(item => {
        const itemKey = createCartItemKey(item.id, item.variantId);
        return itemKey === cartKey;
      });
      
      let newItems;
      if (existingItemIndex !== -1) {
        // Nếu sản phẩm với variant này đã có trong giỏ hàng, tăng số lượng lên
        newItems = [...prevItems];
        newItems[existingItemIndex] = {
          ...newItems[existingItemIndex],
          quantity: newItems[existingItemIndex].quantity + quantity
        };
      } else {
        // Nếu sản phẩm với variant này chưa có trong giỏ hàng, thêm mới
        const cartItem = {
          id: product.id,
          name: product.name,
          price: product.price,
          image: product.image,
          quantity: quantity,
          // Thông tin variant
          variantId: product.variantId || null,
          variantName: product.variantName || '',
          variantColor: product.variantColor || '',
          // Thêm cartKey để dễ quản lý
          cartKey: cartKey
        };
        newItems = [...prevItems, cartItem];
      }
      
      // Lưu vào localStorage
      localStorage.setItem('dexin-cart', JSON.stringify(newItems));
      return newItems;
    });
  }, []);

  // Cập nhật số lượng sản phẩm trong giỏ hàng
  const updateQuantity = (cartKey, quantity) => {
    if (quantity <= 0) {
      removeFromCart(cartKey);
      return;
    }
    
    setCartItems(prevItems => 
      prevItems.map(item => 
        item.cartKey === cartKey ? { ...item, quantity } : item
      )
    );
  };

  // Xóa sản phẩm khỏi giỏ hàng
  const removeFromCart = (cartKey) => {
    setCartItems(prevItems => {
      const itemToRemove = prevItems.find(item => item.cartKey === cartKey);
      const newItems = prevItems.filter(item => item.cartKey !== cartKey);
      
      // Lưu vào localStorage
      localStorage.setItem('dexin-cart', JSON.stringify(newItems));
      
      // Hiển thị toast thông báo với ID duy nhất
      if (itemToRemove) {
        const displayName = itemToRemove.variantName 
          ? `${itemToRemove.name} (${itemToRemove.variantName})`
          : itemToRemove.name;
          
        toast.info(`Đã xóa "${displayName}" khỏi giỏ hàng`, {
          icon: () => <span style={{ fontSize: '1.2rem', marginRight: '10px' }}>🗑️</span>,
          className: 'toast-message',
          toastId: `remove-${cartKey}`,
          autoClose: 2000
        });
      }
      
      return newItems;
    });
  };

  // Xóa toàn bộ giỏ hàng
  const clearCart = () => {
    setCartItems([]);
    localStorage.setItem('dexin-cart', JSON.stringify([]));
    
    toast.info('Đã xóa toàn bộ giỏ hàng', {
      icon: () => <span style={{ fontSize: '1.2rem', marginRight: '10px' }}>🗑️</span>,
      className: 'toast-message',
      toastId: 'clear-cart',
      autoClose: 2000
    });
  };

  // Value cho context
  const value = {
    cartItems,
    cartCount,
    cartTotal,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
}; 