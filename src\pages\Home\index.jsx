import React, { useState } from 'react';
import { motion } from 'motion/react';
import Hero from '../../components/sections/Hero';
import FeatureSection from '../../components/sections/FeatureSection';
import AISection from '../../components/sections/AISection';
import AI<PERSON>hat from '../../components/sections/AIChat';
import Testimonials from '../../components/sections/Testimonials';
import SEOHead from '../../components/common/SEOHead';
import InternalLinks from '../../components/common/InternalLinks';
import SEOAudit from '../../components/common/SEOAudit';
import { pagesSEO, generateLocalBusinessStructuredData } from '../../utils/seoUtils';

const HomePage = () => {
  const [showSEOAudit, setShowSEOAudit] = useState(false);

  // Staggered animation variant
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.25, 1.0]
      }
    }
  };

  return (
    <>
      <SEOHead
        title={pagesSEO.home.title}
        description={pagesSEO.home.description}
        keywords={pagesSEO.home.keywords}
        url={pagesSEO.home.url}
      />
      <motion.div
        initial="hidden"
        animate="show"
        variants={container}
      >
        <motion.div variants={item}>
          <Hero />
        </motion.div>

        <motion.div variants={item}>
          <FeatureSection />
        </motion.div>

        <motion.div variants={item}>
          <AISection />
        </motion.div>

        <motion.div variants={item}>
          <AIChat />
        </motion.div>

        <motion.div variants={item}>
          <Testimonials />
        </motion.div>

        <motion.div variants={item}>
          <InternalLinks
            currentPage="/"
            title="Khám phá thêm DEXIN"
            maxLinks={3}
            className="max-w-4xl mx-auto mt-16"
          />
        </motion.div>
      </motion.div>

      {/* Local Business Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateLocalBusinessStructuredData({
            name: "DEXIN",
            description: "Nền tảng thiết kế nội thất thông minh với AI",
            phone: "+84-xxx-xxx-xxx",
            email: "<EMAIL>",
            address: "123 Đường ABC",
            city: "TP. Hồ Chí Minh",
            region: "TP. Hồ Chí Minh",
            postalCode: "70000",
            latitude: 10.8231,
            longitude: 106.6297,
            openingHours: ["Mo-Fr 08:00-18:00", "Sa 08:00-16:00"],
            priceRange: "$$",
            paymentMethods: ["Cash", "Credit Card", "Bank Transfer"]
          }))
        }}
      />

      {/* SEO Audit Modal */}
      {showSEOAudit && (
        <SEOAudit
          pageData={{
            title: pagesSEO.home.title,
            description: pagesSEO.home.description,
            keywords: pagesSEO.home.keywords,
            url: pagesSEO.home.url,
            contentLength: 500, // Estimated
            totalImages: 5,
            imagesWithAlt: 4,
            internalLinks: 3,
            hasStructuredData: true
          }}
          onClose={() => setShowSEOAudit(false)}
        />
      )}

      {/* Development only - SEO Audit trigger */}
      {process.env.NODE_ENV === 'development' && (
        <button
          onClick={() => setShowSEOAudit(true)}
          className="fixed bottom-4 right-4 bg-dexin-primary text-white p-3 rounded-full shadow-lg hover:bg-dexin-primary/90 transition-colors z-40"
          title="SEO Audit"
        >
          📊
        </button>
      )}
    </>
  );
};

export default HomePage;