import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'motion/react';
import { Trash2, Minus, Plus } from 'lucide-react';

// Hàm định dạng giá tiền
const formatPrice = (price) => {
  return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
};

const CartItem = ({ item, updateQuantity, removeItem }) => {
  const handleIncreaseQuantity = () => {
    updateQuantity(item.cartKey, item.quantity + 1);
  };

  const handleDecreaseQuantity = () => {
    if (item.quantity > 1) {
      updateQuantity(item.cartKey, item.quantity - 1);
    } else {
      removeItem(item.cartKey);
    }
  };

  return (
    <motion.div 
      className="flex flex-col md:flex-row md:items-center py-4 border-b border-gray-200"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* <PERSON><PERSON><PERSON> ảnh sản phẩm */}
      <div className="md:w-1/6 flex justify-center mb-4 md:mb-0">
        <div className="h-24 w-24 bg-gray-50 rounded-md flex items-center justify-center">
          <img 
            src={item.image} 
            alt={item.name} 
            className="max-h-20 max-w-20 object-contain" 
          />
        </div>
      </div>

      {/* Thông tin sản phẩm */}
      <div className="md:w-2/6 md:px-4">
        <Link to={`/product/${item.id}`} className="text-gray-800 font-medium hover:text-dexin-primary">
          {item.name}
        </Link>
        <div className="text-gray-500 text-sm mt-1">
          {item.variantName && (
            <div className="flex items-center gap-2">
              <span>Màu sắc:</span>
              <div className="flex items-center gap-1">
                <div 
                  className="w-4 h-4 rounded-full border border-gray-300"
                  style={{ backgroundColor: item.variantColor }}
                ></div>
                <span>{item.variantName}</span>
              </div>
            </div>
          )}
          {item.size && <div>Kích thước: {item.size}</div>}
        </div>
      </div>

      {/* Giá */}
      <div className="md:w-1/6 text-dexin-primary font-bold mt-2 md:mt-0 md:text-center">
        {formatPrice(item.price)}
      </div>

      {/* Số lượng */}
      <div className="md:w-1/6 flex items-center justify-between md:justify-center mt-2 md:mt-0">
        <div className="flex items-center">
          <button 
            onClick={handleDecreaseQuantity}
            className="h-8 w-8 flex items-center justify-center bg-gray-100 rounded-l-md hover:bg-gray-200"
          >
            <Minus className="w-4 h-4 text-gray-600" />
          </button>
          <div className="h-8 w-10 flex items-center justify-center bg-white border border-gray-200">
            {item.quantity}
          </div>
          <button 
            onClick={handleIncreaseQuantity}
            className="h-8 w-8 flex items-center justify-center bg-gray-100 rounded-r-md hover:bg-gray-200"
          >
            <Plus className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Tổng giá */}
      <div className="md:w-1/6 font-bold text-right mt-2 md:mt-0">
        {formatPrice(item.price * item.quantity)}
      </div>

      {/* Nút xóa */}
      <div className="flex justify-end mt-2 md:mt-0 md:ml-4">
        <button 
          onClick={() => removeItem(item.cartKey)}
          className="text-gray-400 hover:text-red-500 transition-colors duration-200"
          aria-label="Xóa sản phẩm"
        >
          <Trash2 className="h-5 w-5" />
        </button>
      </div>
    </motion.div>
  );
};

export default CartItem; 