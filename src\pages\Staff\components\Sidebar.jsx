import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'motion/react';
import { BarChart3, Users, LogOut } from 'lucide-react';
import { useAuth } from '../../../context/AuthContext';
import { Button } from '../../../components/ui/button';
import { Card } from '../../../components/ui/card';
import { Separator } from '../../../components/ui/separator';

const Sidebar = ({ activeTab, setActiveTab }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useAuth();

  const menuItems = [
    {
      id: 'designs',
      label: '<PERSON><PERSON>n thiết kế',
      icon: BarChart3,
      path: '/staff/designs'
    },
    {
      id: 'customers',
      label: '<PERSON>h<PERSON>ch hàng',
      icon: Users,
      path: '/staff/customers'
    }
  ];

  const handleMenuClick = (item) => {
    setActiveTab(item.id);
    navigate(item.path);
  };

  const handleLogout = async () => {
    try {
      console.log('🔄 Staff logout initiated');
      await logout();
      console.log('✅ Staff logout successful, redirecting to login');
      navigate('/login');
    } catch (error) {
      console.error('❌ Staff logout error:', error);
      // Vẫn redirect dù có lỗi
      navigate('/login');
    }
  };

  return (
    <motion.div
      className="w-64 bg-background border-r flex flex-col"
      initial={{ x: -100, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Logo Section */}
      <div className="p-6">
        <div className="flex items-center space-x-3">
          <img
            src="/images/logo-mini.png"
            alt="DEXIN Logo"
            className="w-10 h-10 object-contain"
          />
          <div>
            <h2 className="text-xl font-bold text-dexin-primary">DEXIN</h2>
            <p className="text-sm text-muted-foreground">Staff Dashboard</p>
          </div>
        </div>
      </div>

      <Separator />

      {/* Menu Items */}
      <div className="flex-1 py-6">
        <nav className="space-y-2 px-4">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;

            return (
              <motion.div
                key={item.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  onClick={() => handleMenuClick(item)}
                  variant={isActive ? "default" : "ghost"}
                  className={`w-full justify-start space-x-3 h-12 ${
                    isActive
                      ? 'bg-dexin-primary hover:bg-dexin-primary/90 text-white shadow-dexin'
                      : 'hover:bg-dexin-light-10 hover:text-dexin-primary'
                  }`}
                >
                  <Icon size={20} />
                  <span className="font-medium">{item.label}</span>
                </Button>
              </motion.div>
            );
          })}
        </nav>
      </div>

      {/* Logout Button */}
      <div className="p-4">
        <Separator className="mb-4" />
        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Button
            onClick={handleLogout}
            variant="ghost"
            className="w-full justify-start space-x-3 h-12 text-destructive hover:bg-destructive/10 hover:text-destructive"
          >
            <LogOut size={20} />
            <span className="font-medium">Đăng xuất</span>
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Sidebar;
