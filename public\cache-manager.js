/**
 * Lightweight Cache Manager - Chỉ giữ chức năng cần thiết
 * Giải quyết MIME type error mà không ảnh hưởng performance
 */

class LightweightCacheManager {
  constructor() {
    this.CACHE_VERSION_KEY = 'dexin_cache_version';
    this.currentVersion = this.getCurrentVersion();
    this.init();
  }

  // Lấy version hiện tại
  getCurrentVersion() {
    return window.APP_VERSION || Date.now().toString();
  }

  // Khởi tạo lightweight
  init() {
    this.checkForUpdates();
  }

  // Kiểm tra update đơn giản
  checkForUpdates() {
    const storedVersion = localStorage.getItem(this.CACHE_VERSION_KEY);
    
    // Nếu version khác nhau, clear cache
    if (!storedVersion || storedVersion !== this.currentVersion) {
      this.clearEssentialCaches().then(() => {
        this.updateStoredVersion();
      });
    }
  }

  // Clear chỉ cache cần thiết
  async clearEssentialCaches() {
    try {
      // Chỉ clear browser caches (core issue)
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(name => caches.delete(name))
        );
      }

      // Clear một số localStorage keys không quan trọng
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('dexin_temp_') || key.includes('cache_')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));

      // Unregister old service workers
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        await Promise.all(
          registrations.map(registration => registration.unregister())
        );
      }

    } catch (error) {
      // Silent fail
    }
  }

  // Update stored version
  updateStoredVersion() {
    localStorage.setItem(this.CACHE_VERSION_KEY, this.currentVersion);
  }
}

// Minimal CSS cho performance
const minimalStyles = `
<style>
.cache-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  max-width: 300px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 10000;
  transform: translateX(320px);
  transition: transform 0.2s ease;
  border-left: 3px solid #3b82f6;
  font-family: system-ui, -apple-system, sans-serif;
}
.cache-notification__content {
  padding: 12px 16px;
}
.cache-notification__title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  color: #1f2937;
}
.cache-notification__message {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.3;
}
.cache-notification.show {
  transform: translateX(0);
}
</style>
`;

// Inject minimal styles
document.head.insertAdjacentHTML('beforeend', minimalStyles);

// Initialize lightweight cache manager
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.cacheManager = new LightweightCacheManager();
  });
} else {
  window.cacheManager = new LightweightCacheManager();
}

// Export minimal functions
window.clearApplicationCache = () => {
  if (window.cacheManager) {
    window.cacheManager.clearEssentialCaches().then(() => {
      window.location.reload();
    });
  }
}; 