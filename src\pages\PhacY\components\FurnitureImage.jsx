import React, { useEffect, useRef, useState } from 'react';
import { Image, Transformer } from 'react-konva';
import useImage from 'use-image';

const FurnitureImage = ({
  src,
  x,
  y,
  width,
  height,
  rotation,
  isSelected,
  onSelect,
  onChange,
  draggable,
  viewMode,
  item // Add item prop to access additional properties like isHidden, isRoomBackground
}) => {
  const imageRef = useRef();
  const trRef = useRef();
  // Track dimensions locally to ensure UI updates
  const [dimensions, setDimensions] = useState({ width, height });
  // Load image with error handling
  const [image] = useImage(src, 'anonymous');

  // Kiểm tra xem item có phải là background phòng không
  const isRoomBackground = item?.isRoomBackground;

  // Update local dimensions when props change
  useEffect(() => {
    setDimensions({ width, height });
  }, [width, height]);

  // Connect transformer to image when selected
  useEffect(() => {
    if (isSelected && trRef.current && imageRef.current) {
      trRef.current.nodes([imageRef.current]);
      trRef.current.getLayer().batchDraw();

      // Add a small animation to highlight selection
      const node = imageRef.current;
      const scaleX = node.scaleX();
      const scaleY = node.scaleY();

      // Small pulse animation
      node.to({
        duration: 0.1,
        scaleX: scaleX * 1.02,
        scaleY: scaleY * 1.02,
        onFinish: () => {
          node.to({
            duration: 0.1,
            scaleX: scaleX,
            scaleY: scaleY
          });
        }
      });
    }
  }, [isSelected]);

  // Handle transform events (both during and after transform)
  const handleTransform = (e) => {
    if (!imageRef.current) return;

    // Prevent transformations when in hand mode
    if (viewMode === 'hand') {
      if (e) {
        e.cancelBubble = true;
      }
      return;
    }

    const node = imageRef.current;

    // Get current scale values
    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    // Nếu là background phòng và keepRatio=true, đảm bảo tỷ lệ được giữ nguyên
    let newWidth, newHeight;

    if (isRoomBackground) {
      // Tính toán kích thước mới dựa trên tỷ lệ ban đầu
      const aspectRatio = width / height;

      // Lấy kích thước của stage (canvas)
      const stage = node.getStage();
      const stageWidth = stage.width();
      const stageHeight = stage.height();

      // Giới hạn kích thước tối đa là 80% của canvas
      const maxWidth = stageWidth * 0.8;
      const maxHeight = stageHeight * 0.8;

      // Tính toán kích thước mới dựa trên scale hiện tại
      let scaledWidth = Math.max(10, Math.round(node.width() * scaleX));
      let scaledHeight = Math.max(10, Math.round(node.height() * scaleY));

      // Kiểm tra và giới hạn kích thước nếu vượt quá giới hạn
      if (scaledWidth > maxWidth) {
        scaledWidth = maxWidth;
        scaledHeight = scaledWidth / aspectRatio;
      }

      if (scaledHeight > maxHeight) {
        scaledHeight = maxHeight;
        scaledWidth = scaledHeight * aspectRatio;
      }

      newWidth = scaledWidth;
      newHeight = scaledHeight;
    } else {
      // Đối với các item thông thường, giữ nguyên logic cũ
      newWidth = Math.max(10, Math.round(node.width() * scaleX));
      newHeight = Math.max(10, Math.round(node.height() * scaleY));
    }

    // Reset scale to avoid compounding scale values
    node.scaleX(1);
    node.scaleY(1);

    // Set dimensions directly on the node
    node.width(newWidth);
    node.height(newHeight);

    // Update local state for immediate visual feedback
    setDimensions({ width: newWidth, height: newHeight });

    // Update parent component with new values
    onChange({
      id: node.attrs.id || imageRef.current.id(),
      x: node.x(),
      y: node.y(),
      width: newWidth,
      height: newHeight,
      rotation: node.rotation(),
      src, // Ensure we keep the source image
      isRoomBackground: isRoomBackground // Giữ thuộc tính isRoomBackground
    });
  };

  // Nếu item bị ẩn, không render gì cả
  const isHidden = item?.isHidden;

  if (isHidden) {
    return null;
  }

  return (
    <>
      <Image
        ref={imageRef}
        image={image}
        x={x}
        y={y}
        width={dimensions.width} // Use local state for dimensions
        height={dimensions.height}
        rotation={rotation || 0}
        offsetX={0}
        offsetY={0}
        onClick={(e) => {
          // Prevent selection when in hand mode
          if (viewMode === 'hand') {
            e.cancelBubble = true;
            return;
          }
          onSelect();
        }}
        onTap={(e) => {
          // Prevent selection when in hand mode
          if (viewMode === 'hand') {
            e.cancelBubble = true;
            return;
          }
          onSelect();
        }}
        draggable={draggable && !isRoomBackground} // Không cho phép kéo nếu là background phòng
        opacity={isSelected ? 1 : 0.95} // Slight opacity difference for non-selected items
        shadowColor={isSelected ? "#00A0FF" : "transparent"}
        shadowBlur={isSelected ? 5 : 0}
        shadowOpacity={isSelected ? 0.3 : 0}
        shadowOffset={isSelected ? { x: 0, y: 0 } : { x: 0, y: 0 }}
        // Note: zIndex được xử lý bằng sorting trong Canvas component
        onMouseEnter={(e) => {
          // Change cursor to pointer when hovering, but only if not in hand mode
          if (viewMode !== 'hand') {
            const container = e.target.getStage().container();
            container.style.cursor = 'pointer';
          }
        }}
        onMouseLeave={(e) => {
          // Reset cursor based on current viewMode
          const container = e.target.getStage().container();
          if (viewMode === 'hand') {
            container.style.cursor = 'grab';
          } else {
            container.style.cursor = 'default';
          }
        }}
        onDragEnd={(e) => {
          // Prevent updating position when in hand mode
          if (viewMode === 'hand') {
            e.cancelBubble = true;
            return;
          }

          onChange({
            id: e.target.attrs.id || imageRef.current.id(),
            x: e.target.x(),
            y: e.target.y(),
            width: dimensions.width,
            height: dimensions.height,
            rotation: rotation || 0,
            src, // Ensure we keep the source image
            isRoomBackground: isRoomBackground // Giữ thuộc tính isRoomBackground
          });
        }}
        onTransformEnd={handleTransform}
        onTransform={handleTransform}
      />
      {isSelected && (
        <>
          <Transformer
            ref={trRef}
            boundBoxFunc={(oldBox, newBox) => {
              // Limit minimum size
              if (newBox.width < 10 || newBox.height < 10) {
                return oldBox;
              }
              return newBox;
            }}
            rotateEnabled={!isRoomBackground} // Không cho phép xoay nếu là background phòng
            resizeEnabled={true} // Vẫn cho phép thay đổi kích thước
            keepRatio={isRoomBackground} // Giữ tỷ lệ nếu là background phòng
            enabledAnchors={isRoomBackground ?
              // Nếu là background phòng, chỉ cho phép thay đổi kích thước từ các góc
              ['top-left', 'top-right', 'bottom-left', 'bottom-right'] :
              // Nếu không, cho phép tất cả các điểm neo
              [
                'top-left', 'top-center', 'top-right',
                'middle-left', 'middle-right',
                'bottom-left', 'bottom-center', 'bottom-right'
              ]
            }
            borderStroke="#00A0FF"
            borderStrokeWidth={2}
            borderDash={[0, 0]} // Solid line
            anchorStroke="#00A0FF"
            anchorFill="#FFFFFF"
            anchorSize={8}
            anchorCornerRadius={4}
            padding={1}
            ignoreStroke={false}
          />
        </>
      )}
    </>
  );
};

export default FurnitureImage;
