import React, { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import {
  Palette,
  Download,
  Share2,
  Trash2,
  Plus,
  Calendar,
  Eye,

  Image as ImageIcon,
  Lock,
  CreditCard,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../../context/AuthContext';
import designService from '../../../services/designService';
import paymentService from '../../../services/paymentService';
import { toast } from 'react-hot-toast';

const ProductDesign = () => {
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [designs, setDesigns] = useState([]);
  const [loading, setLoading] = useState(true);
  const [paymentLoading, setPaymentLoading] = useState(null);

  // Fetch designs khi component mount
  useEffect(() => {
    const fetchDesigns = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const result = await designService.getDesignsByUser(user.id);

        if (result.success) {
          setDesigns(result.data);
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        console.error('Error fetching designs:', error);
        toast.error('Có lỗi xảy ra khi tải danh sách thiết kế');
      } finally {
        setLoading(false);
      }
    };

    fetchDesigns();
  }, [user]);

  // Tạo categories dựa trên status của designs
  const categories = [
    { id: 'all', label: 'Tất cả', count: designs.length },
    { id: 'paid', label: 'Đã thanh toán', count: designs.filter(d => d.status === 'Hoàn tất thanh toán').length },
    { id: 'unpaid', label: 'Chưa thanh toán', count: designs.filter(d => d.status === 'Chưa trả').length },
    { id: 'cancelled', label: 'Đã hủy', count: designs.filter(d => d.status === 'Hủy').length }
  ];

  const filteredDesigns = selectedCategory === 'all'
    ? designs
    : designs.filter(design => {
        switch (selectedCategory) {
          case 'paid':
            return design.status === 'Hoàn tất thanh toán';
          case 'unpaid':
            return design.status === 'Chưa trả';
          case 'cancelled':
            return design.status === 'Hủy';
          default:
            return true;
        }
      });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Kiểm tra xem design có thể xem được không
  const canViewDesign = (design) => {
    return design.status === 'Hoàn tất thanh toán';
  };

  // Xử lý thanh toán cho design
  const handlePayment = async (design) => {
    if (design.status !== 'Chưa trả') {
      toast.error('Thiết kế này không cần thanh toán');
      return;
    }

    try {
      setPaymentLoading(design.designId);

      // Tạo URLs cho success và cancel (không cần truyền designId vì PayOS sẽ trả về thông tin khác)
      const baseUrl = window.location.origin;
      const returnUrl = `${baseUrl}/payment/success`;
      const cancelUrl = `${baseUrl}/payment/cancel`;

      // Tạo expiredAt (1 giờ từ bây giờ)
      const expiredAt = Math.floor(Date.now() / 1000) + (60 * 60);

      const paymentData = {
        productName: design.title,
        description: `Thanh toán thiết kế: ${design.title}`,
        price: design.price,
        returnUrl,
        cancelUrl,
        expiredAt
      };

      const result = await paymentService.createPayment(design.designId, paymentData);

      if (result.success && result.data?.checkoutUrl) {
        // Redirect đến trang thanh toán PayOS
        window.location.href = result.data.checkoutUrl;
      } else {
        toast.error(result.message || 'Không thể tạo đơn thanh toán');
      }
    } catch (error) {
      console.error('Payment error:', error);
      toast.error('Có lỗi xảy ra khi tạo đơn thanh toán');
    } finally {
      setPaymentLoading(null);
    }
  };

  const handleDownload = (design) => {
    if (!canViewDesign(design)) {
      toast.error('Vui lòng thanh toán để tải xuống thiết kế');
      return;
    }

    if (design.pathUrl) {
      window.open(design.pathUrl, '_blank');
      toast.success('Đang tải xuống thiết kế...');
    } else {
      toast.error('Không tìm thấy file thiết kế');
    }
  };

  const handleView = (design) => {
    if (!canViewDesign(design)) {
      toast.error('Vui lòng thanh toán để xem thiết kế');
      return;
    }

    if (design.pathUrl) {
      window.open(design.pathUrl, '_blank');
    } else {
      toast.error('Không tìm thấy file thiết kế');
    }
  };

  const handleShare = (design) => {
    if (!canViewDesign(design)) {
      toast.error('Vui lòng thanh toán để chia sẻ thiết kế');
      return;
    }

    // Logic chia sẻ thiết kế
    if (navigator.share) {
      navigator.share({
        title: design.title,
        text: `Xem thiết kế: ${design.title}`,
        url: design.pathUrl
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(design.pathUrl);
      toast.success('Đã copy link thiết kế vào clipboard');
    }
  };

  const handleDelete = (design) => {
    // Logic xóa thiết kế - cần confirm trước
    if (window.confirm(`Bạn có chắc muốn xóa thiết kế "${design.title}"?`)) {
      console.log('Deleting design:', design.designId);
      toast.info('Tính năng xóa thiết kế đang được phát triển');
    }
  };

  // Nếu chưa đăng nhập
  if (!user) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-lg shadow-lg overflow-hidden"
      >
        <div className="bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4">
          <h2 className="text-xl font-bold text-white">Bản thiết kế của tôi</h2>
        </div>
        <div className="p-6 text-center">
          <AlertCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Vui lòng đăng nhập</h3>
          <p className="text-gray-500 mb-4">Bạn cần đăng nhập để xem danh sách thiết kế của mình.</p>
          <Link
            to="/login"
            className="inline-flex items-center space-x-2 px-4 py-2 bg-dexin-primary text-white rounded-lg hover:bg-dexin-light transition-colors"
          >
            <span>Đăng nhập ngay</span>
          </Link>
        </div>
      </motion.div>
    );
  }

  // Loading state
  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-lg shadow-lg overflow-hidden"
      >
        <div className="bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4">
          <h2 className="text-xl font-bold text-white">Bản thiết kế của tôi</h2>
        </div>
        <div className="p-6 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-dexin-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải danh sách thiết kế...</p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-lg shadow-lg overflow-hidden"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-white">Bản thiết kế của tôi</h2>
            <p className="text-dexin-bg text-sm mt-1">Quản lý các thiết kế đã tạo</p>
          </div>
         
        </div>
      </div>

      {/* Category filter */}
      <div className="border-b border-gray-200">
        <div className="flex overflow-x-auto">
          {categories.map((category) => {
            const isActive = selectedCategory === category.id;
            
            return (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`
                  flex items-center space-x-2 px-4 py-3 border-b-2 transition-colors whitespace-nowrap
                  ${isActive 
                    ? 'border-dexin-primary text-dexin-primary bg-dexin-light-10' 
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <span className="font-medium">{category.label}</span>
                <span className={`
                  px-2 py-1 rounded-full text-xs
                  ${isActive ? 'bg-dexin-primary text-white' : 'bg-gray-200 text-gray-600'}
                `}>
                  {category.count}
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Designs grid */}
      <div className="p-6">
        {filteredDesigns.length === 0 ? (
          <div className="text-center py-12">
            <Palette className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có thiết kế</h3>
            <p className="text-gray-500 mb-4">
              {selectedCategory === 'all' 
                ? 'Bạn chưa tạo thiết kế nào.' 
                : `Chưa có thiết kế ${categories.find(c => c.id === selectedCategory)?.label.toLowerCase()}.`
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredDesigns.map((design) => {
              const isLocked = !canViewDesign(design);
              const isPaymentLoading = paymentLoading === design.designId;

              return (
                <motion.div
                  key={design.designId}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  whileHover={{ y: -5 }}
                  className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300"
                >
                  {/* Thumbnail */}
                  <div className="relative h-48 bg-gray-100">
                    {/* Thumbnail image */}
                    {design.thumbnailUrl ? (
                      <img
                        src={design.thumbnailUrl}
                        alt={design.title}
                        className={`w-full h-full object-cover ${isLocked ? 'filter blur-sm' : ''}`}
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <ImageIcon className="w-12 h-12 text-gray-400" />
                      </div>
                    )}

                    {/* Lock overlay for unpaid designs */}
                    {isLocked && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="text-center text-white">
                          <Lock className="w-8 h-8 mx-auto mb-2" />
                          <p className="text-sm font-medium">Cần thanh toán</p>
                        </div>
                      </div>
                    )}

                    {/* Action buttons overlay */}
                    <div className="absolute top-2 right-2 flex space-x-1">
                      <button
                        onClick={() => handleShare(design)}
                        disabled={isLocked}
                        className={`p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all ${
                          isLocked ? 'opacity-50 cursor-not-allowed' : ''
                        }`}
                        title={isLocked ? 'Cần thanh toán để chia sẻ' : 'Chia sẻ'}
                      >
                        <Share2 className="w-4 h-4 text-gray-600" />
                      </button>
                      <button
                        onClick={() => handleDownload(design)}
                        disabled={isLocked}
                        className={`p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all ${
                          isLocked ? 'opacity-50 cursor-not-allowed' : ''
                        }`}
                        title={isLocked ? 'Cần thanh toán để tải xuống' : 'Tải xuống'}
                      >
                        <Download className="w-4 h-4 text-gray-600" />
                      </button>
                    </div>

                    {/* Status badge */}
                    <div className="absolute top-2 left-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        design.status === 'Hoàn tất thanh toán'
                          ? 'bg-green-100 text-green-800'
                          : design.status === 'Chưa trả'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {design.status}
                      </span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-1">
                      {design.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {design.note || 'Không có mô tả'}
                    </p>

                    {/* Price */}
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-lg font-bold text-dexin-primary">
                        {design.price.toLocaleString('vi-VN')} VNĐ
                      </span>
                      <div className="text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDate(design.createdAt)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Staff info */}
                    {design.staff && (
                      <div className="text-xs text-gray-500 mb-3">
                        Thiết kế bởi: {design.staff.firstName} {design.staff.lastName}
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex space-x-2">
                      {isLocked ? (
                        // Payment button for unpaid designs
                        <button
                          onClick={() => handlePayment(design)}
                          disabled={isPaymentLoading}
                          className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-dexin-primary text-white rounded-lg hover:bg-dexin-light transition-colors text-sm disabled:opacity-50"
                        >
                          {isPaymentLoading ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                              <span>Đang xử lý...</span>
                            </>
                          ) : (
                            <>
                              <CreditCard className="w-3 h-3" />
                              <span>Thanh toán</span>
                            </>
                          )}
                        </button>
                      ) : (
                        // View button for paid designs
                        <button
                          onClick={() => handleView(design)}
                          className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                        >
                          <Eye className="w-3 h-3" />
                          <span>Xem thiết kế</span>
                        </button>
                      )}

                      <button
                        onClick={() => handleDelete(design)}
                        className="px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Xóa thiết kế"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ProductDesign;
