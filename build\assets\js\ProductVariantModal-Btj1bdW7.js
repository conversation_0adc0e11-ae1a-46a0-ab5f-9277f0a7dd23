import{c as e,r as t,R as n,h as r,w as o,x as l,j as i,X as a,m as u}from"./index-DdBL2cja.js";import{M as s}from"./minus-Fx_j7jOv.js";import{P as c}from"./plus-66Jg-RVc.js";
/**
 * @license lucide-react v0.484.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d=e("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var f=Object.defineProperty,m=(e,t,n)=>(((e,t,n)=>{t in e?f(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let p=new class{constructor(){m(this,"current",this.detect()),m(this,"handoffState","pending"),m(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}};function h(e){var t,n;return p.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}function v(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}function g(){let e=[],t={addEventListener:(e,n,r,o)=>(e.addEventListener(n,r,o),t.add((()=>e.removeEventListener(n,r,o)))),requestAnimationFrame(...e){let n=requestAnimationFrame(...e);return t.add((()=>cancelAnimationFrame(n)))},nextFrame:(...e)=>t.requestAnimationFrame((()=>t.requestAnimationFrame(...e))),setTimeout(...e){let n=setTimeout(...e);return t.add((()=>clearTimeout(n)))},microTask(...e){let n={current:!0};return v((()=>{n.current&&e[0]()})),t.add((()=>{n.current=!1}))},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add((()=>{Object.assign(e.style,{[t]:r})}))},group(e){let t=g();return e(t),this.add((()=>t.dispose()))},add:t=>(e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function b(){let[e]=t.useState(g);return t.useEffect((()=>()=>e.dispose()),[e]),e}let y=(e,n)=>{p.isServer?t.useEffect(e,n):t.useLayoutEffect(e,n)};function w(e){let n=t.useRef(e);return y((()=>{n.current=e}),[e]),n}let E=function(e){let t=w(e);return n.useCallback(((...e)=>t.current(...e)),[t])},x=t.createContext(void 0);function S(...e){return Array.from(new Set(e.flatMap((e=>"string"==typeof e?e.split(" "):[])))).filter(Boolean).join(" ")}function C(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,C),r}var F,N,P=((N=P||{})[N.None=0]="None",N[N.RenderStrategy=1]="RenderStrategy",N[N.Static=2]="Static",N),k=((F=k||{})[F.Unmount=0]="Unmount",F[F.Hidden=1]="Hidden",F);function T(){let e=function(){let e=t.useRef([]),n=t.useCallback((t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)}),[]);return(...t)=>{if(!t.every((e=>null==e)))return e.current=t,n}}();return t.useCallback((t=>function({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:l=!0,name:i,mergeRefs:a}){a=null!=a?a:O;let u=A(t,e);if(l)return j(u,n,r,i,a);let s=null!=o?o:0;if(2&s){let{static:e=!1,...t}=u;if(e)return j(t,n,r,i,a)}if(1&s){let{unmount:e=!0,...t}=u;return C(e?0:1,{0:()=>null,1:()=>j({...t,hidden:!0,style:{display:"none"}},n,r,i,a)})}return j(u,n,r,i,a)}({mergeRefs:e,...t})),[e])}function j(e,n={},r,o,l){let{as:i=r,children:a,refName:u="ref",...s}=D(e,["unmount","static"]),c=void 0!==e.ref?{[u]:e.ref}:{},d="function"==typeof a?a(n):a;"className"in s&&s.className&&"function"==typeof s.className&&(s.className=s.className(n)),s["aria-labelledby"]&&s["aria-labelledby"]===s.id&&(s["aria-labelledby"]=void 0);let f={};if(n){let e=!1,t=[];for(let[r,o]of Object.entries(n))"boolean"==typeof o&&(e=!0),!0===o&&t.push(r.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)));if(e){f["data-headlessui-state"]=t.join(" ");for(let e of t)f[`data-${e}`]=""}}if(i===t.Fragment&&(Object.keys(L(s)).length>0||Object.keys(L(f)).length>0)){if(t.isValidElement(d)&&!(Array.isArray(d)&&d.length>1)){let e=d.props,n=null==e?void 0:e.className,r="function"==typeof n?(...e)=>S(n(...e),s.className):S(n,s.className),o=r?{className:r}:{},i=A(d.props,L(D(s,["ref"])));for(let t in f)t in i&&delete f[t];return t.cloneElement(d,Object.assign({},i,f,c,{ref:l(M(d),c.ref)},o))}if(Object.keys(L(s)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(L(s)).concat(Object.keys(L(f))).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"))}return t.createElement(i,Object.assign({},D(s,["ref"]),i!==t.Fragment&&c,i!==t.Fragment&&f),d)}function O(...e){return e.every((e=>null==e))?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function A(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])for(let r in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(r)&&(n[r]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let r in n)Object.assign(t,{[r](e,...t){let o=n[r];for(let n of o){if((e instanceof Event||(null==e?void 0:e.nativeEvent)instanceof Event)&&e.defaultPrevented)return;n(e,...t)}}});return t}function R(e){var n;return Object.assign(t.forwardRef(e),{displayName:null!=(n=e.displayName)?n:e.name})}function L(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function D(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}function M(e){return n.version.split(".")[0]>="19"?e.props.ref:e.ref}var I=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(I||{});let H=R((function(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":!(2&~r)||(null!=(n=o["aria-hidden"])?n:void 0),hidden:!(4&~r)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...!(4&~r)&&!!(2&~r)&&{display:"none"}}};return T()({ourProps:l,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})}));function W(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function V(e){return W(e)&&"tagName"in e}function $(e){return V(e)&&"accessKey"in e}function _(e){return V(e)&&"tabIndex"in e}let U=Symbol();function B(...e){let n=t.useRef(e);t.useEffect((()=>{n.current=e}),[e]);let r=E((e=>{for(let t of n.current)null!=t&&("function"==typeof t?t(e):t.current=e)}));return e.every((e=>null==e||(null==e?void 0:e[U])))?void 0:r}let q=t.createContext(null);function Y(){let e=t.useContext(q);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Y),e}return e}q.displayName="DescriptionContext";let G=R((function(e,n){let r=t.useId(),o=t.useContext(x),{id:l=`headlessui-description-${r}`,...i}=e,a=Y(),u=B(n);y((()=>a.register(l)),[l,a.register]);let s=o||!1,c=t.useMemo((()=>({...a.slot,disabled:s})),[a.slot,s]),d={ref:u,...a.props,id:l};return T()({ourProps:d,theirProps:i,slot:c,defaultTag:"p",name:a.name||"Description"})})),K=Object.assign(G,{});var z,X=((z=X||{}).Space=" ",z.Enter="Enter",z.Escape="Escape",z.Backspace="Backspace",z.Delete="Delete",z.ArrowLeft="ArrowLeft",z.ArrowUp="ArrowUp",z.ArrowRight="ArrowRight",z.ArrowDown="ArrowDown",z.Home="Home",z.End="End",z.PageUp="PageUp",z.PageDown="PageDown",z.Tab="Tab",z);let Z=t.createContext((()=>{}));function J({value:e,children:t}){return n.createElement(Z.Provider,{value:e},t)}let Q=class extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}};var ee,te,ne,re=Object.defineProperty,oe=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},le=(e,t,n)=>(oe(e,t,"read from private field"),n?n.call(e):t.get(e)),ie=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},ae=(e,t,n,r)=>(oe(e,t,"write to private field"),t.set(e,n),n);let ue=class{constructor(e){ie(this,ee,{}),ie(this,te,new Q((()=>new Set))),ie(this,ne,new Set),((e,t,n)=>{((e,t,n)=>{t in e?re(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,t+"",n)})(this,"disposables",g()),ae(this,ee,e)}dispose(){this.disposables.dispose()}get state(){return le(this,ee)}subscribe(e,t){let n={selector:e,callback:t,current:e(le(this,ee))};return le(this,ne).add(n),this.disposables.add((()=>{le(this,ne).delete(n)}))}on(e,t){return le(this,te).get(e).add(t),this.disposables.add((()=>{le(this,te).get(e).delete(t)}))}send(e){let t=this.reduce(le(this,ee),e);if(t!==le(this,ee)){ae(this,ee,t);for(let e of le(this,ne)){let t=e.selector(le(this,ee));se(e.current,t)||(e.current=t,e.callback(t))}for(let t of le(this,te).get(e.type))t(le(this,ee),e)}}};function se(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&ce(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&ce(e.entries(),t.entries()):!(!de(e)||!de(t))&&ce(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function ce(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function de(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}ee=new WeakMap,te=new WeakMap,ne=new WeakMap;var fe,me=Object.defineProperty,pe=(e,t,n)=>(((e,t,n)=>{t in e?me(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n),he=((fe=he||{})[fe.Push=0]="Push",fe[fe.Pop=1]="Pop",fe);let ve={0(e,t){let n=t.id,r=e.stack,o=e.stack.indexOf(n);if(-1!==o){let t=e.stack.slice();return t.splice(o,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let o=e.stack.slice();return o.splice(r,1),{...e,stack:o}}},ge=class e extends ue{constructor(){super(...arguments),pe(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),pe(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new e({stack:[]})}reduce(e,t){return C(t.type,ve,e,t)}};const be=new Q((()=>ge.new()));var ye,we,Ee={exports:{}},xe={};function Se(){if(ye)return xe;ye=1;var e=r(),t="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=e.useSyncExternalStore,o=e.useRef,l=e.useEffect,i=e.useMemo,a=e.useDebugValue;return xe.useSyncExternalStoreWithSelector=function(e,r,u,s,c){var d=o(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=i((function(){function e(e){if(!l){if(l=!0,n=e,e=s(e),void 0!==c&&f.hasValue){var r=f.value;if(c(r,e))return o=r}return o=e}if(r=o,t(n,e))return r;var i=s(e);return void 0!==c&&c(r,i)?(n=e,r):(n=e,o=i)}var n,o,l=!1,i=void 0===u?null:u;return[function(){return e(r())},null===i?void 0:function(){return e(i())}]}),[r,u,s,c]);var m=n(e,d[0],d[1]);return l((function(){f.hasValue=!0,f.value=m}),[m]),a(m),m},xe}function Ce(){return we||(we=1,Ee.exports=Se()),Ee.exports}var Fe=Ce();function Ne(e,t,n=se){return Fe.useSyncExternalStoreWithSelector(E((t=>e.subscribe(Pe,t))),E((()=>e.state)),E((()=>e.state)),E(t),n)}function Pe(e){return e}function ke(e,n){let r=t.useId(),o=be.get(n),[l,i]=Ne(o,t.useCallback((e=>[o.selectors.isTop(e,r),o.selectors.inStack(e,r)]),[o,r]));return y((()=>{if(e)return o.actions.push(r),()=>o.actions.pop(r)}),[o,e,r]),!!e&&(!i||l)}let Te=new Map,je=new Map;function Oe(e){var t;let n=null!=(t=je.get(e))?t:0;return je.set(e,n+1),0!==n||(Te.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>function(e){var t;let n=null!=(t=je.get(e))?t:1;if(1===n?je.delete(e):je.set(e,n-1),1!==n)return;let r=Te.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,Te.delete(e))}(e)}let Ae=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(","),Re=["[data-autofocus]"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var Le,De,Me=((De=Me||{})[De.First=1]="First",De[De.Previous=2]="Previous",De[De.Next=4]="Next",De[De.Last=8]="Last",De[De.WrapAround=16]="WrapAround",De[De.NoScroll=32]="NoScroll",De[De.AutoFocus=64]="AutoFocus",De),Ie=((Le=Ie||{})[Le.Error=0]="Error",Le[Le.Overflow=1]="Overflow",Le[Le.Success=2]="Success",Le[Le.Underflow=3]="Underflow",Le),He=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(He||{}),We=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(We||{}),Ve=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(Ve||{});function $e(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",(e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")}),!0),document.addEventListener("click",(e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")}),!0));let _e=["textarea","input"].join(",");function Ue(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){let l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,i=Array.isArray(e)?n?function(e,t=e=>e){return e.slice().sort(((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(Re)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}(e):function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(Ae)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}(e);o.length>0&&i.length>1&&(i=i.filter((e=>!o.some((t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))))),r=null!=r?r:l.activeElement;let a,u=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),s=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,i.indexOf(r))-1;if(4&t)return Math.max(0,i.indexOf(r))+1;if(8&t)return i.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},d=0,f=i.length;do{if(d>=f||d+f<=0)return 0;let e=s+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}a=i[e],null==a||a.focus(c),d+=u}while(a!==l.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,_e))&&n}(a)&&a.select(),2}function Be(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function qe(){return Be()||/Android/gi.test(window.navigator.userAgent)}function Ye(e,n,r,o){let l=w(r);t.useEffect((()=>{if(e)return document.addEventListener(n,t,o),()=>document.removeEventListener(n,t,o);function t(e){l.current(e)}}),[e,n,o])}function Ge(e,n,r,o){let l=w(r);t.useEffect((()=>{if(e)return window.addEventListener(n,t,o),()=>window.removeEventListener(n,t,o);function t(e){l.current(e)}}),[e,n,o])}function Ke(...e){return t.useMemo((()=>h(...e)),[...e])}function ze(e,n,r,o){let l=w(r);t.useEffect((()=>{function t(e){l.current(e)}return(e=null!=e?e:window).addEventListener(n,t,o),()=>e.removeEventListener(n,t,o)}),[e,n,o])}function Xe(){let e;return{before({doc:t}){var n;let r=t.documentElement,o=null!=(n=t.defaultView)?n:window;e=Math.max(0,o.innerWidth-r.clientWidth)},after({doc:t,d:n}){let r=t.documentElement,o=Math.max(0,r.clientWidth-r.offsetWidth),l=Math.max(0,e-o);n.style(r,"paddingRight",`${l}px`)}}}function Ze(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Je=function(e,t){let n=new Map,r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e,...o){let l=t[e].call(n,...o);l&&(n=l,r.forEach((e=>e())))}}}(0,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:g(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:Ze(n)},o=[Be()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap((e=>e())).some((t=>t.contains(e)))}t.microTask((()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=g();n.style(e.documentElement,"scrollBehavior","auto"),t.add((()=>t.microTask((()=>n.dispose()))))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,l=null;t.addEventListener(e,"click",(t=>{if(_(t.target))try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),i=e.querySelector(o);_(i)&&!r(i)&&(l=i)}catch{}}),!0),t.addEventListener(e,"touchstart",(e=>{if(_(e.target)&&function(e){return V(e)&&"style"in e}(e.target))if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")})),t.addEventListener(e,"touchmove",(e=>{if(_(e.target)){if(function(e){return $(e)&&"INPUT"===e.nodeName}(e.target))return;if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}}),{passive:!1}),t.add((()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)}))}))}}:{},Xe(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];o.forEach((({before:e})=>null==e?void 0:e(r))),o.forEach((({after:e})=>null==e?void 0:e(r)))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});var Qe,et;Je.subscribe((()=>{let e=Je.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&Je.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&Je.dispatch("TEARDOWN",n)}})),"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&"test"===(null==(Qe=null==process?void 0:{})?void 0:Qe.NODE_ENV)&&void 0===(null==(et=null==Element?void 0:Element.prototype)?void 0:et.getAnimations)&&(Element.prototype.getAnimations=function(){return[]});var tt=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(tt||{});function nt(e){let t={};for(let n in e)!0===e[n]&&(t[`data-${n}`]="");return t}function rt(e,n){let r=t.useRef([]),o=E(e);t.useEffect((()=>{let e=[...r.current];for(let[t,l]of n.entries())if(r.current[t]!==l){let t=o(n,e);return r.current=n,t}}),[o,...n])}let ot=t.createContext(null);ot.displayName="OpenClosedContext";var lt=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(lt||{});function it(){return t.useContext(ot)}function at({value:e,children:t}){return n.createElement(ot.Provider,{value:e},t)}function ut({children:e}){return n.createElement(ot.Provider,{value:null},e)}let st=[];function ct(e){let n=E(e),r=t.useRef(!1);t.useEffect((()=>(r.current=!1,()=>{r.current=!0,v((()=>{r.current&&n()}))})),[n])}function dt(){let e=function(){let e="undefined"==typeof document;return"useSyncExternalStore"in o&&o.useSyncExternalStore((()=>()=>{}),(()=>!1),(()=>!e))}(),[n,r]=t.useState(p.isHandoffComplete);return n&&!1===p.isHandoffComplete&&r(!1),t.useEffect((()=>{!0!==n&&r(!0)}),[n]),t.useEffect((()=>p.handoff()),[]),!e&&n}!function(){function e(){"loading"!==document.readyState&&((()=>{function e(e){if(!_(e.target)||e.target===document.body||st[0]===e.target)return;let t=e.target;t=t.closest(Ae),st.unshift(null!=t?t:e.target),st=st.filter((e=>null!=e&&e.isConnected)),st.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})})(),document.removeEventListener("DOMContentLoaded",e))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",e),e())}();let ft=t.createContext(!1);function mt(e){return n.createElement(ft.Provider,{value:e.force},e.children)}let pt=t.Fragment,ht=R((function(e,n){let{ownerDocument:r=null,...o}=e,i=t.useRef(null),a=B(function(e,t=!0){return Object.assign(e,{[U]:t})}((e=>{i.current=e})),n),u=Ke(i),s=null!=r?r:u,c=function(e){let n=t.useContext(ft),r=t.useContext(gt),[o,l]=t.useState((()=>{var t;if(!n&&null!==r)return null!=(t=r.current)?t:null;if(p.isServer)return null;let o=null==e?void 0:e.getElementById("headlessui-portal-root");if(o)return o;if(null===e)return null;let l=e.createElement("div");return l.setAttribute("id","headlessui-portal-root"),e.body.appendChild(l)}));return t.useEffect((()=>{null!==o&&(null!=e&&e.body.contains(o)||null==e||e.body.appendChild(o))}),[o,e]),t.useEffect((()=>{n||null!==r&&l(r.current)}),[r,l,n]),o}(s),[d]=t.useState((()=>{var e;return p.isServer?null:null!=(e=null==s?void 0:s.createElement("div"))?e:null})),f=t.useContext(bt),m=dt();y((()=>{!c||!d||c.contains(d)||(d.setAttribute("data-headlessui-portal",""),c.appendChild(d))}),[c,d]),y((()=>{if(d&&f)return f.register(d)}),[f,d]),ct((()=>{var e;!c||!d||(W(d)&&c.contains(d)&&c.removeChild(d),c.childNodes.length<=0&&(null==(e=c.parentElement)||e.removeChild(c)))}));let h=T();return m&&c&&d?l.createPortal(h({ourProps:{ref:a},theirProps:o,slot:{},defaultTag:pt,name:"Portal"}),d):null})),vt=t.Fragment,gt=t.createContext(null),bt=t.createContext(null),yt=R((function(e,t){let r=B(t),{enabled:o=!0,ownerDocument:l,...i}=e,a=T();return o?n.createElement(ht,{...i,ownerDocument:l,ref:r}):a({ourProps:{ref:r},theirProps:i,slot:{},defaultTag:pt,name:"Portal"})})),wt=R((function(e,t){let{target:r,...o}=e,l={ref:B(t)},i=T();return n.createElement(gt.Provider,{value:r},i({ourProps:l,theirProps:o,defaultTag:vt,name:"Popover.Group"}))})),Et=Object.assign(yt,{Group:wt}),xt=t.createContext(null);function St({children:e,node:r}){let[o,l]=t.useState(null),i=Ct(null!=r?r:o);return n.createElement(xt.Provider,{value:i},e,null===i&&n.createElement(H,{features:I.Hidden,ref:e=>{var t,n;if(e)for(let r of null!=(n=null==(t=h(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&V(r)&&null!=r&&r.contains(e)){l(r);break}}}))}function Ct(e=null){var n;return null!=(n=t.useContext(xt))?n:e}function Ft(){let e=t.useRef(!1);return y((()=>(e.current=!0,()=>{e.current=!1})),[]),e}var Nt=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(Nt||{});function Pt(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)V(n.current)&&t.add(n.current);return t}var kt=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(kt||{});let Tt=R((function(e,r){let o=t.useRef(null),l=B(o,r),{initialFocus:i,initialFocusFallback:a,containers:u,features:s=15,...c}=e;dt()||(s=0);let d=Ke(o);!function(e,{ownerDocument:n}){let r=!!(8&e),o=function(e=!0){let n=t.useRef(st.slice());return rt((([e],[t])=>{!0===t&&!1===e&&v((()=>{n.current.splice(0)})),!1===t&&!0===e&&(n.current=st.slice())}),[e,st,n]),E((()=>{var e;return null!=(e=n.current.find((e=>null!=e&&e.isConnected)))?e:null}))}(r);rt((()=>{r||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&$e(o())}),[r]),ct((()=>{r&&$e(o())}))}(s,{ownerDocument:d});let f=function(e,{ownerDocument:n,container:r,initialFocus:o,initialFocusFallback:l}){let i=t.useRef(null),a=ke(!!(1&e),"focus-trap#initial-focus"),u=Ft();return rt((()=>{if(0===e)return;if(!a)return void(null!=l&&l.current&&$e(l.current));let t=r.current;t&&v((()=>{if(!u.current)return;let r=null==n?void 0:n.activeElement;if(null!=o&&o.current){if((null==o?void 0:o.current)===r)return void(i.current=r)}else if(t.contains(r))return void(i.current=r);if(null!=o&&o.current)$e(o.current);else{if(16&e){if(Ue(t,Me.First|Me.AutoFocus)!==Ie.Error)return}else if(Ue(t,Me.First)!==Ie.Error)return;if(null!=l&&l.current&&($e(l.current),(null==n?void 0:n.activeElement)===l.current))return}i.current=null==n?void 0:n.activeElement}))}),[l,a,e]),i}(s,{ownerDocument:d,container:o,initialFocus:i,initialFocusFallback:a});!function(e,{ownerDocument:t,container:n,containers:r,previousActiveElement:o}){let l=Ft(),i=!!(4&e);ze(null==t?void 0:t.defaultView,"focus",(e=>{if(!i||!l.current)return;let t=Pt(r);$(n.current)&&t.add(n.current);let a=o.current;if(!a)return;let u=e.target;$(u)?Ot(t,u)?(o.current=u,$e(u)):(e.preventDefault(),e.stopPropagation(),$e(a)):$e(o.current)}),!0)}(s,{ownerDocument:d,container:o,containers:u,previousActiveElement:f});let m=function(){let e=t.useRef(0);return Ge(!0,"keydown",(t=>{"Tab"===t.key&&(e.current=t.shiftKey?1:0)}),!0),e}(),p=E((e=>{if(!$(o.current))return;let t=o.current;C(m.current,{[Nt.Forwards]:()=>{Ue(t,Me.First,{skipElements:[e.relatedTarget,a]})},[Nt.Backwards]:()=>{Ue(t,Me.Last,{skipElements:[e.relatedTarget,a]})}})})),h=ke(!!(2&s),"focus-trap#tab-lock"),g=b(),y=t.useRef(!1),w={ref:l,onKeyDown(e){"Tab"==e.key&&(y.current=!0,g.requestAnimationFrame((()=>{y.current=!1})))},onBlur(e){if(!(4&s))return;let t=Pt(u);$(o.current)&&t.add(o.current);let n=e.relatedTarget;_(n)&&"true"!==n.dataset.headlessuiFocusGuard&&(Ot(t,n)||(y.current?Ue(o.current,C(m.current,{[Nt.Forwards]:()=>Me.Next,[Nt.Backwards]:()=>Me.Previous})|Me.WrapAround,{relativeTo:e.target}):_(e.target)&&$e(e.target)))}},x=T();return n.createElement(n.Fragment,null,h&&n.createElement(H,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:I.Focusable}),x({ourProps:w,theirProps:c,defaultTag:"div",name:"FocusTrap"}),h&&n.createElement(H,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:I.Focusable}))})),jt=Object.assign(Tt,{features:kt});function Ot(e,t){for(let n of e)if(n.contains(t))return!0;return!1}function At(e){var r;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(r=e.as)?r:Ht)!==t.Fragment||1===n.Children.count(e.children)}let Rt=t.createContext(null);Rt.displayName="TransitionContext";var Lt=(e=>(e.Visible="visible",e.Hidden="hidden",e))(Lt||{});let Dt=t.createContext(null);function Mt(e){return"children"in e?Mt(e.children):e.current.filter((({el:e})=>null!==e.current)).filter((({state:e})=>"visible"===e)).length>0}function It(e,n){let r=w(e),o=t.useRef([]),l=Ft(),i=b(),a=E(((e,t=k.Hidden)=>{let n=o.current.findIndex((({el:t})=>t===e));-1!==n&&(C(t,{[k.Unmount](){o.current.splice(n,1)},[k.Hidden](){o.current[n].state="hidden"}}),i.microTask((()=>{var e;!Mt(o)&&l.current&&(null==(e=r.current)||e.call(r))})))})),u=E((e=>{let t=o.current.find((({el:t})=>t===e));return t?"visible"!==t.state&&(t.state="visible"):o.current.push({el:e,state:"visible"}),()=>a(e,k.Unmount)})),s=t.useRef([]),c=t.useRef(Promise.resolve()),d=t.useRef({enter:[],leave:[]}),f=E(((e,t,r)=>{s.current.splice(0),n&&(n.chains.current[t]=n.chains.current[t].filter((([t])=>t!==e))),null==n||n.chains.current[t].push([e,new Promise((e=>{s.current.push(e)}))]),null==n||n.chains.current[t].push([e,new Promise((e=>{Promise.all(d.current[t].map((([e,t])=>t))).then((()=>e()))}))]),"enter"===t?c.current=c.current.then((()=>null==n?void 0:n.wait.current)).then((()=>r(t))):r(t)})),m=E(((e,t,n)=>{Promise.all(d.current[t].splice(0).map((([e,t])=>t))).then((()=>{var e;null==(e=s.current.shift())||e()})).then((()=>n(t)))}));return t.useMemo((()=>({children:o,register:u,unregister:a,onStart:f,onStop:m,wait:c,chains:d})),[u,a,o,f,m,d,c])}Dt.displayName="NestingContext";let Ht=t.Fragment,Wt=P.RenderStrategy,Vt=R((function(e,r){let{show:o,appear:l=!1,unmount:i=!0,...a}=e,u=t.useRef(null),s=B(...At(e)?[u,r]:null===r?[]:[r]);dt();let c=it();if(void 0===o&&null!==c&&(o=(c&lt.Open)===lt.Open),void 0===o)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[d,f]=t.useState(o?"visible":"hidden"),m=It((()=>{o||f("hidden")})),[p,h]=t.useState(!0),v=t.useRef([o]);y((()=>{!1!==p&&v.current[v.current.length-1]!==o&&(v.current.push(o),h(!1))}),[v,o]);let g=t.useMemo((()=>({show:o,appear:l,initial:p})),[o,l,p]);y((()=>{o?f("visible"):!Mt(m)&&null!==u.current&&f("hidden")}),[o,m]);let b={unmount:i},w=E((()=>{var t;p&&h(!1),null==(t=e.beforeEnter)||t.call(e)})),x=E((()=>{var t;p&&h(!1),null==(t=e.beforeLeave)||t.call(e)})),S=T();return n.createElement(Dt.Provider,{value:m},n.createElement(Rt.Provider,{value:g},S({ourProps:{...b,as:t.Fragment,children:n.createElement($t,{ref:s,...b,...a,beforeEnter:w,beforeLeave:x})},theirProps:{},defaultTag:t.Fragment,features:Wt,visible:"visible"===d,name:"Transition"})))})),$t=R((function(e,r){var o,l;let{transition:i=!0,beforeEnter:a,afterEnter:u,beforeLeave:s,afterLeave:c,enter:d,enterFrom:f,enterTo:m,entered:p,leave:h,leaveFrom:v,leaveTo:w,...x}=e,[F,N]=t.useState(null),P=t.useRef(null),j=At(e),O=B(...j?[P,r,N]:null===r?[]:[r]),A=null==(o=x.unmount)||o?k.Unmount:k.Hidden,{show:R,appear:D,initial:M}=function(){let e=t.useContext(Rt);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[I,H]=t.useState(R?"visible":"hidden"),W=function(){let e=t.useContext(Dt);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:V,unregister:$}=W;y((()=>V(P)),[V,P]),y((()=>{if(A===k.Hidden&&P.current)return R&&"visible"!==I?void H("visible"):C(I,{hidden:()=>$(P),visible:()=>V(P)})}),[I,P,V,$,R,A]);let _=dt();y((()=>{if(j&&_&&"visible"===I&&null===P.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[P,I,_,j]);let U=M&&!D,q=D&&R&&M,Y=t.useRef(!1),G=It((()=>{Y.current||(H("hidden"),$(P))}),W),K=E((e=>{Y.current=!0;let t=e?"enter":"leave";G.onStart(P,t,(e=>{"enter"===e?null==a||a():"leave"===e&&(null==s||s())}))})),z=E((e=>{let t=e?"enter":"leave";Y.current=!1,G.onStop(P,t,(e=>{"enter"===e?null==u||u():"leave"===e&&(null==c||c())})),"leave"===t&&!Mt(G)&&(H("hidden"),$(P))}));t.useEffect((()=>{j&&i||(K(R),z(R))}),[R,j,i]);let X=(()=>!(!i||!j||!_||U))(),[,Z]=function(e,n,r,o){let[l,i]=t.useState(r),{hasFlag:a,addFlag:u,removeFlag:s}=function(e=0){let[n,r]=t.useState(e),o=t.useCallback((e=>r(e)),[n]),l=t.useCallback((e=>r((t=>t|e))),[n]),i=t.useCallback((e=>(n&e)===e),[n]),a=t.useCallback((e=>r((t=>t&~e))),[r]),u=t.useCallback((e=>r((t=>t^e))),[r]);return{flags:n,setFlag:o,addFlag:l,hasFlag:i,removeFlag:a,toggleFlag:u}}(e&&l?3:0),c=t.useRef(!1),d=t.useRef(!1),f=b();return y((()=>{var t;if(e)return r&&i(!0),n?(null==(t=null==o?void 0:o.start)||t.call(o,r),function(e,{prepare:t,run:n,done:r,inFlight:o}){let l=g();return function(e,{inFlight:t,prepare:n}){if(null!=t&&t.current)return void n();let r=e.style.transition;e.style.transition="none",n(),e.offsetHeight,e.style.transition=r}(e,{prepare:t,inFlight:o}),l.nextFrame((()=>{n(),l.requestAnimationFrame((()=>{l.add(function(e,t){var n,r;let o=g();if(!e)return o.dispose;let l=!1;o.add((()=>{l=!0}));let i=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter((e=>e instanceof CSSTransition)))?r:[];return 0===i.length?(t(),o.dispose):(Promise.allSettled(i.map((e=>e.finished))).then((()=>{l||t()})),o.dispose)}(e,r))}))})),l.dispose}(n,{inFlight:c,prepare(){d.current?d.current=!1:d.current=c.current,c.current=!0,!d.current&&(r?(u(3),s(4)):(u(4),s(2)))},run(){d.current?r?(s(3),u(4)):(s(4),u(3)):r?s(1):u(1)},done(){var e;d.current&&"function"==typeof n.getAnimations&&n.getAnimations().length>0||(c.current=!1,s(7),r||i(!1),null==(e=null==o?void 0:o.end)||e.call(o,r))}})):void(r&&u(3))}),[e,r,n,f]),e?[l,{closed:a(1),enter:a(2),leave:a(4),transition:a(2)||a(4)}]:[r,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(X,F,R,{start:K,end:z}),J=L({ref:O,className:(null==(l=S(x.className,q&&d,q&&f,Z.enter&&d,Z.enter&&Z.closed&&f,Z.enter&&!Z.closed&&m,Z.leave&&h,Z.leave&&!Z.closed&&v,Z.leave&&Z.closed&&w,!Z.transition&&R&&p))?void 0:l.trim())||void 0,...nt(Z)}),Q=0;"visible"===I&&(Q|=lt.Open),"hidden"===I&&(Q|=lt.Closed),R&&"hidden"===I&&(Q|=lt.Opening),!R&&"visible"===I&&(Q|=lt.Closing);let ee=T();return n.createElement(Dt.Provider,{value:G},n.createElement(at,{value:Q},ee({ourProps:J,theirProps:x,defaultTag:Ht,features:Wt,visible:"visible"===I,name:"Transition.Child"})))})),_t=R((function(e,r){let o=null!==t.useContext(Rt),l=null!==it();return n.createElement(n.Fragment,null,!o&&l?n.createElement(Vt,{ref:r,...e}):n.createElement($t,{ref:r,...e}))})),Ut=Object.assign(Vt,{Child:_t,Root:Vt});var Bt=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Bt||{}),qt=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(qt||{});let Yt={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},Gt=t.createContext(null);function Kt(e){let n=t.useContext(Gt);if(null===n){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Kt),t}return n}function zt(e,t){return C(t.type,Yt,e,t)}Gt.displayName="DialogContext";let Xt=R((function(e,r){let o=t.useId(),{id:l=`headlessui-dialog-${o}`,open:i,onClose:a,initialFocus:u,role:s="dialog",autoFocus:c=!0,__demoMode:d=!1,unmount:f=!1,...m}=e,p=t.useRef(!1);s="dialog"===s||"alertdialog"===s?s:(p.current||(p.current=!0),"dialog");let v=it();void 0===i&&null!==v&&(i=(v&lt.Open)===lt.Open);let b=t.useRef(null),x=B(b,r),S=Ke(b),F=i?0:1,[N,P]=t.useReducer(zt,{titleId:null,descriptionId:null,panelRef:t.createRef()}),k=E((()=>a(!1))),j=E((e=>P({type:0,id:e}))),O=!!dt()&&0===F,[A,R]=function(){let e=t.useContext(bt),r=t.useRef([]),o=E((t=>(r.current.push(t),e&&e.register(t),()=>l(t)))),l=E((t=>{let n=r.current.indexOf(t);-1!==n&&r.current.splice(n,1),e&&e.unregister(t)})),i=t.useMemo((()=>({register:o,unregister:l,portals:r})),[o,l,r]);return[r,t.useMemo((()=>function({children:e}){return n.createElement(bt.Provider,{value:i},e)}),[i])]}(),L={get current(){var e;return null!=(e=N.panelRef.current)?e:b.current}},D=Ct(),{resolveContainers:M}=function({defaultContainers:e=[],portals:t,mainTreeNode:n}={}){let r=Ke(n),o=E((()=>{var o,l;let i=[];for(let t of e)null!==t&&(V(t)?i.push(t):"current"in t&&V(t.current)&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&V(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(l=null==n?void 0:n.getRootNode())?void 0:l.host))||i.some((t=>e.contains(t)))||i.push(e));return i}));return{resolveContainers:o,contains:E((e=>o().some((t=>t.contains(e)))))}}({mainTreeNode:D,portals:A,defaultContainers:[L]}),I=null!==v&&(v&lt.Closing)===lt.Closing;!function(e,{allowed:t,disallowed:n}={}){let r=ke(e,"inert-others");y((()=>{var e,o;if(!r)return;let l=g();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&l.add(Oe(t));let i=null!=(o=null==t?void 0:t())?o:[];for(let t of i){if(!t)continue;let e=h(t);if(!e)continue;let n=t.parentElement;for(;n&&n!==e.body;){for(let e of n.children)i.some((t=>e.contains(t)))||l.add(Oe(e));n=n.parentElement}}return l.dispose}),[r,t,n])}(!d&&!I&&O,{allowed:E((()=>{var e,t;return[null!=(t=null==(e=b.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]})),disallowed:E((()=>{var e;return[null!=(e=null==D?void 0:D.closest("body > *:not(#headlessui-portal-root)"))?e:null]}))});let H=be.get(null);y((()=>{if(O)return H.actions.push(l),()=>H.actions.pop(l)}),[H,l,O]);let W=Ne(H,t.useCallback((e=>H.selectors.isTop(e,l)),[H,l]));(function(e,n,r){let o=w(r),l=t.useCallback((function(e,t){if(e.defaultPrevented)return;let r=t(e);if(null===r||!r.getRootNode().contains(r)||!r.isConnected)return;let l=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(n);for(let n of l)if(null!==n&&(n.contains(r)||e.composed&&e.composedPath().includes(n)))return;return!function(e,t=0){var n;return e!==(null==(n=h(e))?void 0:n.body)&&C(t,{0:()=>e.matches(Ae),1(){let t=e;for(;null!==t;){if(t.matches(Ae))return!0;t=t.parentElement}return!1}})}(r,We.Loose)&&-1!==r.tabIndex&&e.preventDefault(),o.current(e,r)}),[o,n]),i=t.useRef(null);Ye(e,"pointerdown",(e=>{var t,n;qe()||(i.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),Ye(e,"pointerup",(e=>{if(qe()||!i.current)return;let t=i.current;return i.current=null,l(e,(()=>t))}),!0);let a=t.useRef({x:0,y:0});Ye(e,"touchstart",(e=>{a.current.x=e.touches[0].clientX,a.current.y=e.touches[0].clientY}),!0),Ye(e,"touchend",(e=>{let t=e.changedTouches[0].clientX,n=e.changedTouches[0].clientY;if(!(Math.abs(t-a.current.x)>=30||Math.abs(n-a.current.y)>=30))return l(e,(()=>_(e.target)?e.target:null))}),!0),Ge(e,"blur",(e=>l(e,(()=>function(e){return $(e)&&"IFRAME"===e.nodeName}(window.document.activeElement)?window.document.activeElement:null))),!0)})(W,M,(e=>{e.preventDefault(),k()})),function(e,t=("undefined"!=typeof document?document.defaultView:null),n){let r=ke(e,"escape");ze(t,"keydown",(e=>{r&&(e.defaultPrevented||e.key===X.Escape&&n(e))}))}(W,null==S?void 0:S.defaultView,(e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),k()})),function(e,n,r=()=>[document.body]){!function(e,n,r=()=>({containers:[]})){let o=function(e){return t.useSyncExternalStore(e.subscribe,e.getSnapshot,e.getSnapshot)}(Je),l=n?o.get(n):void 0,i=!!l&&l.count>0;y((()=>{if(n&&e)return Je.dispatch("PUSH",n,r),()=>Je.dispatch("POP",n,r)}),[e,n])}(ke(e,"scroll-lock"),n,(e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],r]}}))}(!d&&!I&&O,S,M),function(e,n,r){let o=w((e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&r()}));t.useEffect((()=>{if(!e)return;let t=null===n?null:$(n)?n:n.current;if(!t)return;let r=g();if("undefined"!=typeof ResizeObserver){let e=new ResizeObserver((()=>o.current(t)));e.observe(t),r.add((()=>e.disconnect()))}if("undefined"!=typeof IntersectionObserver){let e=new IntersectionObserver((()=>o.current(t)));e.observe(t),r.add((()=>e.disconnect()))}return()=>r.dispose()}),[n,o,e])}(O,b,k);let[U,Y]=function(){let[e,r]=t.useState([]);return[e.length>0?e.join(" "):void 0,t.useMemo((()=>function(e){let o=E((e=>(r((t=>[...t,e])),()=>r((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),l=t.useMemo((()=>({register:o,slot:e.slot,name:e.name,props:e.props,value:e.value})),[o,e.slot,e.name,e.props,e.value]);return n.createElement(q.Provider,{value:l},e.children)}),[r])]}(),G=t.useMemo((()=>[{dialogState:F,close:k,setTitleId:j,unmount:f},N]),[F,N,k,j,f]),K=t.useMemo((()=>({open:0===F})),[F]),z={ref:x,id:l,role:s,tabIndex:-1,"aria-modal":d?void 0:0===F||void 0,"aria-labelledby":N.titleId,"aria-describedby":U,unmount:f},Z=!function(){var e;let[n]=t.useState((()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null)),[r,o]=t.useState(null!=(e=null==n?void 0:n.matches)&&e);return y((()=>{if(n)return n.addEventListener("change",e),()=>n.removeEventListener("change",e);function e(e){o(e.matches)}}),[n]),r}(),Q=kt.None;O&&!d&&(Q|=kt.RestoreFocus,Q|=kt.TabLock,c&&(Q|=kt.AutoFocus),Z&&(Q|=kt.InitialFocus));let ee=T();return n.createElement(ut,null,n.createElement(mt,{force:!0},n.createElement(Et,null,n.createElement(Gt.Provider,{value:G},n.createElement(wt,{target:b},n.createElement(mt,{force:!1},n.createElement(Y,{slot:K},n.createElement(R,null,n.createElement(jt,{initialFocus:u,initialFocusFallback:b,containers:M,features:Q},n.createElement(J,{value:k},ee({ourProps:z,theirProps:m,slot:K,defaultTag:Zt,features:Jt,visible:0===F,name:"Dialog"})))))))))))})),Zt="div",Jt=P.RenderStrategy|P.Static,Qt=R((function(e,t){let{transition:r=!1,open:o,...l}=e,i=it(),a=e.hasOwnProperty("open")||null!==i,u=e.hasOwnProperty("onClose");if(!a&&!u)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!a)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!u)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&"boolean"!=typeof e.open)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if("function"!=typeof e.onClose)throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return void 0===o&&!r||l.static?n.createElement(St,null,n.createElement(Xt,{ref:t,open:o,...l})):n.createElement(St,null,n.createElement(Ut,{show:o,transition:r,unmount:l.unmount},n.createElement(Xt,{ref:t,...l})))})),en=R((function(e,r){let o=t.useId(),{id:l=`headlessui-dialog-panel-${o}`,transition:i=!1,...a}=e,[{dialogState:u,unmount:s},c]=Kt("Dialog.Panel"),d=B(r,c.panelRef),f=t.useMemo((()=>({open:0===u})),[u]),m={ref:d,id:l,onClick:E((e=>{e.stopPropagation()}))},p=i?_t:t.Fragment,h=i?{unmount:s}:{},v=T();return n.createElement(p,{...h},v({ourProps:m,theirProps:a,slot:f,defaultTag:"div",name:"Dialog.Panel"}))}));R((function(e,r){let{transition:o=!1,...l}=e,[{dialogState:i,unmount:a}]=Kt("Dialog.Backdrop"),u=t.useMemo((()=>({open:0===i})),[i]),s={ref:r,"aria-hidden":!0},c=o?_t:t.Fragment,d=o?{unmount:a}:{},f=T();return n.createElement(c,{...d},f({ourProps:s,theirProps:l,slot:u,defaultTag:"div",name:"Dialog.Backdrop"}))}));let tn=R((function(e,n){let r=t.useId(),{id:o=`headlessui-dialog-title-${r}`,...l}=e,[{dialogState:i,setTitleId:a}]=Kt("Dialog.Title"),u=B(n);t.useEffect((()=>(a(o),()=>a(null))),[o,a]);let s=t.useMemo((()=>({open:0===i})),[i]),c={ref:u,id:o};return T()({ourProps:c,theirProps:l,slot:s,defaultTag:"h2",name:"Dialog.Title"})})),nn=Object.assign(Qt,{Panel:en,Title:tn,Description:K});const rn=({isOpen:e,onClose:r,product:o,onAddToCart:l})=>{var f;const[m,p]=t.useState(null),[h,v]=t.useState(1);n.useEffect((()=>{var t;e&&(null==(t=null==o?void 0:o.variants)?void 0:t.length)>0&&(p(o.variants[0].id),v(1))}),[e,o]);const g=t.useCallback((()=>{v((e=>e+1))}),[]),b=t.useCallback((()=>{v((e=>e>1?e-1:1))}),[]),y=t.useCallback((()=>{if(!o||!m)return;const e=o.variants.find((e=>e.id===m)),t={...o,quantity:h,variantId:m,variantName:(null==e?void 0:e.name)||"",variantColor:(null==e?void 0:e.color)||""};l(t),r()}),[o,m,h,l,r]);return o?i.jsx(Ut,{appear:!0,show:e,children:i.jsx(nn,{as:"div",className:"fixed inset-0 z-50 overflow-y-auto",onClose:r,children:i.jsxs("div",{className:"min-h-screen px-4 flex items-center justify-center",children:[i.jsx(Ut.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:i.jsx("div",{className:"fixed inset-0 bg-black/50","aria-hidden":"true"})}),i.jsx(Ut.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:i.jsxs(nn.Panel,{className:"relative w-full max-w-lg mx-auto bg-white rounded-2xl shadow-xl p-6",children:[i.jsx("button",{onClick:r,className:"absolute top-4 right-4 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:i.jsx(a,{className:"w-5 h-5 text-gray-600"})}),i.jsx(nn.Title,{className:"text-xl font-bold text-gray-800 mb-6 pr-8",children:"Chọn thuộc tính sản phẩm"}),i.jsxs("div",{className:"flex gap-4 mb-6",children:[i.jsx("div",{className:"w-20 h-20 bg-gray-50 rounded-lg flex items-center justify-center",children:i.jsx("img",{src:o.image,alt:o.name,className:"max-h-16 max-w-16 object-contain"})}),i.jsxs("div",{className:"flex-1",children:[i.jsx("h3",{className:"font-medium text-gray-800 mb-1",children:o.name}),i.jsx("p",{className:"text-2xl font-bold text-dexin-primary",children:(w=o.price,new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(w))})]})]}),o.variants&&o.variants.length>0&&i.jsxs("div",{className:"mb-6",children:[i.jsxs("h4",{className:"text-base font-semibold text-gray-800 mb-3",children:["Chọn màu sắc ",i.jsx("span",{className:"text-red-500",children:"*"})]}),i.jsx("div",{className:"flex gap-3",children:o.variants.map((e=>i.jsx(u.button,{className:"relative w-12 h-12 rounded-full border-2 "+(m===e.id?"border-dexin-primary ring-2 ring-dexin-primary ring-offset-2":"border-gray-300 hover:border-gray-400"),style:{backgroundColor:e.color},onClick:()=>p(e.id),whileHover:{scale:1.1},whileTap:{scale:.9},title:e.name,children:m===e.id&&i.jsx("div",{className:"absolute inset-0 rounded-full flex items-center justify-center",children:i.jsx("div",{className:"w-2 h-2 bg-white rounded-full shadow-md"})})},e.id)))}),m&&i.jsxs("p",{className:"text-sm text-gray-600 mt-2",children:["Đã chọn: ",null==(f=o.variants.find((e=>e.id===m)))?void 0:f.name]})]}),i.jsxs("div",{className:"mb-6",children:[i.jsx("h4",{className:"text-base font-semibold text-gray-800 mb-3",children:"Số lượng"}),i.jsxs("div",{className:"inline-flex items-center border border-gray-300 rounded-lg overflow-hidden",children:[i.jsx("button",{onClick:b,className:"p-2 hover:bg-gray-100 transition-colors",children:i.jsx(s,{className:"w-4 h-4"})}),i.jsx("div",{className:"px-4 py-2 bg-white border-x border-gray-300 min-w-[3rem] text-center",children:h}),i.jsx("button",{onClick:g,className:"p-2 hover:bg-gray-100 transition-colors",children:i.jsx(c,{className:"w-4 h-4"})})]})]}),i.jsxs("div",{className:"flex gap-3",children:[i.jsx("button",{onClick:r,className:"flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Hủy"}),i.jsxs("button",{onClick:y,disabled:!m,className:"flex-1 py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2 "+(m?"bg-dexin-light text-white hover:bg-pink-600":"bg-gray-200 text-gray-400 cursor-not-allowed"),children:[i.jsx(d,{className:"w-5 h-5"}),"Thêm vào giỏ hàng"]})]})]})})]})})}):null;var w};export{nn as L,rn as P,d as S,Ut as z};
