import Compressor from 'compressorjs';

/**
 * Utility functions cho việc upload file và xử lý ảnh
 */

// Các format ảnh được hỗ trợ
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp'
];

// Kích thước file tối đa (5MB)
export const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Kích thước ảnh avatar sau khi compress
export const AVATAR_SIZE = {
  width: 400,
  height: 400,
  quality: 0.8
};

/**
 * Validate file trước khi upload
 * @param {File} file - File cần validate
 * @returns {Object} - { isValid: boolean, error: string }
 */
export const validateImageFile = (file) => {
  if (!file) {
    return { isValid: false, error: 'Vui lòng chọn file ảnh' };
  }

  // Kiểm tra format
  if (!SUPPORTED_IMAGE_FORMATS.includes(file.type)) {
    return { 
      isValid: false, 
      error: 'Định dạng file không được hỗ trợ. Vui lòng chọn file JPG, PNG hoặc WebP' 
    };
  }

  // Kiểm tra kích thước
  if (file.size > MAX_FILE_SIZE) {
    return { 
      isValid: false, 
      error: 'File quá lớn. Vui lòng chọn file nhỏ hơn 5MB' 
    };
  }

  return { isValid: true, error: null };
};

/**
 * Compress ảnh trước khi upload
 * @param {File} file - File ảnh gốc
 * @param {Object} options - Tùy chọn compress
 * @returns {Promise<File>} - File đã được compress
 */
export const compressImage = (file, options = {}) => {
  return new Promise((resolve, reject) => {
    const defaultOptions = {
      quality: AVATAR_SIZE.quality,
      maxWidth: AVATAR_SIZE.width,
      maxHeight: AVATAR_SIZE.height,
      convertSize: 1000000, // Convert to JPEG if larger than 1MB
      ...options
    };

    new Compressor(file, {
      ...defaultOptions,
      success: (compressedFile) => {
        console.log('✅ Image compressed successfully:', {
          originalSize: (file.size / 1024).toFixed(2) + 'KB',
          compressedSize: (compressedFile.size / 1024).toFixed(2) + 'KB',
          compressionRatio: ((1 - compressedFile.size / file.size) * 100).toFixed(1) + '%'
        });
        resolve(compressedFile);
      },
      error: (error) => {
        console.error('❌ Image compression failed:', error);
        reject(new Error('Không thể nén ảnh. Vui lòng thử lại.'));
      }
    });
  });
};

/**
 * Tạo preview URL cho ảnh
 * @param {File} file - File ảnh
 * @returns {string} - URL preview
 */
export const createImagePreview = (file) => {
  return URL.createObjectURL(file);
};

/**
 * Cleanup preview URL
 * @param {string} url - URL preview cần cleanup
 */
export const cleanupImagePreview = (url) => {
  if (url && url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
};

/**
 * Tạo FormData cho upload avatar
 * @param {File} file - File ảnh đã được compress
 * @returns {FormData} - FormData ready để upload
 */
export const createAvatarFormData = (file) => {
  const formData = new FormData();
  formData.append('avatar', file);
  return formData;
};

/**
 * Utility để resize ảnh canvas (nếu cần)
 * @param {File} file - File ảnh
 * @param {number} maxWidth - Chiều rộng tối đa
 * @param {number} maxHeight - Chiều cao tối đa
 * @returns {Promise<Blob>} - Blob ảnh đã resize
 */
export const resizeImageCanvas = (file, maxWidth = 400, maxHeight = 400) => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Tính toán kích thước mới giữ tỷ lệ
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Vẽ ảnh lên canvas
      ctx.drawImage(img, 0, 0, width, height);

      // Convert canvas thành blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Không thể resize ảnh'));
          }
        },
        file.type,
        0.8
      );
    };

    img.onerror = () => {
      reject(new Error('Không thể load ảnh'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Format file size để hiển thị
 * @param {number} bytes - Kích thước file tính bằng bytes
 * @returns {string} - Kích thước đã format
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Kiểm tra browser có hỗ trợ File API không
 * @returns {boolean}
 */
export const isFileAPISupported = () => {
  return !!(window.File && window.FileReader && window.FileList && window.Blob);
};
