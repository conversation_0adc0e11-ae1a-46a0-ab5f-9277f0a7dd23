import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { X, Upload, User, FileText, DollarSign, Image, File } from 'lucide-react';
import { toast } from 'react-toastify';

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '../../../components/ui/dialog';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Textarea } from '../../../components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select';
import { Card, CardContent } from '../../../components/ui/card';
import { Avatar, AvatarImage, AvatarFallback } from '../../../components/ui/avatar';

import { designService } from '../../../services/designService';
import DesignSuccessDialog from './DesignSuccessDialog';

const AddDesignDialog = ({ isOpen, onClose, onSuccess }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [loadingCustomers, setLoadingCustomers] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [successData, setSuccessData] = useState(null);
  
  const [formData, setFormData] = useState({
    title: '',
    pathUrl: null,
    thumbnailUrl: null,
    note: '',
    price: '',
    customerId: ''
  });

  const [previews, setPreviews] = useState({
    pathUrl: null,
    thumbnailUrl: null
  });

  // Load customers khi dialog mở
  useEffect(() => {
    if (isOpen) {
      loadCustomers();
    }
  }, [isOpen]);

  // Load danh sách khách hàng
  const loadCustomers = async () => {
    setLoadingCustomers(true);
    try {
      const result = await designService.getAllCustomers();
      if (result.success) {
        setCustomers(result.data);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Load customers error:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách khách hàng');
    } finally {
      setLoadingCustomers(false);
    }
  };

  // Xử lý thay đổi input
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Xử lý upload file
  const handleFileChange = (field, file) => {
    if (!file) return;

    // Validate file
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      toast.error('File quá lớn. Vui lòng chọn file nhỏ hơn 10MB');
      return;
    }

    // Update form data
    setFormData(prev => ({
      ...prev,
      [field]: file
    }));

    // Create preview for images
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviews(prev => ({
          ...prev,
          [field]: e.target.result
        }));
      };
      reader.readAsDataURL(file);
    } else {
      setPreviews(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    if (!formData.title.trim()) {
      toast.error('Vui lòng nhập tiêu đề thiết kế');
      return false;
    }
    if (!formData.pathUrl) {
      toast.error('Vui lòng chọn file thiết kế 3D');
      return false;
    }
    if (!formData.thumbnailUrl) {
      toast.error('Vui lòng chọn ảnh thumbnail');
      return false;
    }
    if (!formData.price || isNaN(formData.price) || Number(formData.price) <= 0) {
      toast.error('Vui lòng nhập giá hợp lệ');
      return false;
    }
    if (!formData.customerId) {
      toast.error('Vui lòng chọn khách hàng');
      return false;
    }
    return true;
  };

  // Xử lý submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    try {
      console.log('🔄 Submitting design creation...', {
        title: formData.title.trim(),
        customerId: Number(formData.customerId),
        price: Number(formData.price),
        hasPathUrl: !!formData.pathUrl,
        hasThumbnail: !!formData.thumbnailUrl
      });

      const result = await designService.create3DDesign({
        title: formData.title.trim(),
        pathUrl: formData.pathUrl,
        thumbnailUrl: formData.thumbnailUrl,
        note: formData.note.trim(),
        price: Number(formData.price),
        customerId: Number(formData.customerId)
      });

      console.log('📤 Design creation result:', result);

      if (result.success) {
        // Tìm thông tin khách hàng đã chọn
        const selectedCustomer = customers.find(c => c.userAccountId.toString() === formData.customerId);

        // Hiển thị success dialog với thông tin chi tiết
        setSuccessData({
          ...result.data,
          customerInfo: selectedCustomer
        });
        setShowSuccessDialog(true);
        handleClose(); // Đóng form dialog

        // Gọi callback để reload danh sách
        if (onSuccess) {
          onSuccess(result.data);
        }
      } else {
        // Xử lý các loại lỗi cụ thể
        if (result.message.includes('Phiên đăng nhập đã hết hạn')) {
          toast.error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
          // Có thể thêm logic để refresh token hoặc redirect
        } else if (result.message.includes('không có quyền')) {
          toast.error('Bạn không có quyền thực hiện thao tác này.');
        } else {
          toast.error(result.message);
        }
      }
    } catch (error) {
      console.error('❌ Submit error:', error);

      // Xử lý lỗi network hoặc unexpected errors
      if (error.response?.status === 401) {
        toast.error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
      } else if (error.response?.status === 403) {
        toast.error('Bạn không có quyền thực hiện thao tác này.');
      } else if (error.response?.status >= 500) {
        toast.error('Lỗi server. Vui lòng thử lại sau.');
      } else if (error.code === 'NETWORK_ERROR') {
        toast.error('Lỗi kết nối mạng. Vui lòng kiểm tra kết nối.');
      } else {
        toast.error('Có lỗi xảy ra khi tạo thiết kế. Vui lòng thử lại.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Đóng dialog và reset form
  const handleClose = () => {
    setFormData({
      title: '',
      pathUrl: null,
      thumbnailUrl: null,
      note: '',
      price: '',
      customerId: ''
    });
    setPreviews({
      pathUrl: null,
      thumbnailUrl: null
    });
    onClose();
  };

  // Get customer display name
  const getCustomerDisplayName = (customer) => {
    return `${customer.firstName} ${customer.lastName}`.trim() || customer.userName;
  };

  // Get customer initials
  const getCustomerInitials = (customer) => {
    const name = getCustomerDisplayName(customer);
    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-dexin-primary flex items-center space-x-2">
              <Upload className="w-5 h-5" />
              <span>Thêm thiết kế 3D mới</span>
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
          {/* Tiêu đề */}
          <div className="space-y-2">
            <Label htmlFor="title" className="flex items-center space-x-2">
              <FileText className="w-4 h-4" />
              <span>Tiêu đề thiết kế *</span>
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Nhập tiêu đề thiết kế..."
              disabled={isLoading}
            />
          </div>

          {/* Khách hàng */}
          <div className="space-y-2">
            <Label className="flex items-center space-x-2">
              <User className="w-4 h-4" />
              <span>Khách hàng *</span>
            </Label>
            <Select
              value={formData.customerId}
              onValueChange={(value) => handleInputChange('customerId', value)}
              disabled={isLoading || loadingCustomers}
            >
              <SelectTrigger>
                <SelectValue placeholder={loadingCustomers ? "Đang tải..." : "Chọn khách hàng"} />
              </SelectTrigger>
              <SelectContent>
                {customers.map((customer) => (
                  <SelectItem key={customer.userAccountId} value={customer.userAccountId.toString()}>
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-6 h-6">
                        <AvatarImage src={customer.avartar} alt={getCustomerDisplayName(customer)} />
                        <AvatarFallback className="text-xs">
                          {getCustomerInitials(customer)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{getCustomerDisplayName(customer)}</div>
                        <div className="text-xs text-gray-500">{customer.email}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* File uploads */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* File thiết kế 3D */}
            <div className="space-y-2">
              <Label className="flex items-center space-x-2">
                <File className="w-4 h-4" />
                <span>File thiết kế 3D *</span>
              </Label>
              <Card className="border-dashed border-2 hover:border-dexin-primary transition-colors">
                <CardContent className="p-4">
                  <input
                    type="file"
                    id="pathUrl"
                    accept=".obj,.fbx,.gltf,.glb,image/*"
                    onChange={(e) => handleFileChange('pathUrl', e.target.files[0])}
                    className="hidden"
                    disabled={isLoading}
                  />
                  <label
                    htmlFor="pathUrl"
                    className="cursor-pointer flex flex-col items-center space-y-2 text-center"
                  >
                    {formData.pathUrl ? (
                      <div className="text-green-600">
                        <File className="w-8 h-8 mx-auto mb-2" />
                        <div className="text-sm font-medium">{formData.pathUrl.name}</div>
                        <div className="text-xs text-gray-500">
                          {(formData.pathUrl.size / 1024 / 1024).toFixed(2)} MB
                        </div>
                      </div>
                    ) : (
                      <div className="text-gray-500">
                        <Upload className="w-8 h-8 mx-auto mb-2" />
                        <div className="text-sm">Chọn file thiết kế</div>
                        <div className="text-xs">OBJ, FBX, GLTF, hoặc ảnh</div>
                      </div>
                    )}
                  </label>
                </CardContent>
              </Card>
            </div>

            {/* Ảnh thumbnail */}
            <div className="space-y-2">
              <Label className="flex items-center space-x-2">
                <Image className="w-4 h-4" />
                <span>Ảnh thumbnail *</span>
              </Label>
              <Card className="border-dashed border-2 hover:border-dexin-primary transition-colors">
                <CardContent className="p-4">
                  <input
                    type="file"
                    id="thumbnailUrl"
                    accept="image/*"
                    onChange={(e) => handleFileChange('thumbnailUrl', e.target.files[0])}
                    className="hidden"
                    disabled={isLoading}
                  />
                  <label
                    htmlFor="thumbnailUrl"
                    className="cursor-pointer flex flex-col items-center space-y-2 text-center"
                  >
                    {previews.thumbnailUrl ? (
                      <div>
                        <img
                          src={previews.thumbnailUrl}
                          alt="Thumbnail preview"
                          className="w-16 h-16 object-cover rounded mx-auto mb-2"
                        />
                        <div className="text-sm font-medium text-green-600">{formData.thumbnailUrl.name}</div>
                      </div>
                    ) : (
                      <div className="text-gray-500">
                        <Image className="w-8 h-8 mx-auto mb-2" />
                        <div className="text-sm">Chọn ảnh thumbnail</div>
                        <div className="text-xs">JPG, PNG, WebP</div>
                      </div>
                    )}
                  </label>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Giá và ghi chú */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="price" className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4" />
                <span>Giá (VNĐ) *</span>
              </Label>
              <Input
                id="price"
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                placeholder="Nhập giá..."
                min="0"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="note">Ghi chú</Label>
            <Textarea
              id="note"
              value={formData.note}
              onChange={(e) => handleInputChange('note', e.target.value)}
              placeholder="Nhập ghi chú về thiết kế..."
              rows={3}
              disabled={isLoading}
            />
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-dexin-primary hover:bg-dexin-primary/90"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Đang tạo...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Tạo thiết kế
                </>
              )}
            </Button>
          </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <DesignSuccessDialog
        isOpen={showSuccessDialog}
        onClose={() => {
          setShowSuccessDialog(false);
          setSuccessData(null);
        }}
        designData={successData}
      />
    </>
  );
};

export default AddDesignDialog;
