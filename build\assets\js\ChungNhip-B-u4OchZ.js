import{c as e,r as a,j as i,S as s,p as t,m as r,a as n,B as l,H as c,L as d,F as m,f as h}from"./index-DdBL2cja.js";
/**
 * @license lucide-react v0.484.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x=e("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),o=e("thumbs-up",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]),p=[{id:1,author:"Quốc Bảo",avatar:"https://i.pravatar.cc/150?img=1",time:"1 phút trước",title:"Làm sao để phòng ngủ nhà vẫn có cảm giác rộng rãi?",content:"Phòng mình khá nhỏ, chỉ khoảng 9m², mà đồ đạc thì nhiều, lúc nào cũng thấy chật chội và bí bách. Mình đã thử dọn bớt đồ nhưng vẫn không đủ rộng như mong muốn. Mình nghĩ nếu có cách sắp xếp nội thất hoặc dùng...",tags:["#Decor","#PhòngNgủNhỏ","#KhôngGianNhỏ","#TổLàmĐẹpGọn","#MoiTrangTri"],likes:0,comments:0,views:0},{id:2,author:"Quỳnh Hương",avatar:"https://i.pravatar.cc/150?img=5",time:"21 phút trước",title:"Tự làm đèn LED treo tường chỉ với 100k!",content:"Gần đây mình đã tự DIY một chiếc đèn LED treo tường với chi phí chỉ khoảng 100k. Rất đơn giản và hiệu quả!",tags:["#DIY","#Decor","#DenLED","#TựLàm"],likes:0,comments:0,views:0}],g=[{id:1,user:"Hoàng Hải",avatar:"https://i.pravatar.cc/150?img=3",likes:2241},{id:2,user:"Kelly",avatar:"https://i.pravatar.cc/150?img=4",likes:1995},{id:3,user:"Ân",avatar:"https://i.pravatar.cc/150?img=6",likes:1921},{id:4,user:"Nguyen Phuc",avatar:"https://i.pravatar.cc/150?img=7",likes:1784},{id:5,user:"An",avatar:"https://i.pravatar.cc/150?img=8",likes:1678},{id:6,user:"Thu Lê",avatar:"https://i.pravatar.cc/150?img=9",likes:1565},{id:7,user:"Uyển Nhi",avatar:"https://i.pravatar.cc/150?img=10",likes:1555},{id:8,user:"Nhã Nhi",avatar:"https://i.pravatar.cc/150?img=11",likes:1503},{id:9,user:"Quốc Cường",avatar:"https://i.pravatar.cc/150?img=12",likes:1211},{id:10,user:"Hàn Hân",avatar:"https://i.pravatar.cc/150?img=13",likes:841}],u=["#decor","#nội_thất","#phòngngủnhỏ","#meotrângtri","#đènLED","#giường ngủ","#giấydántường","#tốiưukhônggian","#DIY"],y=()=>{const[e,y]=a.useState("newest"),v=e=>0===e?i.jsx(m,{icon:h,className:"text-dexin-gold"}):1===e?"🥈":2===e?"🥉":null,b=e=>{switch(e){case 0:return"bg-white border border-pink-100 rounded-lg shadow-sm mb-3";case 1:case 2:return"bg-white border border-pink-100 rounded-lg mb-3";default:return""}},w=e=>{switch(e){case 0:case 1:case 2:return"font-bold text-gray-900";default:return"font-medium text-gray-800"}},j=e=>{switch(e){case 0:case 1:case 2:return"text-gray-800 font-medium";default:return"text-gray-500 text-sm"}};return i.jsxs(i.Fragment,{children:[i.jsx(s,{title:t.chungnhip.title,description:t.chungnhip.description,keywords:t.chungnhip.keywords,url:t.chungnhip.url}),i.jsxs(r.div,{className:"bg-white min-h-screen pb-10",initial:{opacity:0},animate:{opacity:1},transition:{duration:.4},children:[i.jsx(r.div,{className:"pt-10 pb-16",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},children:i.jsx("div",{className:"container mx-auto px-4 sm:px-6",children:i.jsxs("div",{className:"flex flex-col lg:flex-row items-center",children:[i.jsxs(r.div,{className:"w-full ml-4 sm:ml-6 lg:ml-10 lg:w-1/2 lg:pr-1",initial:{opacity:0,x:-30},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},children:[i.jsxs("div",{className:"mb-10",children:[i.jsx(r.h1,{className:"text-2xl md:text-4xl font-bold text-dexin-primary mb-4",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},children:"Rất vui được đồng hành cùng bạn!"}),i.jsx(r.h2,{className:"text-3xl md:text-4xl font-bold text-dexin-primary",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},children:"Bạn đang tìm ý tưởng gì hôm nay?"})]}),i.jsx(r.div,{className:"w-full bg-pink-50 p-4 rounded-3xl shadow-sm",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.5},whileHover:{boxShadow:"0 4px 12px rgba(254, 124, 171, 0.2)"},children:i.jsxs("div",{className:"flex",children:[i.jsxs("div",{className:"relative w-full mr-2",children:[i.jsx(r.input,{whileFocus:{scale:1.01},type:"text",placeholder:"Tìm kiếm...",className:"w-full pl-12 pr-4 py-3 bg-white rounded-full border-2 border-pink-100 focus:outline-none focus:border-dexin-primary"}),i.jsx(n,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400",size:20})]}),i.jsx(r.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:i.jsx(l,{variant:"bolded",size:"md",children:"Tạo chủ đề"})})]})})]}),i.jsx("div",{className:"w-full lg:w-1/2 mt-8 lg:mt-0 flex justify-center",children:i.jsx("img",{src:"/images/chungnhip.png",alt:"Community Illustration",className:"max-w-full h-auto"})})]})})}),i.jsx(r.div,{className:"container mx-auto px-4 sm:px-6",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:i.jsxs("div",{className:"flex flex-col lg:flex-row gap-4 sm:gap-6 lg:gap-8",children:[i.jsxs("div",{className:"w-full lg:w-3/4",children:[i.jsx("div",{className:"mb-6 border-b border-pink-200",children:i.jsxs("div",{className:"flex",children:[i.jsx(r.button,{onClick:()=>y("newest"),className:"py-3 px-6 font-medium "+("newest"===e?"text-dexin-primary border-b-2 border-dexin-primary":"text-gray-600 hover:text-dexin-primary"),whileHover:{y:-2},whileTap:{y:0},children:"Bài Viết Mới Nhất"}),i.jsx(r.button,{onClick:()=>y("hot"),className:"py-3 px-6 font-medium "+("hot"===e?"text-dexin-primary border-b-2 border-dexin-primary":"text-gray-600 hover:text-dexin-primary"),whileHover:{y:-2},whileTap:{y:0},children:"Bài Viết Nổi Bật"})]})}),i.jsx("div",{className:"space-y-6",children:p.map(((e,a)=>i.jsx(r.div,{className:"bg-white rounded-xl shadow-sm overflow-hidden border border-pink-100",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2+.1*a},whileHover:{y:-5,boxShadow:"0 10px 20px rgba(254, 124, 171, 0.1)"},children:i.jsxs("div",{className:"p-6",children:[i.jsxs("div",{className:"flex items-center mb-4",children:[i.jsx(r.img,{src:e.avatar,alt:e.author,className:"w-10 h-10 rounded-full mr-3",whileHover:{scale:1.1}}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-medium text-gray-800",children:e.author}),i.jsx("p",{className:"text-sm text-gray-500",children:e.time})]})]}),i.jsx("h2",{className:"text-xl font-bold mb-3 text-gray-800",children:e.title}),i.jsx("p",{className:"text-gray-600 mb-4",children:e.content}),i.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:e.tags.map(((e,a)=>i.jsx(r.span,{className:"inline-block bg-pink-50 text-gray-600 text-sm px-3 py-1 rounded-full",whileHover:{backgroundColor:"rgba(254, 124, 171, 0.2)",scale:1.05},children:e},a)))}),i.jsx("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:i.jsxs("div",{className:"flex items-center space-x-6",children:[i.jsxs(r.button,{className:"flex items-center space-x-1 text-gray-500 hover:text-dexin-primary",whileHover:{scale:1.1},whileTap:{scale:.95},children:[i.jsx(o,{size:18}),i.jsx("span",{children:e.likes||""})]}),i.jsxs(r.button,{className:"flex items-center space-x-1 text-gray-500 hover:text-dexin-primary",whileHover:{scale:1.1},whileTap:{scale:.95},children:[i.jsx(x,{size:18}),i.jsx("span",{children:e.comments||""})]}),i.jsxs(r.button,{className:"flex items-center space-x-1 text-gray-500 hover:text-dexin-primary",whileHover:{scale:1.1},whileTap:{scale:.95},children:[i.jsx(c,{size:18}),i.jsx("span",{children:e.views||""})]})]})})]})},e.id)))})]}),i.jsxs("div",{className:"w-full lg:w-1/4 space-y-4 sm:space-y-6",children:[i.jsxs(r.div,{className:"bg-white rounded-xl shadow-sm overflow-hidden border border-pink-100 p-3 sm:p-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},whileHover:{boxShadow:"0 8px 16px rgba(254, 124, 171, 0.15)"},children:[i.jsx("h3",{className:"font-bold text-lg sm:text-xl text-[#de1e54] mb-4 sm:mb-6",children:"Bảng Xếp Hạng"}),i.jsx("div",{className:"space-y-0",children:g.map(((e,a)=>i.jsx(r.div,{className:`py-2 sm:py-3 ${b(a)}`,initial:{opacity:0,x:10},animate:{opacity:1,x:0},transition:{duration:.3,delay:.2+.03*a},whileHover:{backgroundColor:"rgba(254, 124, 171, 0.05)",x:5},children:i.jsxs("div",{className:"flex items-center justify-between px-1 sm:px-2 min-h-[2.5rem]",children:[i.jsxs("div",{className:"flex items-center flex-1 min-w-0 mr-2",children:[i.jsx("span",{className:"w-4 sm:w-6 text-center font-bold text-base sm:text-xl flex-shrink-0 "+(0===a?"text-dexin-gold":1===a?"text-dexin-silver":2===a?"text-dexin-bronze":"text-gray-500"),children:a+1}),v(a)&&i.jsx("span",{className:"ml-1 mr-1 sm:mr-2 flex-shrink-0",children:v(a)}),i.jsx(r.img,{src:e.avatar,alt:e.user,className:"w-6 h-6 sm:w-8 sm:h-8 rounded-full mx-1 sm:mx-2 flex-shrink-0",whileHover:{scale:1.15}}),i.jsx("span",{className:`${w(a)} truncate text-sm sm:text-base`,children:e.user})]}),i.jsx("div",{className:"flex items-center flex-shrink-0",children:i.jsxs("span",{className:`${j(a)} text-xs sm:text-sm whitespace-nowrap`,children:[i.jsxs("span",{className:"hidden sm:inline",children:[e.likes.toLocaleString()," Lượt Thích"]}),i.jsx("span",{className:"sm:hidden",children:e.likes.toLocaleString()})]})})]})},e.id)))})]}),i.jsxs(r.div,{className:"bg-white rounded-xl shadow-sm overflow-hidden border border-dexin-light-20 p-3 sm:p-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},whileHover:{boxShadow:"0 8px 16px rgba(254, 124, 171, 0.15)"},children:[i.jsxs("div",{className:"flex items-center mb-3 sm:mb-4",children:[i.jsx(r.div,{whileHover:{scale:1.1},className:"mr-2 flex-shrink-0",children:i.jsx(c,{className:"w-4 h-4 sm:w-5 sm:h-5 text-dexin-primary"})}),i.jsx("h3",{className:"font-bold text-lg sm:text-xl text-dexin-primary",children:"Hashtag"})]}),i.jsx("div",{className:"flex flex-wrap gap-1.5 sm:gap-2",children:u.map(((e,a)=>i.jsx(r.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.3,delay:.2+.04*a},className:"flex-shrink-0",children:i.jsx(d,{to:`/chung-nhip/tag/${e.replace("#","")}`,className:"inline-block bg-pink-50 text-gray-600 hover:bg-pink-100 hover:text-dexin-primary text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-full transition-colors duration-300 whitespace-nowrap",children:i.jsx(r.span,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"block",children:e})})},a)))})]})]})]})})]})]})};export{y as default};
