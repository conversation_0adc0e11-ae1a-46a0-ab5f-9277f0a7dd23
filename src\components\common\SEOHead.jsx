// React 19 native metadata support - no external dependencies needed
const SEOHead = ({
  title = 'DEXIN - Nền tảng thiết kế nội thất với AI',
  description = 'DEXIN là nền tảng thiết kế nội thất thông minh với AI, cung cấp công cụ thiết kế <PERSON>, cử<PERSON> hàng nội thất và tư vấn chuyên nghiệp. Tạo không gian sống hoàn hảo với DEXIN.',
  keywords = 'thiết kế nội thất, <PERSON>, nộ<PERSON> thất, thiết kế 2D, cử<PERSON> hàng nội thất, trang trí nh<PERSON>, DEXIN, furniture, interior design',
  image = '/images/Logo DEXIN finall 2.png',
  url = 'https://dexin.io.vn',
  type = 'website',
  author = 'DEXIN Team',
  locale = 'vi_VN',
  siteName = 'DEXIN'
}) => {
  const fullTitle = title.includes('DEXIN') ? title : `${title} | DEXIN`;
  const fullUrl = url.startsWith('http') ? url : `https://dexin.io.vn${url}`;
  const fullImage = image.startsWith('http') ? image : `https://dexin.io.vn${image}`;

  // React 19 native metadata support - these tags are automatically moved to <head>
  return (
    <>
      {/* Document title */}
      <title>{fullTitle}</title>

      {/* Basic meta tags */}
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow" />
      <meta name="language" content="Vietnamese" />
      <meta name="revisit-after" content="7 days" />

      {/* Open Graph meta tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImage} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />

      {/* Twitter Card meta tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImage} />
      <meta name="twitter:url" content={fullUrl} />

      {/* Theme colors */}
      <meta name="theme-color" content="#FF6B93" />
      <meta name="msapplication-TileColor" content="#FF6B93" />

      {/* Favicon - Multiple sizes for better Google Search compatibility */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" sizes="16x16 32x32 48x48 64x64"/>
      <link rel="icon" type="image/png" href="/images/Icon.png" sizes="32x32"/>
      <link rel="icon" type="image/png" href="/images/Icon.png" sizes="16x16"/>
      <link rel="icon" type="image/png" href="/images/Icon.png" sizes="192x192"/>
      <link rel="icon" type="image/png" href="/images/Icon.png" sizes="512x512"/>
      <link rel="shortcut icon" href="/favicon.ico"/>

      {/* Canonical link */}
      <link rel="canonical" href={fullUrl} />

      {/* Structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "DEXIN",
            "description": description,
            "url": "https://dexin.io.vn",
            "logo": "https://dexin.io.vn/images/Logo DEXIN finall 2.png",
            "contactPoint": {
              "@type": "ContactPoint",
              "contactType": "customer service",
              "availableLanguage": "Vietnamese"
            },
            "sameAs": [
              "https://facebook.com/dexin",
              "https://instagram.com/dexin"
            ]
          })
        }}
      />
    </>
  );
};

export default SEOHead;
