import React, { useState, useCallback, Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, ShoppingCart, Plus, Minus } from 'lucide-react';
import { motion } from 'motion/react';

// Hàm định dạng giá tiền
const formatPrice = (price) => {
  return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
};

const ProductVariantModal = ({ 
  isOpen, 
  onClose, 
  product, 
  onAddToCart 
}) => {
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [quantity, setQuantity] = useState(1);

  // Reset state khi modal mở
  React.useEffect(() => {
    if (isOpen && product?.variants?.length > 0) {
      setSelectedVariant(product.variants[0].id);
      setQuantity(1);
    }
  }, [isOpen, product]);

  // Xử lý tăng số lượng
  const increaseQuantity = useCallback(() => {
    setQuantity(prev => prev + 1);
  }, []);

  // Xử lý giảm số lượng
  const decreaseQuantity = useCallback(() => {
    setQuantity(prev => (prev > 1 ? prev - 1 : 1));
  }, []);

  // Xử lý thêm vào giỏ hàng
  const handleAddToCart = useCallback(() => {
    if (!product || !selectedVariant) return;

    const selectedVariantInfo = product.variants.find(v => v.id === selectedVariant);
    
    const cartItem = {
      ...product,
      quantity: quantity,
      variantId: selectedVariant,
      variantName: selectedVariantInfo?.name || '',
      variantColor: selectedVariantInfo?.color || ''
    };

    onAddToCart(cartItem);
    onClose();
  }, [product, selectedVariant, quantity, onAddToCart, onClose]);

  if (!product) return null;

  return (
    <Transition appear show={isOpen}>
      <Dialog as="div" className="fixed inset-0 z-50 overflow-y-auto" onClose={onClose}>
        <div className="min-h-screen px-4 flex items-center justify-center">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" aria-hidden="true" />
          </Transition.Child>

          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 scale-95"
            enterTo="opacity-100 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 scale-100"
            leaveTo="opacity-0 scale-95"
          >
            <Dialog.Panel className="relative w-full max-w-lg mx-auto bg-white rounded-2xl shadow-xl p-6">
              {/* Nút đóng */}
              <button 
                onClick={onClose} 
                className="absolute top-4 right-4 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>

              {/* Tiêu đề */}
              <Dialog.Title className="text-xl font-bold text-gray-800 mb-6 pr-8">
                Chọn thuộc tính sản phẩm
              </Dialog.Title>

              {/* Thông tin sản phẩm */}
              <div className="flex gap-4 mb-6">
                <div className="w-20 h-20 bg-gray-50 rounded-lg flex items-center justify-center">
                  <img 
                    src={product.image} 
                    alt={product.name} 
                    className="max-h-16 max-w-16 object-contain" 
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-800 mb-1">{product.name}</h3>
                  <p className="text-2xl font-bold text-dexin-primary">{formatPrice(product.price)}</p>
                </div>
              </div>

              {/* Chọn màu sắc */}
              {product.variants && product.variants.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-base font-semibold text-gray-800 mb-3">
                    Chọn màu sắc <span className="text-red-500">*</span>
                  </h4>
                  <div className="flex gap-3">
                    {product.variants.map((variant) => (
                      <motion.button
                        key={variant.id}
                        className={`relative w-12 h-12 rounded-full border-2 ${
                          selectedVariant === variant.id 
                            ? 'border-dexin-primary ring-2 ring-dexin-primary ring-offset-2' 
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                        style={{ backgroundColor: variant.color }}
                        onClick={() => setSelectedVariant(variant.id)}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        title={variant.name}
                      >
                        {selectedVariant === variant.id && (
                          <div className="absolute inset-0 rounded-full flex items-center justify-center">
                            <div className="w-2 h-2 bg-white rounded-full shadow-md"></div>
                          </div>
                        )}
                      </motion.button>
                    ))}
                  </div>
                  {selectedVariant && (
                    <p className="text-sm text-gray-600 mt-2">
                      Đã chọn: {product.variants.find(v => v.id === selectedVariant)?.name}
                    </p>
                  )}
                </div>
              )}

              {/* Chọn số lượng */}
              <div className="mb-6">
                <h4 className="text-base font-semibold text-gray-800 mb-3">Số lượng</h4>
                <div className="inline-flex items-center border border-gray-300 rounded-lg overflow-hidden">
                  <button
                    onClick={decreaseQuantity}
                    className="p-2 hover:bg-gray-100 transition-colors"
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                  <div className="px-4 py-2 bg-white border-x border-gray-300 min-w-[3rem] text-center">
                    {quantity}
                  </div>
                  <button
                    onClick={increaseQuantity}
                    className="p-2 hover:bg-gray-100 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Nút hành động */}
              <div className="flex gap-3">
                <button
                  onClick={onClose}
                  className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Hủy
                </button>
                <button
                  onClick={handleAddToCart}
                  disabled={!selectedVariant}
                  className={`flex-1 py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2 ${
                    selectedVariant
                      ? 'bg-dexin-light text-white hover:bg-pink-600'
                      : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  <ShoppingCart className="w-5 h-5" />
                  Thêm vào giỏ hàng
                </button>
              </div>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition>
  );
};

export default ProductVariantModal; 