# Hướng dẫn Setup Vercel Web Analytics

## 🚀 Đã được Fix

### Vấn đề đã được giải quyết:
1. ✅ **Lỗi function name mismatch** - Đ<PERSON> sửa `isProductionEnvironment()` thành `isVercelEnvironment()`
2. ✅ **Cải thiện logic detection environment** - Thêm nhiều environment variables check
3. ✅ **Tạo component Analytics chuyên biệt** với custom events tracking
4. ✅ **Tạo useAnalytics hook** để sử dụng tracking functions dễ dàng hơn

## 📋 Checklist Setup Vercel Web Analytics

### 1. Enable Web Analytics trong Vercel Dashboard
1. T<PERSON>y cập [Vercel Dashboard](https://vercel.com/dashboard)
2. Chọn project của bạn
3. Vào tab **Analytics** 
4. Click **Enable Web Analytics**
5. Chờ 24-48 giờ để dữ liệu bắt đầu hiển thị

### 2. Verify Setup
- ✅ Package `@vercel/analytics` đã được cài: v1.5.0
- ✅ Component `<Analytics />` đã được thêm vào App.jsx
- ✅ Environment detection logic đã được cải thiện
- ✅ Custom events tracking đã được setup

### 3. Kiểm tra Environment Variables (Optional)
Thêm vào `.env.local` nếu cần:
```bash
VITE_VERCEL_ENV=production
```

## 🔧 Troubleshooting

### Vấn đề 1: Analytics không hiển thị data
**Nguyên nhân có thể:**
- Web Analytics chưa được enable trong Vercel Dashboard
- Cần chờ 24-48 giờ sau khi enable
- Project chưa được deploy lên Vercel

**Giải pháp:**
1. Kiểm tra Vercel Dashboard > Project > Analytics
2. Đảm bảo đã deploy project lên Vercel
3. Chờ ít nhất 24 giờ sau khi enable

### Vấn đề 2: Local development không track
**Điều này là bình thường!** 
- Analytics chỉ hoạt động trên production (Vercel)
- Local development sẽ không track để tránh dữ liệu noise

### Vấn đề 3: Domain tùy chỉnh không track
Đảm bảo domain được cấu hình đúng trong Vercel:
1. Vào Settings > Domains
2. Thêm domain tùy chỉnh
3. Cập nhật DNS records

## 📊 Cách sử dụng Custom Events

### Trong Component:
```jsx
import { useAnalytics } from '../hooks';

const ProductCard = ({ product }) => {
  const { trackAddToCart, trackAddToWishlist } = useAnalytics();

  const handleAddToCart = () => {
    // Logic thêm vào cart
    trackAddToCart(product);
  };

  const handleAddToWishlist = () => {
    // Logic thêm vào wishlist
    trackAddToWishlist(product);
  };

  return (
    // JSX component
  );
};
```

### Các events có sẵn:
- `trackAddToCart(product)` - Track thêm vào giỏ hàng
- `trackAddToWishlist(product)` - Track thêm vào wishlist
- `trackSignUp(method)` - Track đăng ký
- `trackSignIn(method)` - Track đăng nhập
- `trackPurchase(orderData)` - Track mua hàng
- `trackChatInteraction(messageType)` - Track chat với AI
- `trackDesignTool(action, toolType)` - Track sử dụng design tools
- `trackSearch(query, category)` - Track tìm kiếm

## 📈 Xem Analytics Data

1. Truy cập [Vercel Dashboard](https://vercel.com/dashboard)
2. Chọn project
3. Tab **Analytics** > **Web Analytics**
4. Xem:
   - **Visitors**: Unique visitors theo ngày
   - **Page Views**: Lượt xem trang
   - **Bounce Rate**: Tỷ lệ thoát
   - **Top Pages**: Trang được xem nhiều nhất
   - **Referrers**: Nguồn traffic
   - **Countries**: Quốc gia của visitors
   - **Devices**: Thiết bị sử dụng
   - **Custom Events**: Events bạn đã track

## 🔒 Privacy & GDPR

Vercel Web Analytics:
- ✅ **Privacy-friendly**: Không sử dụng cookies
- ✅ **Anonymized data**: Không track cá nhân
- ✅ **GDPR compliant**: Tuân thủ quy định bảo mật
- ✅ **No third-party**: Tích hợp sẵn trong platform Vercel

## 📝 Notes

- Data retention: 7 ngày (Hobby plan), 30 ngày (Pro plan)
- Real-time data: Cập nhật mỗi vài phút
- Export data: Có thể export CSV (Pro plan)
- Custom events: Unlimited tracking

## 🆘 Hỗ trợ

Nếu vẫn gặp vấn đề:
1. Kiểm tra [Vercel Analytics Documentation](https://vercel.com/docs/analytics)
2. Liên hệ Vercel Support
3. Kiểm tra Vercel Status Page

---

**Lưu ý:** Web Analytics của Vercel khác với Google Analytics. Nó được thiết kế để privacy-friendly và tích hợp sẵn với platform Vercel. 