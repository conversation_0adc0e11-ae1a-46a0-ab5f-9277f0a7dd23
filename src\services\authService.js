import apiClient from '../config/api';

// Helper function để decode JWT token và lấy thông tin user
const decodeJWTToken = (token) => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return null;
  }
};

// Helper function để chuẩn hóa user data response từ JWT token
const formatUserFromToken = (token) => {
  const decoded = decodeJWTToken(token);
  if (!decoded) return null;

  return {
    id: decoded.UserId || decoded.sub || decoded.userId || decoded.id,
    firstName: decoded.firstName || decoded.given_name || '',
    lastName: decoded.lastName || decoded.family_name || '',
    userName: decoded.UserName || decoded.userName || decoded.username || decoded.preferred_username || '',
    email: decoded.email || '',
    phone: decoded.phone || decoded.phone_number || '',
    gender: decoded.gender || '',
    avatar: decoded.avatar || decoded.picture || '',
    role: decoded.Role || decoded.role || decoded.roles?.[0] || 'user',
    token: token
  };
};

// Helper function để validate email
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Helper function để validate phone
const isValidPhone = (phone) => {
  if (!phone) return true; // Phone is optional
  const phoneRegex = /^[0-9+\-\s()]{10,}$/;
  return phoneRegex.test(phone);
};

// Utility function để tạo cache key
const createCacheKey = (type, value) => `${type}_${String(value).toLowerCase()}`;

// Simple in-memory cache để tránh repeated API calls
const userCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

// Cache utility functions
const setCacheItem = (key, data) => {
  userCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl: CACHE_TTL
  });
};

const getCacheItem = (key) => {
  const cached = userCache.get(key);
  if (!cached) return null;
  
  if (Date.now() - cached.timestamp > cached.ttl) {
    userCache.delete(key);
    return null;
  }
  
  return cached.data;
};

// Service để xử lý authentication với API mới (Bearer token)
export const authService = {
  // Đăng ký user mới
  async register(userData) {
    // Input validation
    if (!userData.firstName?.trim()) {
      return { success: false, message: 'Họ không được để trống' };
    }
    if (!userData.lastName?.trim()) {
      return { success: false, message: 'Tên không được để trống' };
    }
    if (!userData.username?.trim()) {
      return { success: false, message: 'Tên người dùng không được để trống' };
    }
    if (!userData.email?.trim() || !isValidEmail(userData.email)) {
      return { success: false, message: 'Email không hợp lệ' };
    }
    if (!userData.password || userData.password.length < 6) {
      return { success: false, message: 'Mật khẩu phải có ít nhất 6 ký tự' };
    }
    if (userData.phone && !isValidPhone(userData.phone)) {
      return { success: false, message: 'Số điện thoại không hợp lệ' };
    }

    try {
      // Prepare request data theo format API mới
      const requestData = {
        firstName: userData.firstName.trim(),
        lastName: userData.lastName.trim(),
        username: userData.username.trim(), // Sử dụng 'username' thay vì 'userName'
        email: userData.email.trim().toLowerCase(),
        password: userData.password
      };

      const response = await apiClient.post('/SystemUserAccounts/register', requestData);

      // API mới trả về message "đăng ký thành công" thay vì user object
      // Tạo user object từ request data để maintain compatibility với UI
      const user = {
        id: Date.now(), // Temporary ID
        firstName: requestData.firstName,
        lastName: requestData.lastName,
        userName: requestData.username,
        email: requestData.email,
        phone: '',
        gender: '',
        avatar: '',
        role: 'user'
      };

      return {
        success: true,
        user: user,
        message: response.data || 'Đăng ký thành công!'
      };
    } catch (error) {
      console.error('Registration error:', error);

      if (error.response?.status === 400) {
        return { success: false, message: 'Dữ liệu đăng ký không hợp lệ' };
      }
      if (error.response?.status === 409) {
        return { success: false, message: 'Tên người dùng hoặc email đã tồn tại' };
      }

      return {
        success: false,
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi đăng ký'
      };
    }
  },

  // LOGIN - Sử dụng API mới với Bearer token
  async login(loginData) {
    // Input validation
    if (!loginData.identifier?.trim()) {
      return { success: false, message: 'Tên người dùng không được để trống' };
    }
    if (!loginData.password) {
      return { success: false, message: 'Mật khẩu không được để trống' };
    }

    try {
      // Prepare request data theo format API mới
      const requestData = {
        userName: loginData.identifier.trim(), // API mới sử dụng userName
        password: loginData.password
      };

      const response = await apiClient.post('/SystemUserAccounts/login', requestData);

      // API mới trả về Bearer token
      const token = response.data;

      if (!token) {
        return {
          success: false,
          message: 'Không nhận được token từ server'
        };
      }

      // Decode JWT token để lấy thông tin user
      const user = formatUserFromToken(token);

      if (!user) {
        return {
          success: false,
          message: 'Token không hợp lệ'
        };
      }

      console.log('✅ Login successful');
      return {
        success: true,
        user: user,
        message: 'Đăng nhập thành công!'
      };
    } catch (error) {
      console.error('Login error:', error);

      // Handle specific API errors
      if (error.response?.status === 401) {
        return {
          success: false,
          message: 'Tên người dùng hoặc mật khẩu không đúng'
        };
      }
      if (error.response?.status === 404) {
        return {
          success: false,
          message: 'Tên người dùng không tồn tại'
        };
      }

      return {
        success: false,
        message: error.response?.data?.message || 'Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại sau.'
      };
    }
  },



  // Update user - sử dụng API
  async updateUser(userId, userData) {
    if (!userId) {
      return { success: false, message: 'ID người dùng không hợp lệ' };
    }
    
    // Validation
    if (userData.firstName !== undefined && !userData.firstName?.trim()) {
      return { success: false, message: 'Họ không được để trống' };
    }
    if (userData.lastName !== undefined && !userData.lastName?.trim()) {
      return { success: false, message: 'Tên không được để trống' };
    }
    if (userData.email !== undefined && (!userData.email?.trim() || !isValidEmail(userData.email))) {
      return { success: false, message: 'Email không hợp lệ' };
    }
    if (userData.phone !== undefined && userData.phone && !isValidPhone(userData.phone)) {
      return { success: false, message: 'Số điện thoại không hợp lệ' };
    }

    try {
      // Prepare update data
      const updateData = { ...userData };
      if (updateData.firstName) updateData.firstName = updateData.firstName.trim();
      if (updateData.lastName) updateData.lastName = updateData.lastName.trim();
      if (updateData.email) updateData.email = updateData.email.toLowerCase().trim();
      if (updateData.phone) updateData.phone = updateData.phone.trim();

      const response = await apiClient.put(`/auth/user/${userId}`, updateData);
      
      // Update cache with new data
      const updatedUser = response.data;
      setCacheItem(createCacheKey('email', updatedUser.email), updatedUser);
      setCacheItem(createCacheKey('username', updatedUser.userName), updatedUser);
      setCacheItem(createCacheKey('id', updatedUser.id), updatedUser);

      return {
        success: true,
        user: response.data,
        message: 'Cập nhật thông tin thành công!'
      };
    } catch (error) {
      console.error('Update user error:', error);
      
      if (error.response?.status === 404) {
        return { success: false, message: 'Không tìm thấy người dùng' };
      }
      if (error.response?.status === 422) {
        return { success: false, message: 'Dữ liệu cập nhật không hợp lệ' };
      }
      
      return {
        success: false,
        message: error.message || 'Có lỗi xảy ra khi cập nhật thông tin'
      };
    }
  },

  // Get user detail by ID từ API mới
  async getUserDetailById(userId) {
    if (!userId) {
      return { success: false, message: 'ID người dùng không hợp lệ' };
    }

    try {
      // Check cache first
      const cacheKey = createCacheKey('detail', userId);
      const cachedUser = getCacheItem(cacheKey);

      if (cachedUser) {
        return {
          success: true,
          user: cachedUser
        };
      }

      // API call để lấy thông tin chi tiết user
      const response = await apiClient.get(`/SystemUserAccounts/get-by-id/${userId}`);

      if (response.data.success && response.data.data) {
        const userData = response.data.data;

        // Format user data theo cấu trúc cũ để tương thích
        const formattedUser = {
          id: userData.userAccountId,
          firstName: userData.firstName,
          lastName: userData.lastName,
          userName: userData.userName,
          email: userData.email,
          phone: userData.phone || '',
          gender: userData.gender,
          avatar: userData.avartar || '', // Note: API trả về "avartar" (typo)
          role: userData.role,
          isActive: userData.isActive
        };

        // Cache the user detail
        setCacheItem(cacheKey, formattedUser);

        return {
          success: true,
          user: formattedUser,
          message: response.data.message
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Không thể lấy thông tin người dùng'
        };
      }
    } catch (error) {
      console.error('Get user detail error:', error);

      if (error.response?.status === 404) {
        return { success: false, message: 'Không tìm thấy người dùng' };
      }

      return {
        success: false,
        message: error.response?.data?.message || error.message || 'Không thể lấy thông tin người dùng'
      };
    }
  },

  // Get user by ID (legacy method - giữ lại để tương thích)
  async getUserById(userId) {
    // Sử dụng method mới để lấy thông tin chi tiết
    return await this.getUserDetailById(userId);
  },

  // Change password
  async changePassword(userId, currentPassword, newPassword) {
    if (!userId) {
      return { success: false, message: 'ID người dùng không hợp lệ' };
    }
    if (!currentPassword) {
      return { success: false, message: 'Mật khẩu hiện tại không được để trống' };
    }
    if (!newPassword || newPassword.length < 6) {
      return { success: false, message: 'Mật khẩu mới phải có ít nhất 6 ký tự' };
    }
    if (currentPassword === newPassword) {
      return { success: false, message: 'Mật khẩu mới phải khác mật khẩu hiện tại' };
    }

    try {
      // Get current user to verify password
      const userResponse = await this.getUserById(userId);
      if (!userResponse.success) {
        return userResponse;
      }

      // Get full user data to verify current password
      const userDataResponse = await apiClient.get(`/auth/user/${userId}`);
      const user = userDataResponse.data;

      if (user.password !== currentPassword) {
        return {
          success: false,
          message: 'Mật khẩu hiện tại không đúng'
        };
      }

      // Update password
      await apiClient.put(`/auth/user/${userId}`, {
        ...user,
        password: newPassword
      });

      // Update cache with new password
      const updatedUser = { ...user, password: newPassword };
      setCacheItem(createCacheKey('email', updatedUser.email), updatedUser);
      setCacheItem(createCacheKey('username', updatedUser.userName), updatedUser);
      setCacheItem(createCacheKey('id', updatedUser.id), updatedUser);

      return {
        success: true,
        message: 'Đổi mật khẩu thành công!'
      };
    } catch (error) {
      console.error('Change password error:', error);
      
      if (error.response?.status === 404) {
        return { success: false, message: 'Không tìm thấy người dùng' };
      }
      
      return {
        success: false,
        message: error.message || 'Có lỗi xảy ra khi đổi mật khẩu'
      };
    }
  },

  // Upload avatar
  async uploadAvatar(userId, avatarFile) {
    if (!userId) {
      return { success: false, message: 'ID người dùng không hợp lệ' };
    }

    if (!avatarFile) {
      return { success: false, message: 'Vui lòng chọn file ảnh' };
    }

    try {
      // Tạo FormData cho upload
      const formData = new FormData();
      formData.append('avatar', avatarFile);

      const response = await apiClient.put(`/SystemUserAccounts/update-avatar/${userId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Clear tất cả cache liên quan đến user này để force refresh
      try {
        const userIdStr = String(userId);
        const cacheKeys = [
          createCacheKey('detail', userIdStr),
          createCacheKey('id', userIdStr),
          createCacheKey('email', ''), // Sẽ clear sau khi có email
          createCacheKey('username', '') // Sẽ clear sau khi có username
        ];

        // Clear cache keys
        cacheKeys.forEach(key => {
          if (userCache.has(key)) {
            userCache.delete(key);
          }
        });

        // Clear all cache để đảm bảo
        userCache.clear();

        console.log('✅ Cache cleared after avatar upload');
      } catch (cacheError) {
        console.warn('Cache clear error:', cacheError);
      }

      return {
        success: true,
        message: 'Cập nhật avatar thành công!',
        data: response.data
      };
    } catch (error) {
      console.error('Upload avatar error:', error);

      if (error.response?.status === 400) {
        return { success: false, message: 'File ảnh không hợp lệ' };
      }
      if (error.response?.status === 413) {
        return { success: false, message: 'File quá lớn. Vui lòng chọn file nhỏ hơn' };
      }

      return {
        success: false,
        message: error.response?.data?.message || error.message || 'Có lỗi xảy ra khi upload avatar'
      };
    }
  },

  // Logout với API
  async logout() {
    try {
      // Gọi API logout
      await apiClient.post('/SystemUserAccounts/logout');

      // Clear cache
      this.clearCache();

      return {
        success: true,
        message: 'Đăng xuất thành công!'
      };
    } catch (error) {
      console.error('Logout API error:', error);

      // Vẫn clear cache local dù API lỗi
      this.clearCache();

      return {
        success: true, // Vẫn return success để UI có thể logout
        message: 'Đăng xuất thành công!'
      };
    }
  },

  // Clear cache
  clearCache() {
    userCache.clear();
    console.log('🗑️ User cache cleared');
  }
};