import React, { useState } from 'react';
import { motion } from 'motion/react';
import { RefreshCw, MessageCircle, AlertTriangle, CheckCircle } from 'lucide-react';
import { useChat } from '../../context/ChatContext';

/**
 * Component test cho tính năng giới hạn tin nhắn
 * Sử dụng để kiểm tra và demo tính năng giới hạn 15 lần hỏi
 */
const MessageLimitTest = () => {
  const { messageCount, isLimitReached, messageLimit, sendMessage, isLoading } = useChat();
  const [testResults, setTestResults] = useState([]);
  const [isRunningTest, setIsRunningTest] = useState(false);

  // Reset localStorage để test lại từ đầu
  const resetMessageCount = () => {
    localStorage.removeItem('dexin_message_count');
    localStorage.removeItem('dexin_message_date');
    window.location.reload();
  };

  // Set message count để test các trạng thái khác nhau
  const setTestMessageCount = (count) => {
    const today = new Date().toDateString();
    localStorage.setItem('dexin_message_count', count.toString());
    localStorage.setItem('dexin_message_date', today);
    window.location.reload();
  };

  // Test gửi tin nhắn để kiểm tra counter
  const testSendMessage = async () => {
    if (isRunningTest) return;
    
    setIsRunningTest(true);
    const testMessage = `Test message #${messageCount + 1} - ${new Date().toLocaleTimeString()}`;
    
    try {
      const result = await sendMessage(testMessage);
      
      setTestResults(prev => [...prev, {
        id: Date.now(),
        messageNumber: messageCount,
        message: testMessage,
        success: !!result,
        isLimitMessage: result?.content?.includes('hết lượt hỏi'),
        timestamp: new Date().toLocaleTimeString()
      }]);
    } catch (error) {
      setTestResults(prev => [...prev, {
        id: Date.now(),
        messageNumber: messageCount,
        message: testMessage,
        success: false,
        error: error.message,
        timestamp: new Date().toLocaleTimeString()
      }]);
    } finally {
      setIsRunningTest(false);
    }
  };

  // Auto test - gửi nhiều tin nhắn liên tiếp
  const runAutoTest = async () => {
    if (isRunningTest || isLimitReached) return;
    
    setIsRunningTest(true);
    const remainingMessages = messageLimit - messageCount;
    
    for (let i = 0; i < remainingMessages + 2; i++) {
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // Delay 1s giữa các tin nhắn
      }
      
      const testMessage = `Auto test message #${messageCount + i + 1}`;
      
      try {
        const result = await sendMessage(testMessage);
        
        setTestResults(prev => [...prev, {
          id: Date.now() + i,
          messageNumber: messageCount + i,
          message: testMessage,
          success: !!result,
          isLimitMessage: result?.content?.includes('hết lượt hỏi'),
          timestamp: new Date().toLocaleTimeString(),
          isAutoTest: true
        }]);
        
        // Dừng nếu đã đạt giới hạn
        if (result?.content?.includes('hết lượt hỏi')) {
          break;
        }
      } catch (error) {
        setTestResults(prev => [...prev, {
          id: Date.now() + i,
          messageNumber: messageCount + i,
          message: testMessage,
          success: false,
          error: error.message,
          timestamp: new Date().toLocaleTimeString(),
          isAutoTest: true
        }]);
        break;
      }
    }
    
    setIsRunningTest(false);
  };

  // Clear test results
  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">
        🧪 Test Giới hạn Tin nhắn (15 lần/ngày)
      </h2>

      {/* Current Status */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Trạng thái hiện tại</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <MessageCircle className="w-5 h-5 text-blue-500" />
            <span className="text-sm">
              <strong>Đã gửi:</strong> {messageCount}/{messageLimit}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            {isLimitReached ? (
              <>
                <AlertTriangle className="w-5 h-5 text-red-500" />
                <span className="text-sm text-red-600">
                  <strong>Trạng thái:</strong> Đã đạt giới hạn
                </span>
              </>
            ) : (
              <>
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span className="text-sm text-green-600">
                  <strong>Trạng thái:</strong> Còn {messageLimit - messageCount} lượt
                </span>
              </>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-5 h-5 text-gray-500" />
            <span className="text-sm text-gray-600">
              <strong>Reset:</strong> 00:00 ngày mai
            </span>
          </div>
        </div>
      </div>

      {/* Test Controls */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Điều khiển Test</h3>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={testSendMessage}
            disabled={isRunningTest || isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunningTest ? 'Đang gửi...' : 'Gửi 1 tin nhắn test'}
          </button>
          
          <button
            onClick={runAutoTest}
            disabled={isRunningTest || isLoading || isLimitReached}
            className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunningTest ? 'Đang chạy...' : 'Auto test đến giới hạn'}
          </button>
          
          <button
            onClick={resetMessageCount}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
          >
            Reset về 0
          </button>
          
          <button
            onClick={() => setTestMessageCount(13)}
            className="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600"
          >
            Set 13 (gần giới hạn)
          </button>
          
          <button
            onClick={() => setTestMessageCount(15)}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
          >
            Set 15 (đạt giới hạn)
          </button>
        </div>
      </div>

      {/* Test Results */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold">Kết quả Test</h3>
          {testResults.length > 0 && (
            <button
              onClick={clearResults}
              className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Xóa kết quả
            </button>
          )}
        </div>

        {testResults.length === 0 ? (
          <p className="text-gray-500 italic">Chưa có kết quả test nào</p>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {testResults.map((result) => (
              <motion.div
                key={result.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`p-3 rounded-lg border-l-4 ${
                  result.isLimitMessage
                    ? 'bg-red-50 border-red-500'
                    : result.success
                    ? 'bg-green-50 border-green-500'
                    : 'bg-red-50 border-red-500'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded ${
                      result.isLimitMessage
                        ? 'bg-red-200 text-red-800'
                        : result.success
                        ? 'bg-green-200 text-green-800'
                        : 'bg-red-200 text-red-800'
                    }`}>
                      {result.isLimitMessage ? 'LIMIT REACHED' : result.success ? 'SUCCESS' : 'ERROR'}
                    </span>
                    {result.isAutoTest && (
                      <span className="px-2 py-1 text-xs bg-purple-200 text-purple-800 rounded">
                        AUTO TEST
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-gray-500">{result.timestamp}</span>
                </div>
                
                <div className="text-sm space-y-1">
                  <p><strong>Message #{result.messageNumber + 1}:</strong> {result.message}</p>
                  {result.error && (
                    <p className="text-red-600"><strong>Error:</strong> {result.error}</p>
                  )}
                  {result.isLimitMessage && (
                    <p className="text-red-600"><strong>Limit Message:</strong> Đã hiển thị thông báo giới hạn</p>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">Hướng dẫn Test:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• <strong>Gửi 1 tin nhắn test:</strong> Gửi 1 tin nhắn và kiểm tra counter tăng</li>
          <li>• <strong>Auto test:</strong> Tự động gửi tin nhắn đến khi đạt giới hạn</li>
          <li>• <strong>Reset về 0:</strong> Xóa localStorage và reload trang</li>
          <li>• <strong>Set 13:</strong> Thiết lập counter gần giới hạn để test warning</li>
          <li>• <strong>Set 15:</strong> Thiết lập counter đạt giới hạn để test block</li>
        </ul>
      </div>
    </div>
  );
};

export default MessageLimitTest;
