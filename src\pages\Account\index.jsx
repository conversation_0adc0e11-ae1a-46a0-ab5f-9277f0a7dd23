import React, { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { Menu } from 'lucide-react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import SEOHead from '../../components/common/SEOHead';

// Import các components con
import AccountSidebar from './components/AccountSidebar';
import Profile from './profile';
import AddressSection from './components/AddressSection';
import OrderHistory from './components/OrderHistory';
import ProductDesign from './components/ProductDesign';

const Account = () => {
  const { loading, refreshUser, user } = useAuth();
  const location = useLocation();
  const [activeSection, setActiveSection] = useState('profile');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // Refresh user detail khi vào trang Account
  useEffect(() => {
    const loadUserDetail = async () => {
      if (user?.id) {
        try {
          await refreshUser();
        } catch (error) {
          console.warn('Could not refresh user details:', error);
        }
      }
    };

    loadUserDetail();
  }, [user?.id, refreshUser]);

  // Lắng nghe state từ navigation để chuyển đến section tương ứng
  useEffect(() => {
    if (location.state?.activeSection) {
      setActiveSection(location.state.activeSection);
      // Clear state sau khi đã sử dụng
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  // Render component tương ứng với section được chọn
  const renderContent = () => {
    switch (activeSection) {
      case 'profile':
        return <Profile />;
      case 'address':
        return <AddressSection />;
      case 'orders':
        return <OrderHistory />;
      case 'designs':
        return <ProductDesign />;
      default:
        return <Profile />;
    }
  };

  const getSectionTitle = () => {
    const titles = {
      profile: 'Thông tin cá nhân',
      address: 'Địa chỉ giao hàng',
      orders: 'Lịch sử đơn hàng',
      designs: 'Bản thiết kế'
    };
    return titles[activeSection] || 'Tài khoản';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 flex items-center justify-center">
        <div className="flex items-center space-x-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-dexin-primary"></div>
          <span className="text-gray-600">Đang tải...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHead 
        title={`${getSectionTitle()} - Tài khoản DEXIN`}
        description="Quản lý thông tin tài khoản, địa chỉ, đơn hàng và bản thiết kế trên DEXIN"
        keywords="tài khoản, thông tin cá nhân, đơn hàng, địa chỉ, thiết kế nội thất"
      />
      
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Mobile header */}
          <div className="md:hidden mb-6">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold text-gray-900">{getSectionTitle()}</h1>
              <button
                onClick={() => setIsSidebarOpen(true)}
                className="p-2 bg-white rounded-lg shadow-sm border border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <Menu className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-6">
            {/* Sidebar - Desktop: cố định bên trái, Mobile: overlay */}
            <div className="md:w-80 md:flex-shrink-0">
              <AccountSidebar
                activeSection={activeSection}
                setActiveSection={setActiveSection}
                isSidebarOpen={isSidebarOpen}
                setIsSidebarOpen={setIsSidebarOpen}
              />
            </div>

            {/* Main content area */}
            <div className="flex-1 min-w-0">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="w-full"
              >
                {renderContent()}
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Account;
