import React from 'react';
import { Button } from '../common';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const AIChat = () => {
  return (
    <section className="bg-dexin-light-50 py-10 sm:py-16 md:py-20">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="flex flex-col lg:flex-row gap-8 sm:gap-12 lg:gap-16 items-center">
          <div className="w-full lg:w-1/2 text-center lg:text-left">
            <div className="text-sm text-dexin-dark mb-2 font-medium">Tính năng</div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6 text-dexin-primary font-dexin">
              Khám phá bằng cách<br className="hidden sm:block" />
              tr<PERSON> chuyện với chuyên gia AI
            </h2>
            <p className="text-gray-700 mb-6 sm:mb-8 text-base sm:text-lg">
              Một câu hỏi, một gợi ý – từng bước nhỏ<br className="hidden sm:block" />
              mở ra cánh cửa lớn để bạn khám phá và<br className="hidden sm:block" />
              định hình không gian của chính mình.
            </p>

            <div className="mt-4 sm:mt-8 flex justify-center lg:justify-start">
              <Link to="/mo-loi">
                <Button variant="link" size="lg" className="text-dexin-primary font-semibold text-base sm:text-lg flex items-center hover:underline">
                  Khám phá thêm <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                </Button>
              </Link>
            </div>
          </div>

          <div className="w-full lg:w-1/2 flex justify-center mt-6 lg:mt-0">
            <img
              src="/images/machine-learning.png"
              alt="AI Robot"
              className="w-3/4 sm:w-3/5 lg:w-4/5"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default AIChat;