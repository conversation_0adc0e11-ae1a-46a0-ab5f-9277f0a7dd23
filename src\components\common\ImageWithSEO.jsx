import React, { useState, useRef, useEffect } from 'react';
import { optimizeImageForSEO } from '../../utils/seoUtils';

const ImageWithSEO = ({
  src,
  alt,
  title,
  className = '',
  width,
  height,
  loading = 'lazy',
  placeholder = '/images/placeholder.png',
  category = 'general', // 'product', 'hero', 'avatar', 'general'
  productName = '',
  onLoad,
  onError,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef(null);

  // Intersection Observer để lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Tự động tạo alt text dựa trên category
  const generateAltText = () => {
    if (alt) return alt;
    
    switch (category) {
      case 'product':
        return productName ? 
          `${productName} - Sản phẩm nội thất chất lượng cao tại DEXIN` :
          'Sản phẩm nội thất chất lượng cao tại DEXIN';
      
      case 'hero':
        return 'DEXIN - Nền tảng thiết kế nội thất thông minh với AI, tạo không gian sống hoàn hảo';
      
      case 'avatar':
        return 'Avatar người dùng DEXIN';
      
      case 'design':
        return 'Thiết kế nội thất 2D với công cụ Phác Ý của DEXIN';
      
      case 'furniture':
        return 'Đồ nội thất và trang trí nhà đẹp tại DEXIN';
      
      default:
        return 'Hình ảnh từ DEXIN - Nền tảng thiết kế nội thất với AI';
    }
  };

  // Tự động tạo title dựa trên category
  const generateTitle = () => {
    if (title) return title;
    
    switch (category) {
      case 'product':
        return productName ? 
          `Mua ${productName} chất lượng cao tại DEXIN` :
          'Sản phẩm nội thất chất lượng cao tại DEXIN';
      
      case 'hero':
        return 'DEXIN - Thiết kế nội thất thông minh với AI';
      
      case 'avatar':
        return 'Hồ sơ người dùng DEXIN';
      
      case 'design':
        return 'Công cụ thiết kế nội thất 2D miễn phí';
      
      case 'furniture':
        return 'Nội thất và đồ trang trí nhà đẹp';
      
      default:
        return 'DEXIN - Nền tảng thiết kế nội thất với AI';
    }
  };

  // Tối ưu hóa thuộc tính hình ảnh cho SEO
  const optimizedProps = optimizeImageForSEO(src, generateAltText(), generateTitle());

  const handleLoad = (e) => {
    setIsLoaded(true);
    if (onLoad) onLoad(e);
  };

  const handleError = (e) => {
    setHasError(true);
    if (onError) onError(e);
  };

  // Tạo srcSet cho responsive images
  const generateSrcSet = (baseSrc) => {
    if (!baseSrc || baseSrc.startsWith('http')) return '';
    
    const extension = baseSrc.split('.').pop();
    const baseName = baseSrc.replace(`.${extension}`, '');
    
    return `
      ${baseName}-320w.${extension} 320w,
      ${baseName}-640w.${extension} 640w,
      ${baseName}-1024w.${extension} 1024w,
      ${baseName}-1280w.${extension} 1280w
    `.trim();
  };

  const shouldLoad = loading === 'eager' || isInView;

  return (
    <div 
      ref={imgRef}
      className={`relative overflow-hidden ${className}`}
      style={{ width, height }}
    >
      {/* Placeholder/Loading state */}
      {!isLoaded && !hasError && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center"
          style={{ width, height }}
        >
          <svg 
            className="w-8 h-8 text-gray-400" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path 
              fillRule="evenodd" 
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" 
              clipRule="evenodd" 
            />
          </svg>
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div 
          className="absolute inset-0 bg-gray-100 flex items-center justify-center text-gray-500"
          style={{ width, height }}
        >
          <div className="text-center">
            <svg 
              className="w-8 h-8 mx-auto mb-2 text-gray-400" 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path 
                fillRule="evenodd" 
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" 
                clipRule="evenodd" 
              />
            </svg>
            <p className="text-sm">Không thể tải hình ảnh</p>
          </div>
        </div>
      )}

      {/* Main image */}
      {shouldLoad && (
        <img
          src={hasError ? placeholder : optimizedProps.src}
          alt={optimizedProps.alt}
          title={optimizedProps.title}
          loading={optimizedProps.loading}
          width={width}
          height={height}
          srcSet={generateSrcSet(src)}
          sizes="(max-width: 320px) 320px, (max-width: 640px) 640px, (max-width: 1024px) 1024px, 1280px"
          onLoad={handleLoad}
          onError={handleError}
          className={`
            transition-opacity duration-300
            ${isLoaded ? 'opacity-100' : 'opacity-0'}
            ${className}
          `}
          style={{
            objectFit: 'cover',
            width: '100%',
            height: '100%'
          }}
          {...props}
        />
      )}

      {/* SEO Schema markup for images */}
      {category === 'product' && productName && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "ImageObject",
              "contentUrl": src,
              "name": generateTitle(),
              "description": generateAltText(),
              "author": {
                "@type": "Organization",
                "name": "DEXIN"
              }
            })
          }}
        />
      )}
    </div>
  );
};

// Component cho hình ảnh hero với tối ưu hóa đặc biệt
export const HeroImageSEO = ({ src, alt, title, className = '', ...props }) => {
  return (
    <ImageWithSEO
      src={src}
      alt={alt}
      title={title}
      category="hero"
      loading="eager" // Hero images should load immediately
      className={`w-full h-auto ${className}`}
      {...props}
    />
  );
};

// Component cho hình ảnh sản phẩm
export const ProductImageSEO = ({ src, alt, title, productName, className = '', ...props }) => {
  return (
    <ImageWithSEO
      src={src}
      alt={alt}
      title={title}
      category="product"
      productName={productName}
      className={`rounded-lg ${className}`}
      {...props}
    />
  );
};

// Component cho avatar
export const AvatarImageSEO = ({ src, alt, title, className = '', size = 40, ...props }) => {
  return (
    <ImageWithSEO
      src={src}
      alt={alt}
      title={title}
      category="avatar"
      width={size}
      height={size}
      className={`rounded-full ${className}`}
      {...props}
    />
  );
};

export default ImageWithSEO;
