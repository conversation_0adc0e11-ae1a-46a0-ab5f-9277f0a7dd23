# 🏠 DEXIN - Trợ lý AI Thiết kế Nội thất Thông minh

<div align="center">
  <img src="public/images/Logo%20DEXIN%20finall%202.png" alt="DEXIN Logo" width="200"/>

  <p><em>Ứng dụng thương mại điện tử nội thất với trợ lý AI thông minh</em></p>

  [![React](https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB)](https://reactjs.org/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)
  [![Vite](https://img.shields.io/badge/Vite-646CFF?style=for-the-badge&logo=vite&logoColor=white)](https://vitejs.dev/)
  [![Google AI](https://img.shields.io/badge/Google_AI-4285F4?style=for-the-badge&logo=google&logoColor=white)](https://ai.google.dev/)

  [🚀 Demo Live](#) | [📖 Tài liệu](#-tính-năng-chính) | [🐛 Báo lỗi](https://github.com/yourusername/dexin/issues)
</div>

---

## ✨ DEXIN là gì?

**DEXIN** là một ứng dụng web hiện đại giúp bạn:
- 🛍️ **Mua sắm nội thất** dễ dàng với giao diện đẹp mắt
- 🎨 **Thiết kế 2D "Phác Ý"** - Vẽ bản phác thảo nội thất với công cụ kéo thả
- 🤖 **Chat với AI Nara** để nhận tư vấn thiết kế nội thất chuyên nghiệp
- 🏠 **Khám phá ý tưởng "Chuyện Nhà"** với thư viện Pinterest-style
- 💝 **Quản lý danh sách yêu thích** và **giỏ hàng** thông minh
- 📱 **Sử dụng trên mọi thiết bị** từ điện thoại đến máy tính

> 💡 **Ví dụ:** Bạn muốn thiết kế phòng khách? Hỏi AI Nara: *"Tôi có phòng khách 20m². Nên chọn sofa màu gì và bố trí như thế nào?"* hoặc sử dụng công cụ **Phác Ý** để vẽ layout trực quan!

---

## 🎯 Tính năng chính

### 🎨 **Công cụ thiết kế "Phác Ý" (2D Sketch)**
- 🖱️ **Kéo thả nội thất** từ sidebar vào canvas
- 🏠 **Phòng mẫu có sẵn** để bắt đầu nhanh
- 🔧 **Toolbar đa năng**: Select, Hand, Lock modes
- 📐 **Layers management** cho thiết kế phức tạp
- 💾 **Xuất file PNG/PDF** với tên tùy chỉnh
- ⌨️ **Phím tắt**: Delete để xóa đối tượng
- 🔒 **Chế độ khóa** để chỉ xem, không chỉnh sửa

### 🏠 **Thư viện ý tưởng "Chuyện Nhà"**
- 🎨 **Pinterest-style masonry layout** với cards động
- 📱 **Responsive**: 2-5 cột tùy màn hình
- 🔍 **Lọc theo danh mục**: Phòng khách, phòng ngủ, nhà bếp...
- ❤️ **Yêu thích và chia sẻ** ý tưởng
- 📊 **Thống kê lượt xem** và tương tác
- 🎭 **Aspect ratios đa dạng**: Portrait, landscape, square

### 🛒 **Cửa hàng nội thất**
- 📦 Danh mục sản phẩm đa dạng (bàn, ghế, giường, tủ, đèn...)
- 🎨 Chọn màu sắc và thuộc tính sản phẩm
- 🔍 **Tìm kiếm và lọc thông minh** với price range slider
- 📈 **Sắp xếp giá**: Thấp đến cao, cao đến thấp
- 👁️ Xem nhanh sản phẩm (Quick View)
- 🛒 **Hover effects** mượt mà với add-to-cart

### 🛍️ **Quản lý mua sắm**
- ❤️ **Danh sách yêu thích** với localStorage persistence
- 🛒 **Giỏ hàng** với nhiều variant và quantity
- 📊 Tính tổng tiền tự động
- 🔔 **Toast notifications** cho mọi tương tác
- 📱 Giao diện responsive hoàn hảo

### 🤖 **Trợ lý AI Nara thông minh**
- 💬 **Chat interface** giống ChatGPT/Gemini
- 🎨 Tư vấn thiết kế cá nhân hóa
- 📝 Gợi ý sản phẩm phù hợp
- 🚀 Phản hồi nhanh chóng với Google Gemini AI
- 🎯 **Centered layout** cho trải nghiệm tốt nhất

### 🎨 **Giao diện hiện đại**
- ✨ **Motion animations** mượt mà (900ms transitions)
- 🌈 **DEXIN Design System** với color palette tùy chỉnh
- 📱 **Responsive design** tương thích mọi thiết bị
- ⚡ **Performance tối ưu** với Vite và lazy loading
- 🎭 **Component splitting** cho maintainability

---

## 🚀 Bắt đầu ngay (5 phút)

### 📋 Bạn cần có sẵn
- 💻 **Node.js** (phiên bản 18 trở lên) - [Tải tại đây](https://nodejs.org/)
- 🔑 **API Key của Google Gemini** - [Lấy miễn phí tại đây](https://ai.google.dev/)

### 📥 Cài đặt

**Bước 1:** Tải mã nguồn về máy
```bash
git clone https://github.com/yourusername/dexin.git
cd dexin
```

**Bước 2:** Cài đặt các thư viện cần thiết
```bash
npm install
```

**Bước 3:** Tạo file cấu hình `.env`
```bash
# Tạo file .env và thêm API key
echo "VITE_GEMINI_API_KEY=your-api-key-here" > .env
```

**Bước 4:** Chạy ứng dụng
```bash
npm run dev
```

**Bước 5:** Mở trình duyệt và truy cập http://localhost:5173

🎉 **Xong rồi!** Bây giờ bạn có thể sử dụng DEXIN ngay!

---

## 🎮 Hướng dẫn sử dụng nhanh

### 🎨 **Công cụ "Phác Ý" - Thiết kế 2D**
1. **Truy cập**: Nhấn vào menu "Phác Ý" trên navbar
2. **Kéo thả**: Chọn nội thất từ sidebar bên trái và kéo vào canvas
3. **Di chuyển**: Dùng tool "Hand" để di chuyển view, "Select" để chọn đối tượng
4. **Xóa**: Chọn đối tượng và nhấn phím `Delete`
5. **Khóa**: Dùng tool "Lock" để chỉ xem, không chỉnh sửa
6. **Xuất file**: Nhấn "Download" để lưu thành PNG/PDF

### 🏠 **Thư viện "Chuyện Nhà"**
1. **Duyệt ý tưởng**: Xem gallery với layout Pinterest-style
2. **Lọc danh mục**: Chọn "Phòng khách", "Phòng ngủ", etc.
3. **Tương tác**: Hover để thấy nút Like và Share
4. **Yêu thích**: Nhấn ❤️ để lưu ý tưởng

### 🛒 **Mua sắm thông minh**
1. **Duyệt Store**: Xem sản phẩm với filters và price range
2. **Thêm Wishlist**: Nhấn ❤️ trên product card
3. **Thêm giỏ hàng**: Hover và nhấn "Add to cart"
4. **Quản lý**: Xem Cart và Wishlist từ navbar

### 🤖 **Chat với AI Nara**
1. **Truy cập**: Nhấn "Mở Lời" trên navbar
2. **Hỏi đáp**: Gõ câu hỏi về thiết kế nội thất
3. **Ví dụ**: *"Phòng khách 25m² nên bố trí như thế nào?"*

---

## 🛠️ Các lệnh hữu ích

| Lệnh | Mô tả | Khi nào dùng |
|------|-------|---------------|
| `npm run dev` | Chạy ứng dụng để phát triển | Khi bạn đang code |
| `npm run build` | Tạo bản build cho website thật | Khi triển khai lên server |
| `npm run build:analyze` | Build với phân tích bundle size | Kiểm tra tối ưu hóa |
| `npm run preview` | Xem trước bản build | Kiểm tra trước khi deploy |
| `npm test` | Chạy kiểm tra lỗi | Đảm bảo code hoạt động đúng |

---

## 📁 Cấu trúc dự án (Dành cho dev)

```
dexin/
├── 📁 public/                 # Hình ảnh và file tĩnh
│   ├── 🖼️ images/            # Logo, hình sản phẩm
│   └── 📄 favicon.ico         # Icon website
├── 📁 src/                    # Mã nguồn chính
│   ├── 📁 components/         # Các thành phần giao diện
│   │   ├── 💬 chat/           # Phần chat với AI
│   │   ├── 🛒 common/         # Thành phần dùng chung (Button, etc.)
│   │   └── 🎨 layout/         # Bố cục trang (Header, Footer)
│   ├── 📁 pages/              # Các trang
│   │   ├── 🏠 Home/           # Trang chủ
│   │   ├── 🛍️ Store/          # Cửa hàng với filters
│   │   ├── 🛒 Cart/           # Giỏ hàng
│   │   ├── ❤️ Wishlist/       # Danh sách yêu thích
│   │   ├── 🎨 PhacY/          # Công cụ thiết kế 2D
│   │   ├── 🏠 ChuyenNha/      # Thư viện ý tưởng
│   │   ├── 🤖 MoLoi/          # Chat với AI Nara
│   │   ├── 🎯 NgoLoi/         # Landing page
│   │   └── 🎵 ChungNhip/      # Trang đặc biệt
│   ├── 📁 context/            # Quản lý trạng thái (Cart, Chat, Wishlist)
│   ├── 📁 data/               # Dữ liệu sản phẩm và mock data
│   └── 📁 services/           # Kết nối API (Gemini AI)
├── ⚙️ .env                    # Cấu hình bí mật
└── 📄 package.json            # Danh sách thư viện
```

---

## 🎨 Công nghệ sử dụng

### 🖥️ **Frontend**
- **React 19** - Thư viện tạo giao diện hiện đại
- **React Router** - Điều hướng trang SPA
- **Tailwind CSS v3.4.1** - CSS framework với custom palette
- **Motion v12.6.3** - Animation library mượt mà
- **Lucide React** - Icon system đẹp và nhất quán

### 🎨 **Design & Canvas**
- **Konva.js** - Canvas library cho công cụ Phác Ý 2D
- **React Konva** - React wrapper cho Konva
- **CSS Masonry** - Pinterest-style layout cho gallery

### 🤖 **AI & Services**
- **Google Gemini AI** - Trợ lý AI thông minh Nara
- **React Markdown** - Hiển thị response từ AI
- **React Toastify v11.0.5** - Notification system

### ⚡ **Tools & Build**
- **Vite** - Build tool nhanh và hiện đại
- **PostCSS** - Xử lý CSS advanced
- **ESLint** - Kiểm tra code quality
- **Component Architecture** - Splitting pattern cho maintainability

---

## 🤝 Đóng góp cho dự án

Chúng tôi rất hoan nghênh mọi đóng góp!

### 🌟 Cách đóng góp đơn giản:
1. **⭐ Star** dự án này
2. **🍴 Fork** về tài khoản của bạn
3. **🔧 Sửa đổi** hoặc **➕ thêm tính năng**
4. **📤 Tạo Pull Request**

### 🐛 Báo lỗi:
- [Tạo issue mới](https://github.com/yourusername/dexin/issues)
- Mô tả chi tiết lỗi và cách tái hiện

### 💡 Đề xuất tính năng:
- [Tạo feature request](https://github.com/yourusername/dexin/issues)
- Giải thích tại sao tính năng này hữu ích

---

## ❓ Câu hỏi thường gặp (FAQ)

<details>
<summary><strong>🤔 Tôi không biết code, có thể sử dụng được không?</strong></summary>

Có! Bạn chỉ cần:
1. Tải Node.js về máy
2. Copy và paste các lệnh trong hướng dẫn
3. Lấy API key miễn phí từ Google
4. Chạy `npm run dev`

Nếu gặp khó khăn, hãy [tạo issue](https://github.com/yourusername/dexin/issues) để được hỗ trợ!
</details>

<details>
<summary><strong>🔑 Làm sao để lấy API key Google Gemini?</strong></summary>

1. Truy cập [Google AI Studio](https://ai.google.dev/)
2. Đăng nhập bằng tài khoản Google
3. Tạo API key mới
4. Copy và paste vào file `.env`
</details>

<details>
<summary><strong>💰 Có tốn phí gì không?</strong></summary>

- ✅ **Mã nguồn**: Hoàn toàn miễn phí
- ✅ **Google Gemini**: Có quota miễn phí hàng tháng
- ✅ **Hosting**: Có thể deploy miễn phí trên Vercel, Netlify
</details>

<details>
<summary><strong>📱 Có hoạt động trên điện thoại không?</strong></summary>

Có! DEXIN được thiết kế responsive, hoạt động mượt mà trên:
- 📱 Điện thoại
- 📟 Tablet
- 💻 Máy tính
- 🖥️ Màn hình lớn
</details>

---

## ⚡ Tối ưu hóa hiệu năng

### 📦 **Bundle Size Optimization**
Dự án đã được tối ưu để giảm kích thước bundle:

- **Code Splitting**: Tách thành các chunks nhỏ theo vendor
- **Tree Shaking**: Loại bỏ code không sử dụng
- **FontAwesome Optimization**: Chỉ import icon thực sự cần thiết
- **Asset Optimization**: Tối ưu hình ảnh và fonts
- **Minification**: Nén code và CSS trong production

### 🔍 **Kiểm tra Bundle Size**
```bash
# Phân tích kích thước bundle
npm run build:analyze

# Xem chi tiết các chunks
npm run build && ls -la build/assets/
```

### 📊 **Performance Metrics**
- **Initial Bundle**: ~800KB (đã tối ưu từ 1.9MB)
- **Vendor Chunks**: Tách riêng React, UI libs, Canvas libs
- **Lazy Loading**: Các trang được load theo yêu cầu
- **Asset Caching**: Cache tối ưu cho static assets

---

## 🚀 Triển khai (Deploy)

### 🌐 **Vercel** (Đề xuất - Miễn phí)
1. Đăng ký tài khoản [Vercel](https://vercel.com)
2. Connect với GitHub repository
3. Thêm environment variable `VITE_GEMINI_API_KEY`
4. Vercel sẽ tự động detect cấu hình từ `vercel.json`
5. Deploy tự động với output directory `build/`

> **Lưu ý**: Dự án đã được tối ưu với `vercel.json` để sử dụng đúng output directory và caching headers.

### 🌐 **Netlify** (Miễn phí)
1. Đăng ký tài khoản [Netlify](https://netlify.com)
2. Drag & drop thư mục `dist` sau khi build
3. Cấu hình environment variables

### 🐳 **Docker**
```bash
# Build image
docker build -t dexin .

# Run container
docker run -p 3000:3000 dexin
```

---

## 📈 Roadmap

### 🎯 **Phiên bản tiếp theo (v2.0)**
- [ ] 🔐 Đăng nhập/đăng ký người dùng
- [ ] 💳 Tích hợp thanh toán (VNPay, MoMo)
- [ ] 📊 Dashboard quản trị cho admin
- [ ] 🌍 Đa ngôn ngữ (Tiếng Anh)
- [ ] 📧 Thông báo email và push notifications
- [ ] 🎨 Theme tối/sáng với system preference
- [ ] 🔄 **Phác Ý nâng cao**: Undo/Redo, Grid snap, Measurements
- [ ] 📱 **ChuyenNha mở rộng**: User-generated content, Comments

### 🚀 **Tương lai xa (v3.0+)**
- [ ] 📱 Mobile app (React Native) với offline mode
- [ ] 🔍 Tìm kiếm bằng hình ảnh với AI Vision
- [ ] 🎮 AR/VR xem nội thất trong không gian thực
- [ ] 🤖 **AI design generator**: Tự động tạo layout từ mô tả
- [ ] 🏗️ **3D Modeling**: Nâng cấp từ 2D lên 3D với Three.js
- [ ] 🌐 **Collaborative editing**: Real-time collaboration trên Phác Ý
- [ ] 📊 **Analytics**: Tracking user behavior và design trends

---

## 📞 Liên hệ & Hỗ trợ

<div align="center">

### 💬 **Cần hỗ trợ?**

[![Email](https://img.shields.io/badge/<EMAIL>-red?style=for-the-badge&logo=gmail)](mailto:<EMAIL>)
[![Website](https://img.shields.io/badge/Website-dexin.vn-blue?style=for-the-badge&logo=google-chrome)](https://dexin.vn)
[![GitHub Issues](https://img.shields.io/badge/GitHub-Issues-black?style=for-the-badge&logo=github)](https://github.com/yourusername/dexin/issues)

### 🌟 **Nếu dự án hữu ích, hãy cho chúng tôi một Star!**

</div>

---

## 📄 Giấy phép

Dự án được phân phối dưới giấy phép **MIT**. Xem [LICENSE](LICENSE) để biết thêm chi tiết.

```
MIT License - Bạn có thể:
✅ Sử dụng thương mại
✅ Sửa đổi
✅ Phân phối
✅ Sử dụng riêng tư
```

---

<div align="center">
  <p><strong>💖 Được phát triển với tình yêu bởi team DEXIN</strong></p>
  <p><em>⭐ Đừng quên star dự án nếu bạn thấy hữu ích!</em></p>

  ---

  <p>
    <a href="#-dexin---trợ-lý-ai-thiết-kế-nội-thất-thông-minh">🔝 Về đầu trang</a>
  </p>
</div>
