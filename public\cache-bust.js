
// Auto-generated cache bust script
window.APP_VERSION = '1.0.1750945125';
window.BUILD_ID = '1750945125366';
window.BUILD_DATE = '2025-06-26T13:38:45.367Z';
window.GIT_COMMIT = '7142064';

// Force cache refresh cho development
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  console.log('🔄 Development mode - forcing cache refresh');
  
  // Add timestamp to all script/link tags
  document.addEventListener('DOMContentLoaded', function() {
    const timestamp = Date.now();
    
    document.querySelectorAll('script[src], link[href]').forEach(element => {
      const attr = element.tagName === 'SCRIPT' ? 'src' : 'href';
      const url = element.getAttribute(attr);
      
      if (url && !url.includes('?') && !url.startsWith('http')) {
        element.setAttribute(attr, url + '?v=' + timestamp);
      }
    });
  });
}
