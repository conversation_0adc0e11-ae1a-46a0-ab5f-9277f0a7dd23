import React from 'react';
import { motion } from 'motion/react';

const HeroSection = () => {
  // Hàm xử lý scroll smooth đến GallerySection
  const handleScrollToGallery = () => {
    const gallerySection = document.getElementById('gallery-section');
    if (gallerySection) {
      // Tính toán offset để account cho navbar height (khoảng 80px)
      const navbarHeight = 80;
      const elementPosition = gallerySection.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - navbarHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section className="relative bg-gradient-to-br from-dexin-bg to-white py-16 sm:py-10 lg:py-15">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Text Content */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-10"
        >
          {/* Breadcrumb */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex items-center justify-center space-x-2 text-sm text-gray-600 mb-6"
          >
            <span>Trang chủ</span>
            <span>/</span>
            <span className="text-dexin-primary font-medium">Chuyện nhà</span>
          </motion.div>

          {/* Tagline */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-dexin-primary font-medium text-lg mb-4"
          >
            Rất vui được đồng hành cùng bạn!
          </motion.p>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-dexin-dark mb-8 leading-tight"
          >
            Mỗi không gian là
            <br/>
            <div className='mt-2'><span  className="text-dexin-primary">một câu chuyện đáng nhớ</span></div>
          </motion.h1>
        </motion.div>

        {/* Images Layout */}
        <div className="relative">
          {/* Desktop: 3 columns layout */}
          <div className="hidden lg:grid lg:grid-cols-5 gap-6 items-center">
            {/* Left Image */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="col-span-1"
            >
              <div className="relative rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                <img
                  src="/images/hometile hero.jpg"
                  alt="Không gian nội thất phụ"
                  className="w-full h-[300px] xl:h-[400px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>
              </div>
            </motion.div>

            {/* Center Main Image */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="col-span-3 relative"
            >
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src="/images/ChuyenNha/phong-cach-biophilic-design-24.jpg"
                  alt="Phong cách biophilic design - Không gian xanh trong nhà"
                  className="w-full h-[400px] xl:h-[500px] object-cover"
                />

                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>

                {/* Floating decorations */}
                <motion.div
                  animate={{
                    y: [0, -10, 0],
                    rotate: [0, 2, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute -top-4 -right-4 w-16 h-16 bg-dexin-light rounded-full
                             opacity-80 blur-sm"
                ></motion.div>

                <motion.div
                  animate={{
                    y: [0, 15, 0],
                    rotate: [0, -3, 0]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                  className="absolute -bottom-6 -left-6 w-12 h-12 bg-dexin-primary rounded-full
                             opacity-60 blur-sm"
                ></motion.div>
              </div>
            </motion.div>

            {/* Right Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
              className="col-span-1"
            >
              <div className="relative rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                <img
                  src="/images/hometile hero.jpg"
                  alt="Không gian nội thất phụ"
                  className="w-full h-[300px] xl:h-[400px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>
              </div>
            </motion.div>
          </div>

          {/* Tablet: 2 columns layout */}
          <div className="hidden md:grid lg:hidden md:grid-cols-3 gap-6 items-center">
            {/* Left Image */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="col-span-1"
            >
              <div className="relative rounded-2xl overflow-hidden shadow-lg">
                <img
                  src="/images/hometile hero.jpg"
                  alt="Không gian nội thất phụ"
                  className="w-full h-[250px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>
              </div>
            </motion.div>

            {/* Center Main Image */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="col-span-2"
            >
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src="/images/ChuyenNha/phong-cach-biophilic-design-24.jpg"
                  alt="Phong cách biophilic design - Không gian xanh trong nhà"
                  className="w-full h-[350px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            </motion.div>
          </div>

          {/* Mobile: Single image */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="md:hidden"
          >
            <div className="relative rounded-2xl overflow-hidden shadow-2xl">
              <img
                src="/images/ChuyenNha/phong-cach-biophilic-design-24.jpg"
                alt="Phong cách biophilic design - Không gian xanh trong nhà"
                className="w-full h-[300px] sm:h-[400px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          </motion.div>
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="text-center mt-12"
        >
          {/* Description */}
          <p className="text-gray-600 text-lg max-w-2xl mx-auto mb-8">
            Khám phá những câu chuyện thú vị về thiết kế nội thất,
            xu hướng trang trí và cách tạo nên không gian sống hoàn hảo cho gia đình bạn.
          </p>

          {/* CTA Button */}
          <motion.button
            onClick={handleScrollToGallery}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-dexin-primary text-white px-8 py-3 rounded-full font-medium
                       hover:bg-dexin-light-90 transition-all duration-300
                       shadow-lg hover:shadow-dexin cursor-pointer"
          >
            Khám phá ngay
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;
