import React, { useState } from 'react';
import {
  <PERSON>,
  Hand,
  MousePointer,
  Layers,
  Undo,
  Redo
} from 'lucide-react';
import { classNames } from '../../../utils/classNames';

const Toolbar = ({
  isLocked,
  setIsLocked,
  viewMode,
  setViewMode,
  canUndo,
  canRedo,
  handleUndo,
  handleRedo
}) => {
  // State để hiển thị thông báo khi khóa/mở khóa
  const [showLockMessage, setShowLockMessage] = useState(false);
  const [lockMessage, setLockMessage] = useState('');

  // Xử lý khi click vào nút khóa
  const handleLockClick = () => {
    const newLockedState = !isLocked;
    setIsLocked(newLockedState);

    // Hiển thị thông báo
    setLockMessage(newLockedState ? '<PERSON><PERSON> khóa trang. T<PERSON>t cả tương tác bị vô hiệ<PERSON> hóa.' : 'Đã mở khóa trang.');
    setShowLockMessage(true);

    // Tự động ẩn thông báo sau 3 giây
    setTimeout(() => {
      setShowLockMessage(false);
    }, 3000);
  };

  return (
    <>
      {/* Thông báo khóa/mở khóa */}
      {showLockMessage && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg shadow-lg z-50 opacity-90 transition-opacity duration-300 flex items-center max-w-[90vw] sm:max-w-md">
          <span className="text-xs sm:text-sm">{lockMessage}</span>
        </div>
      )}

      <div className="fixed bottom-4 sm:bottom-6 left-1/2 transform -translate-x-1/2 bg-white rounded-[20px] sm:rounded-[30px] shadow-xl px-3 sm:px-6 py-2 sm:py-4 flex items-center space-x-2 sm:space-x-6 z-[51]" style={{ boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)' }}>
        {/* Divider */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          <button
            className={classNames(
              "p-2 sm:p-3 rounded-full transition-all duration-200 transform hover:scale-105 active:scale-95",
              isLocked ? "bg-dexin-light text-white shadow-md" : "bg-white text-gray-700 hover:bg-gray-50 shadow-md"
            )}
            onClick={handleLockClick}
            title={isLocked ? "Mở khóa" : "Khóa trang"}
          >
            <Lock size={18} className="sm:hidden" />
            <Lock size={22} className="hidden sm:block" />
          </button>

        <button
          className={classNames(
            "p-2 sm:p-3 rounded-full transition-all duration-200 transform",
            isLocked ? "opacity-50 cursor-not-allowed" : "hover:scale-105 active:scale-95",
            viewMode === 'hand' ? "bg-dexin-light text-white shadow-md" : "bg-white text-gray-700 hover:bg-gray-50 shadow-md"
          )}
          onClick={isLocked ? undefined : () => setViewMode('hand')}
          title={isLocked ? "Đã khóa" : "Chế độ xem"}
          disabled={isLocked}
        >
          <Hand size={18} className="sm:hidden" />
          <Hand size={22} className="hidden sm:block" />
        </button>

        <button
          className={classNames(
            "p-2 sm:p-3 rounded-full transition-all duration-200 transform",
            isLocked ? "opacity-50 cursor-not-allowed" : "hover:scale-105 active:scale-95",
            viewMode === 'edit' ? "bg-dexin-light text-white shadow-md" : "bg-white text-gray-700 hover:bg-gray-50 shadow-md"
          )}
          onClick={isLocked ? undefined : () => setViewMode('edit')}
          title={isLocked ? "Đã khóa" : "Chế độ chỉnh sửa"}
          disabled={isLocked}
        >
          <MousePointer size={18} className="sm:hidden" />
          <MousePointer size={22} className="hidden sm:block" />
        </button>

        <button
          className={classNames(
            "p-2 sm:p-3 rounded-full transition-all duration-200 transform",
            isLocked ? "opacity-50 cursor-not-allowed" : "hover:scale-105 active:scale-95",
            viewMode === 'layers' ? "bg-dexin-light text-white shadow-md" : "bg-white text-gray-700 hover:bg-gray-50 shadow-md"
          )}
          onClick={isLocked ? undefined : () => setViewMode('layers')}
          title={isLocked ? "Đã khóa" : "Quản lý lớp"}
          disabled={isLocked}
        >
          <Layers size={18} className="sm:hidden" />
          <Layers size={22} className="hidden sm:block" />
        </button>
      </div>

      {/* Divider */}
      <div className="h-6 sm:h-8 w-px bg-gray-200 opacity-70"></div>

      <div className="flex items-center space-x-2 sm:space-x-4">
        <button
          className={classNames(
            "p-2 sm:p-3 rounded-full transition-all duration-200 transform",
            isLocked
              ? "bg-white text-gray-400 shadow-md opacity-50 cursor-not-allowed"
              : canUndo
                ? "bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:scale-105 active:scale-95"
                : "bg-white text-gray-400 shadow-md opacity-70 cursor-not-allowed"
          )}
          onClick={isLocked ? undefined : handleUndo}
          disabled={isLocked || !canUndo}
          title={isLocked ? "Đã khóa" : "Hoàn tác"}
        >
          <Undo size={18} className="sm:hidden" />
          <Undo size={22} className="hidden sm:block" />
        </button>

        <button
          className={classNames(
            "p-2 sm:p-3 rounded-full transition-all duration-200 transform",
            isLocked
              ? "bg-white text-gray-400 shadow-md opacity-50 cursor-not-allowed"
              : canRedo
                ? "bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:scale-105 active:scale-95"
                : "bg-white text-gray-400 shadow-md opacity-70 cursor-not-allowed"
          )}
          onClick={isLocked ? undefined : handleRedo}
          disabled={isLocked || !canRedo}
          title={isLocked ? "Đã khóa" : "Làm lại"}
        >
          <Redo size={18} className="sm:hidden" />
          <Redo size={22} className="hidden sm:block" />
        </button>
      </div>
    </div>
    </>
  );
};

export default Toolbar;
