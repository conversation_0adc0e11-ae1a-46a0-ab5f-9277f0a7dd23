import React, { useEffect, useState } from 'react';
import { motion } from 'motion/react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import {
  XCircle,
  RefreshCw,
  Home,
  ArrowLeft,
  AlertTriangle,
  CreditCard,
  Clock
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import paymentService from '../../services/paymentService';

const PaymentCancel = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(10);
  const [loading, setLoading] = useState(true);

  // Lấy thông tin từ URL params theo PayOS Return URL format
  const code = searchParams.get('code');
  const paymentLinkId = searchParams.get('id');
  const cancel = searchParams.get('cancel');
  const status = searchParams.get('status');
  const orderCode = searchParams.get('orderCode');
  const reason = searchParams.get('reason') || 'Người dùng hủy thanh toán';

  useEffect(() => {
    const handlePaymentCancel = async () => {
      try {
        setLoading(true);

        // Cập nhật trạng thái thanh toán nếu có thông tin từ PayOS
        if (orderCode && (status === 'CANCELLED' || cancel === 'true')) {
          const updateResult = await paymentService.updateDesignPaymentStatus(
            orderCode, // transactionIdGateway - sử dụng orderCode thay vì paymentLinkId
            'CANCELLED' // status
          );

          if (updateResult.success) {
            toast.success('Đã cập nhật trạng thái hủy thanh toán');
          } else {
            console.error('Failed to update payment status:', updateResult.message);
          }
        }
      } catch (error) {
        console.error('Error handling payment cancel:', error);
      } finally {
        setLoading(false);
      }
    };

    handlePaymentCancel();

    // Countdown để tự động redirect về trang account sau 10 giây
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          navigate('/account');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [navigate, orderCode, status, cancel]);

  const handleRetryPayment = () => {
    // Redirect về trang account để thử thanh toán lại
    navigate('/account?tab=designs');
  };

  const handleStopCountdown = () => {
    setCountdown(0);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang xử lý thông tin hủy thanh toán...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg shadow-lg overflow-hidden"
        >
          {/* Header với icon hủy */}
          <div className="bg-gradient-to-r from-red-500 to-red-600 px-6 py-8 text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <XCircle className="w-16 h-16 text-white mx-auto mb-4" />
            </motion.div>
            <h1 className="text-2xl font-bold text-white mb-2">
              Thanh toán đã bị hủy
            </h1>
            <p className="text-red-100">
              Giao dịch của bạn đã bị hủy bỏ hoặc không thể hoàn thành
            </p>
          </div>

          {/* Thông tin hủy thanh toán */}
          <div className="px-6 py-6">
            <div className="space-y-4">
              {/* Mã đơn hàng */}
              {orderCode && (
                <div className="flex items-center justify-between py-3 border-b border-gray-200">
                  <div className="flex items-center space-x-3">
                    <CreditCard className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-600">Mã đơn hàng:</span>
                  </div>
                  <span className="font-semibold text-gray-900">{orderCode}</span>
                </div>
              )}

              {/* Lý do hủy */}
              <div className="flex items-center justify-between py-3 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600">Lý do:</span>
                </div>
                <span className="font-semibold text-gray-900">{reason}</span>
              </div>

              {/* Thời gian */}
              <div className="flex items-center justify-between py-3 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600">Thời gian:</span>
                </div>
                <span className="font-semibold text-gray-900">
                  {new Date().toLocaleString('vi-VN')}
                </span>
              </div>

              {/* Trạng thái */}
              <div className="flex items-center justify-between py-3">
                <div className="flex items-center space-x-3">
                  <XCircle className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600">Trạng thái:</span>
                </div>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                  Đã hủy
                </span>
              </div>
            </div>

            {/* Thông báo hướng dẫn */}
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="text-sm font-medium text-yellow-800 mb-2">
                Điều gì xảy ra tiếp theo?
              </h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Không có khoản phí nào được tính từ tài khoản của bạn</li>
                <li>• Thiết kế vẫn ở trạng thái khóa và cần thanh toán để mở</li>
                <li>• Bạn có thể thử thanh toán lại bất cứ lúc nào</li>
                <li>• Nếu gặp vấn đề, vui lòng liên hệ hỗ trợ khách hàng</li>
              </ul>
            </div>

            {/* Countdown notification */}
            {countdown > 0 && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-center">
                <p className="text-sm text-blue-700">
                  Tự động chuyển về trang tài khoản sau{' '}
                  <span className="font-bold text-blue-800">{countdown}</span> giây
                </p>
                <button
                  onClick={handleStopCountdown}
                  className="mt-2 text-xs text-blue-600 hover:text-blue-800 underline"
                >
                  Dừng đếm ngược
                </button>
              </div>
            )}

            {/* Action buttons */}
            <div className="mt-8 flex flex-col sm:flex-row gap-3">
              <button
                onClick={handleRetryPayment}
                className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-dexin-primary text-white rounded-lg hover:bg-dexin-light transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Thử thanh toán lại</span>
              </button>
              
              <Link
                to="/account"
                className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                onClick={handleStopCountdown}
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Về tài khoản</span>
              </Link>
              
              <Link
                to="/"
                className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                onClick={handleStopCountdown}
              >
                <Home className="w-4 h-4" />
                <span>Trang chủ</span>
              </Link>
            </div>

            {/* Support contact */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Cần hỗ trợ?{' '}
                <Link 
                  to="/contact" 
                  className="text-dexin-primary hover:text-dexin-light font-medium"
                  onClick={handleStopCountdown}
                >
                  Liên hệ với chúng tôi
                </Link>
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PaymentCancel;
