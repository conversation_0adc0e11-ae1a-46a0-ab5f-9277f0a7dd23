import React, { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { motion } from 'motion/react';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import DesignManagement from './components/DesignManagement';
import CustomerManagement from './components/CustomerManagement';

const StaffDashboard = () => {
  const [activeTab, setActiveTab] = useState('designs');

  return (
    <div className="h-screen w-full bg-background flex overflow-hidden">
      {/* Sidebar */}
      <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-muted/30">
        {/* Header */}
        <Header />

        {/* Content Area */}
        <motion.div
          className="flex-1 overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Routes>
            <Route path="/" element={<Navigate to="/staff/designs" replace />} />
            <Route path="/designs" element={<DesignManagement />} />
            <Route path="/customers" element={<CustomerManagement />} />
          </Routes>
        </motion.div>
      </div>
    </div>
  );
};

export default StaffDashboard;
