// Performance optimization utilities for DEXIN

// Completely disable web vitals console logging to prevent data leakage
const disableWebVitalsLogging = () => {
  if (typeof window === 'undefined') return;
  
  // Override console methods for web vitals to prevent any logging
  const originalConsoleLog = console.log;
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;
  
  const filterWebVitalsLogs = (...args) => {
    const stringArgs = args.join(' ');
    return stringArgs.includes('CLS') || 
           stringArgs.includes('FID') || 
           stringArgs.includes('FCP') || 
           stringArgs.includes('LCP') || 
           stringArgs.includes('TTFB') ||
           stringArgs.includes('metric') ||
           stringArgs.includes('vitals') ||
           stringArgs.includes('rating') ||
           stringArgs.includes('delta') ||
           stringArgs.includes('entries') ||
           stringArgs.includes('navigationType');
  };
  
  console.log = (...args) => {
    if (filterWebVitalsLogs(...args)) {
      return; // Suppress web vitals logs
    }
    originalConsoleLog.apply(console, args);
  };
  
  console.warn = (...args) => {
    if (filterWebVitalsLogs(...args)) {
      return; // Suppress web vitals logs
    }
    originalConsoleWarn.apply(console, args);
  };
  
  console.error = (...args) => {
    if (filterWebVitalsLogs(...args)) {
      return; // Suppress web vitals logs
    }
    originalConsoleError.apply(console, args);
  };
};

// Disable all web vitals console logging in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  // Override console methods for web vitals to prevent any logging
  const originalConsoleLog = console.log;
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;
  
  console.log = (...args) => {
    // Filter out web vitals related logs
    const stringArgs = args.join(' ');
    if (stringArgs.includes('CLS') || 
        stringArgs.includes('FID') || 
        stringArgs.includes('FCP') || 
        stringArgs.includes('LCP') || 
        stringArgs.includes('TTFB') ||
        stringArgs.includes('metric') ||
        stringArgs.includes('vitals')) {
      return; // Suppress the log
    }
    originalConsoleLog.apply(console, args);
  };
  
  console.warn = (...args) => {
    const stringArgs = args.join(' ');
    if (stringArgs.includes('CLS') || 
        stringArgs.includes('FID') || 
        stringArgs.includes('FCP') || 
        stringArgs.includes('LCP') || 
        stringArgs.includes('TTFB') ||
        stringArgs.includes('metric') ||
        stringArgs.includes('vitals')) {
      return;
    }
    originalConsoleWarn.apply(console, args);
  };
  
  console.error = (...args) => {
    const stringArgs = args.join(' ');
    if (stringArgs.includes('CLS') || 
        stringArgs.includes('FID') || 
        stringArgs.includes('FCP') || 
        stringArgs.includes('LCP') || 
        stringArgs.includes('TTFB') ||
        stringArgs.includes('metric') ||
        stringArgs.includes('vitals')) {
      return;
    }
    originalConsoleError.apply(console, args);
  };
}

// Web Vitals tracking
export const trackWebVitals = () => {
  // Completely disable web vitals tracking to prevent data leakage
  if (typeof window === 'undefined') return;
  
  // Do not track any web vitals metrics in production
  // This prevents any console logging or data exposure
  return;
};

// Preload critical resources
export const preloadCriticalResources = () => {
  const criticalResources = [
    '/images/Logo DEXIN finall 2.png',
    '/images/Mask group.png' // Hero image actually used
  ];

  criticalResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = resource;
    document.head.appendChild(link);
  });
};

// Lazy load non-critical resources
export const lazyLoadResources = () => {
  // Lazy load images
  const images = document.querySelectorAll('img[data-src]');
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        observer.unobserve(img);
      }
    });
  });

  images.forEach(img => imageObserver.observe(img));
};

// Optimize font loading
export const optimizeFontLoading = () => {
  // Preload critical fonts
  const criticalFonts = [
    'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
  ];

  criticalFonts.forEach(fontUrl => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = fontUrl;
    document.head.appendChild(link);

    // Load the font stylesheet
    const fontLink = document.createElement('link');
    fontLink.rel = 'stylesheet';
    fontLink.href = fontUrl;
    fontLink.media = 'print';
    fontLink.onload = function() {
      this.media = 'all';
    };
    document.head.appendChild(fontLink);
  });
};

// Debounce function for performance
export const debounce = (func, wait, immediate) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
};

// Throttle function for performance
export const throttle = (func, limit) => {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Check if device is low-end
export const isLowEndDevice = () => {
  // Check for various indicators of low-end devices
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
  const hardwareConcurrency = navigator.hardwareConcurrency || 4;
  const deviceMemory = navigator.deviceMemory || 4;

  // Consider device low-end if:
  // - Less than 4 CPU cores
  // - Less than 4GB RAM
  // - Slow connection
  const isSlowConnection = connection && (
    connection.effectiveType === 'slow-2g' ||
    connection.effectiveType === '2g' ||
    connection.effectiveType === '3g'
  );

  return hardwareConcurrency < 4 || deviceMemory < 4 || isSlowConnection;
};

// Optimize animations for low-end devices
export const getOptimizedAnimationConfig = () => {
  const isLowEnd = isLowEndDevice();

  return {
    duration: isLowEnd ? 0.2 : 0.5,
    ease: isLowEnd ? 'linear' : 'easeOut',
    stagger: isLowEnd ? 0.05 : 0.1,
    scale: isLowEnd ? { min: 0.98, max: 1.02 } : { min: 0.95, max: 1.05 }
  };
};

// Resource hints for better loading
export const addResourceHints = () => {
  const hints = [
    { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
    { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
    { rel: 'dns-prefetch', href: '//www.google-analytics.com' },
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true }
  ];

  hints.forEach(hint => {
    const link = document.createElement('link');
    Object.keys(hint).forEach(key => {
      if (key === 'crossorigin') {
        link.crossOrigin = hint[key];
      } else {
        link[key] = hint[key];
      }
    });
    document.head.appendChild(link);
  });
};

// Critical CSS inlining
export const inlineCriticalCSS = (css) => {
  const style = document.createElement('style');
  style.textContent = css;
  document.head.appendChild(style);
};

// Service Worker registration for caching
export const registerServiceWorker = () => {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          // SW registration success - handle updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content available
                  if (window.confirm('Có phiên bản mới của ứng dụng. Bạn có muốn tải lại không?')) {
                    window.location.reload();
                  }
                }
              });
            }
          });

          // Listen for messages from service worker
          navigator.serviceWorker.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'SW_UPDATE') {
              // Handle service worker updates
              window.location.reload();
            }
          });

          // Check for updates periodically
          setInterval(() => {
            registration.update();
          }, 60000); // Check every minute
        })
        .catch(registrationError => {
          // SW registration failed - silent fail in production
          if (process.env.NODE_ENV === 'development') {
            console.warn('SW registration failed:', registrationError);
          }
        });
    });
  }
};

// Measure and report performance metrics
export const measurePerformance = () => {
  if (typeof window === 'undefined' || !window.performance) return;

  window.addEventListener('load', () => {
    setTimeout(() => {
      const perfData = window.performance.timing;
      const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
      const domReadyTime = perfData.domContentLoadedEventEnd - perfData.navigationStart;
      const firstPaintTime = window.performance.getEntriesByType('paint')[0]?.startTime || 0;

      // Performance metrics logging removed to reduce console spam

      // Send to analytics if needed
      if (window.gtag) {
        window.gtag('event', 'page_load_time', {
          value: Math.round(pageLoadTime),
          custom_parameter: 'performance'
        });
      }
    }, 0);
  });
};

// Initialize all performance optimizations
export const initializePerformanceOptimizations = () => {
  // Disable web vitals logging first
  disableWebVitalsLogging();
  
  preloadCriticalResources();
  optimizeFontLoading();
  addResourceHints();
  lazyLoadResources();
  measurePerformance();
  trackWebVitals();

  // Register service worker in production
  if (process.env.NODE_ENV === 'production') {
    registerServiceWorker();
  }
};
