import React from 'react';
import { classNames } from '../../utils/classNames';

/**
 * Button component có thể tái sử dụng
 */
const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  rounded = true,
  className,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium focus:outline-none transition-colors font-dexin font-bold';

  const variantClasses = {
    primary: 'bg-dexin-light text-white hover:bg-dexin-dark hover:text-dexin-light hover:border-2 hover:border-dexin-light transition-colors',
    secondary: 'bg-white text-dexin-light border border-dexin-light hover:bg-dexin-light hover:text-white',
    outline: 'bg-transparent text-dexin-light border border-dexin-light hover:border-dexin-primary hover:text-dexin-primary',
    ghost: 'bg-transparent text-dexin-primary hover:bg-dexin-primary/10',
    bolded: 'bg-dexin-primary text-white px-5 py-3 rounded-full font-medium hover:border-2 hover:border-dexin-light hover:text-dexin-light transition-colors hover:bg-dexin-dark transition-colors whitespace-nowrap',
    click: 'bg-dexin-light text-white active:bg-dexin-dark active:text-dexin-light active:border-2 active:border-dexin-light',
  };

  const sizeClasses = {
    sm: 'px-4 py-1.5 text-sm',
    md: 'px-8 py-2',
    lg: 'px-6 py-3 text-lg',
  };

  const roundedClasses = rounded ? 'rounded-full' : 'rounded-md';

  return (
    <button
      className={classNames(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        roundedClasses,
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;