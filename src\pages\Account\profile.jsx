import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { motion } from 'motion/react';
import { User, Mail, Phone, Edit2, Save, X } from 'lucide-react';
import { toast } from 'react-toastify';
import AvatarUpload from '../../components/common/AvatarUpload';

const Profile = () => {
  const { user, updateUser, loading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    userName: user?.userName || ''
  });

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setFormData({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      userName: user?.userName || ''
    });
    setIsEditing(false);
  };

  const handleSave = async () => {
    // Validate form
    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      toast.error('Vui lòng nhập đầy đủ họ và tên');
      return;
    }

    if (!formData.email.trim()) {
      toast.error('Vui lòng nhập email');
      return;
    }

    setIsSaving(true);

    try {
      // Gọi API để cập nhật thông tin
      const result = await updateUser({
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        phone: formData.phone.trim()
        // Email và userName không được cập nhật
      });

      if (result.success) {
        setIsEditing(false);
        toast.success(result.message || 'Cập nhật thông tin thành công!');
      } else {
        toast.error(result.message || 'Có lỗi xảy ra khi cập nhật thông tin');
      }
    } catch (error) {
      console.error('Update user error:', error);
      toast.error('Có lỗi xảy ra. Vui lòng thử lại.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex items-center space-x-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-dexin-primary"></div>
          <span className="text-gray-600">Đang tải...</span>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-lg shadow-lg overflow-hidden"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-8">
        <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
          {/* Avatar Upload */}
          <div className="flex-shrink-0">
            <AvatarUpload
              size="lg"
              onUploadSuccess={() => {
                toast.success('Avatar đã được cập nhật!');
              }}
              onUploadError={(error) => {
                toast.error(error.message || 'Có lỗi xảy ra khi upload avatar');
              }}
            />
          </div>

          {/* User Info */}
          <div className="text-white text-center md:text-left">
            <h1 className="text-2xl font-bold">
              {user?.firstName} {user?.lastName}
            </h1>
            <p className="text-dexin-bg">@{user?.userName}</p>
            <p className="text-sm text-dexin-bg capitalize">{user?.role}</p>
            {user?.email && (
              <p className="text-sm text-dexin-bg mt-1">{user?.email}</p>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Thông tin tài khoản</h2>
          {!isEditing ? (
            <motion.button
              onClick={handleEdit}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-dexin-light text-white rounded-lg hover:bg-dexin-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              whileHover={{ scale: loading ? 1 : 1.05 }}
              whileTap={{ scale: loading ? 1 : 0.95 }}
            >
              <Edit2 className="w-4 h-4" />
              <span>Chỉnh sửa</span>
            </motion.button>
          ) : (
            <div className="flex space-x-2">
              <motion.button
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: isSaving ? 1 : 1.05 }}
                whileTap={{ scale: isSaving ? 1 : 0.95 }}
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Đang lưu...</span>
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    <span>Lưu</span>
                  </>
                )}
              </motion.button>
              <motion.button
                onClick={handleCancel}
                disabled={isSaving}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: isSaving ? 1 : 1.05 }}
                whileTap={{ scale: isSaving ? 1 : 0.95 }}
              >
                <X className="w-4 h-4" />
                <span>Hủy</span>
              </motion.button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Họ */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Họ</label>
            {isEditing ? (
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                disabled={isSaving}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary disabled:bg-gray-50 disabled:cursor-not-allowed"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <User className="w-5 h-5 text-gray-400" />
                <span>{user?.firstName}</span>
              </div>
            )}
          </div>

          {/* Tên */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Tên</label>
            {isEditing ? (
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                disabled={isSaving}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary disabled:bg-gray-50 disabled:cursor-not-allowed"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <User className="w-5 h-5 text-gray-400" />
                <span>{user?.lastName}</span>
              </div>
            )}
          </div>

          {/* Username */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Tên người dùng</label>
            <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
              <User className="w-5 h-5 text-gray-400" />
              <span>@{user?.userName}</span>
              <span className="text-xs text-gray-500">(Không thể thay đổi)</span>
            </div>
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
              <Mail className="w-5 h-5 text-gray-400" />
              <span>{user?.email}</span>
              <span className="text-xs text-gray-500">(Không thể thay đổi)</span>
            </div>
          </div>

          {/* Phone */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Số điện thoại</label>
            {isEditing ? (
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                disabled={isSaving}
                placeholder="Nhập số điện thoại"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary disabled:bg-gray-50 disabled:cursor-not-allowed"
              />
            ) : (
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <Phone className="w-5 h-5 text-gray-400" />
                <span>{user?.phone || 'Chưa cập nhật'}</span>
              </div>
            )}
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Thông tin khác</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-500">Giới tính</p>
              <p className="font-medium">
                {user?.gender === 'female' ? 'Nữ' : user?.gender === 'male' ? 'Nam' : 'Khác'}
              </p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-500">Vai trò</p>
              <p className="font-medium capitalize">{user?.role}</p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default Profile;
