import React, { useState, useCallback, useRef } from 'react';
import { motion } from 'motion/react';
import { Image, Paperclip, Send, Mic } from 'lucide-react';
import { toast } from 'react-toastify';
import EmojiPickerComponent from './EmojiPicker';
import FileUpload from './FileUpload';
import ImageUpload from './ImageUpload';

const ChatInput = React.memo(({ onSendMessage }) => {
  const [inputValue, setInputValue] = useState('');
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
  const [isFileUploadOpen, setIsFileUploadOpen] = useState(false);
  const [isImageUploadOpen, setIsImageUploadOpen] = useState(false);

  // Refs cho các button để xử lý click outside
  const emojiButtonRef = useRef(null);
  const fileButtonRef = useRef(null);
  const imageButtonRef = useRef(null);

  const handleInputChange = useCallback((e) => {
    setInputValue(e.target.value);
  }, []);

  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    if (inputValue.trim() === '') return;
    onSendMessage({
      type: 'text',
      content: inputValue,
      timestamp: new Date().toISOString()
    });
    setInputValue('');
  }, [inputValue, onSendMessage]);

  // Xử lý chọn emoji
  const handleEmojiClick = useCallback((emoji) => {
    setInputValue(prev => prev + emoji);
    toast.success('Đã thêm emoji!');
  }, []);

  // Xử lý gửi file
  const handleFileSelect = useCallback((files) => {
    onSendMessage({
      type: 'files',
      content: files,
      timestamp: new Date().toISOString()
    });
    toast.success(`Đã gửi ${files.length} file!`);
  }, [onSendMessage]);

  // Xử lý gửi hình ảnh
  const handleImageSelect = useCallback((images) => {
    onSendMessage({
      type: 'images',
      content: images,
      timestamp: new Date().toISOString()
    });
    toast.success(`Đã gửi ${images.length} hình ảnh!`);
  }, [onSendMessage]);

  // Toggle các modal
  const toggleEmojiPicker = useCallback(() => {
    setIsEmojiPickerOpen(prev => !prev);
    setIsFileUploadOpen(false);
    setIsImageUploadOpen(false);
  }, []);

  const toggleFileUpload = useCallback(() => {
    setIsFileUploadOpen(prev => !prev);
    setIsEmojiPickerOpen(false);
    setIsImageUploadOpen(false);
  }, []);

  const toggleImageUpload = useCallback(() => {
    setIsImageUploadOpen(prev => !prev);
    setIsEmojiPickerOpen(false);
    setIsFileUploadOpen(false);
  }, []);

  return (
    <motion.form
      onSubmit={handleSubmit}
      className="bg-white border-t border-dexin-light p-3 sm:p-5 relative"
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center w-full">
        <div className="flex space-x-3 sm:space-x-5 mr-3 sm:mr-4 flex-shrink-0 relative">
          {/* Emoji/Sticker Button */}
          <motion.button
            ref={emojiButtonRef}
            type="button"
            onClick={toggleEmojiPicker}
            className={`text-dexin-light hover:text-dexin-primary transition-colors ${
              isEmojiPickerOpen ? 'text-dexin-primary' : ''
            }`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <motion.img
              src="/images/ty_chat_icon.png"
              alt="Emoji & Sticker"
              className="w-6 h-6 sm:h-6 sm:w-6 object-contain"
              whileHover={{ rotate: 10 }}
            />
          </motion.button>

          {/* Image Upload Button */}
          <motion.button
            ref={imageButtonRef}
            type="button"
            onClick={toggleImageUpload}
            className={`text-dexin-light hover:text-dexin-primary transition-colors ${
              isImageUploadOpen ? 'text-dexin-primary' : ''
            }`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Image className="h-6 w-6 sm:h-6 sm:w-6" stroke={isImageUploadOpen ? "#FE7CAB" : "#FE7CAB"} />
          </motion.button>

          {/* File Upload Button */}
          <motion.button
            ref={fileButtonRef}
            type="button"
            onClick={toggleFileUpload}
            className={`text-dexin-light hover:text-dexin-primary transition-colors ${
              isFileUploadOpen ? 'text-dexin-primary' : ''
            }`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Paperclip className="h-6 w-6 sm:h-6 sm:w-6" stroke={isFileUploadOpen ? "#FE7CAB" : "#FE7CAB"} />
          </motion.button>

          {/* Emoji Picker */}
          <EmojiPickerComponent
            isOpen={isEmojiPickerOpen}
            onClose={() => setIsEmojiPickerOpen(false)}
            onEmojiClick={handleEmojiClick}
            buttonRef={emojiButtonRef}
          />
        </div>
        <motion.div
          className="flex flex-1 bg-dexin-light-10 rounded-full items-center overflow-hidden min-w-0"
          whileHover={{ boxShadow: "0 2px 10px rgba(254, 124, 171, 0.3)" }}
        >
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            placeholder="Nhập tin nhắn..."
            className="flex-1 bg-transparent py-2 sm:py-3 px-3 sm:px-4 focus:outline-none text-xs sm:text-sm min-w-0 w-full"
            autoComplete="off"
          />
        </motion.div>
        <motion.button
          type="submit"
          className="ml-2 sm:ml-4 w-11 h-11 sm:w-12 sm:h-12 rounded-full flex items-center justify-center bg-dexin-primary text-white shadow-md hover:bg-dexin-light-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
          disabled={inputValue.trim() === ''}
          whileHover={{ scale: 1.05, boxShadow: "0 3px 15px rgba(254, 124, 171, 0.5)" }}
          whileTap={{ scale: 0.95 }}
          transition={{ duration: 0.2 }}
        >
          <Send className="h-5 w-5 sm:h-5 sm:w-5" />
        </motion.button>
      </div>

      {/* File Upload Modal */}
      <FileUpload
        isOpen={isFileUploadOpen}
        onClose={() => setIsFileUploadOpen(false)}
        onFileSelect={handleFileSelect}
        buttonRef={fileButtonRef}
      />

      {/* Image Upload Modal */}
      <ImageUpload
        isOpen={isImageUploadOpen}
        onClose={() => setIsImageUploadOpen(false)}
        onImageSelect={handleImageSelect}
        buttonRef={imageButtonRef}
      />
    </motion.form>
  );
});

export default ChatInput;