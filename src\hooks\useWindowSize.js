import { useState, useEffect } from 'react';

/**
 * Hook theo dõi kích thước cửa sổ
 * @returns {Object} - <PERSON><PERSON><PERSON> tư<PERSON> { width, height } chứa kích thước cửa sổ hiện tại
 */
function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: undefined,
    height: undefined,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }
    
    // Thêm event listener
    window.addEventListener('resize', handleResize);
    
    // G<PERSON><PERSON> hàm xử lý để set kích thước ban đầu
    handleResize();
    
    // Xóa event listener khi unmount
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
}

export default useWindowSize; 