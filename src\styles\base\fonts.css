@font-face {
  font-family: 'BDLifelessGrotesk';
  src: url('../../assets/fonts/BDLifelessGrotesk-Thin.otf') format('opentype');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BDLifelessGrotesk';
  src: url('../../assets/fonts/BDLifelessGrotesk-ExtraLight.otf') format('opentype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BDLifelessGrotesk';
  src: url('../../assets/fonts/BDLifelessGrotesk-Light.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BDLifelessGrotesk';
  src: url('../../assets/fonts/BDLifelessGrotesk-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BDLifelessGrotesk';
  src: url('../../assets/fonts/BDLifelessGrotesk-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BDLifelessGrotesk';
  src: url('../../assets/fonts/BDLifelessGrotesk-SemiBold.otf') format('opentype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BDLifelessGrotesk';
  src: url('../../assets/fonts/BDLifelessGrotesk-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BDLifelessGrotesk';
  src: url('../../assets/fonts/BDLifelessGrotesk-ExtraBold.otf') format('opentype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'BDLifelessGrotesk';
  src: url('../../assets/fonts/BDLifelessGrotesk-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
} 