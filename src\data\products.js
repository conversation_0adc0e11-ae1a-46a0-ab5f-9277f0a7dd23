// Danh sách sản phẩm với thông tin variants đầy đủ
export const productData = [
  {
    id: 1,
    name: '<PERSON>àn gỗ',
    description: '<PERSON>àn gỗ cao cấp với thiết kế hiện đại, hoàn hảo cho phòng khách hoặc văn phòng.',
    price: 3250000,
    image: '/images/shop/image 2.png',
    rating: 4.8,
    reviews: 128,
    category: 'ban',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế khung gỗ chắc chắn, thiết kế thanh mảnh với mặt bàn rộng.' },
      { label: '<PERSON><PERSON><PERSON> sắc', value: '<PERSON><PERSON><PERSON> gỗ tự nhiên, được phủ lớp sơn bảo vệ.' },
      { label: 'Kích thước', value: 'Dài 120cm x Rộng 60cm x Cao 75cm, phù hợp với nhiều không gian.' }
    ],
    variants: [
      { id: 1, name: 'Gỗ sồi', color: '#C19A6B' },
      { id: 2, name: 'Gỗ óc chó', color: '#5C4033' },
      { id: 3, name: 'Gỗ thông', color: '#DEB887' }
    ],
    images: [
      '/images/shop/image 2.png',
      '/images/shop/image 3.png'
    ]
  },
  {
    id: 2,
    name: 'Bàn tròn',
    description: 'Bàn tròn với chân gỗ chắc chắn, phù hợp với mọi không gian.',
    price: 2750000,
    image: '/images/shop/image 3.png',
    rating: 4.5,
    reviews: 96,
    category: 'ban',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế khung gỗ tròn chắc chắn với mặt bàn tròn.' },
      { label: 'Màu sắc', value: 'Mặt bàn màu nâu, chân gỗ màu đen.' },
      { label: 'Kích thước', value: 'Đường kính 60cm, cao 45cm.' }
    ],
    variants: [
      { id: 1, name: 'Đen', color: '#4A3520' },
      { id: 2, name: 'Nâu', color: '#D2901F' },
      { id: 3, name: 'Be', color: '#DFCDB4' }
    ],
    images: [
      '/images/shop/image 3.png',
      '/images/shop/image 2.png'
    ]
  },
  {
    id: 3,
    name: 'Ghế văn phòng',
    description: 'Ghế văn phòng tiện nghi, thiết kế công thái học giúp giảm mệt mỏi khi ngồi lâu.',
    price: 3450000,
    image: '/images/shop/image 4.png',
    rating: 4.7,
    reviews: 112,
    category: 'ghe',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế công thái học, có thể điều chỉnh độ cao và góc ngả.' },
      { label: 'Màu sắc', value: 'Đen, với phần đệm êm ái và thoáng khí.' },
      { label: 'Kích thước', value: 'Cao 110-120cm (có thể điều chỉnh), rộng 60cm.' }
    ],
    variants: [
      { id: 1, name: 'Đen', color: '#000000' },
      { id: 2, name: 'Xám', color: '#808080' }
    ],
    images: [
      '/images/shop/image 4.png'
    ]
  },
  {
    id: 4,
    name: 'Ghế gỗ',
    description: 'Ghế gỗ cao cấp với thiết kế tối giản, phù hợp cho phòng ăn hoặc góc học tập.',
    price: 4250000,
    image: '/images/shop/image 5.png',
    rating: 4.9,
    reviews: 156,
    category: 'ghe',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế tối giản, gọn gàng và hiện đại.' },
      { label: 'Màu sắc', value: 'Màu gỗ tự nhiên, đánh bóng nhẹ.' },
      { label: 'Kích thước', value: 'Cao 85cm, rộng 45cm, phù hợp với bàn cao 75cm.' }
    ],
    variants: [
      { id: 1, name: 'Gỗ sồi', color: '#C19A6B' },
      { id: 2, name: 'Gỗ óc chó', color: '#5C4033' }
    ],
    images: [
      '/images/shop/image 5.png'
    ]
  },
  {
    id: 5,
    name: 'Đèn bàn',
    description: 'Đèn bàn LED hiện đại, tiết kiệm điện và bảo vệ thị lực.',
    price: 850000,
    image: '/images/shop/image 6.png',
    rating: 4.6,
    reviews: 78,
    category: 'den',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế mảnh mai, điều chỉnh được góc chiếu.' },
      { label: 'Màu sắc', value: 'Trắng tinh tế, phần thân kim loại.' },
      { label: 'Công suất', value: '8W, 3 chế độ sáng, bảo vệ thị lực.' }
    ],
    variants: [
      { id: 1, name: 'Trắng', color: '#FFFFFF' },
      { id: 2, name: 'Đen', color: '#000000' }
    ],
    images: [
      '/images/shop/image 6.png'
    ]
  },
  {
    id: 6,
    name: 'Đèn sàn',
    description: 'Đèn sàn thiết kế thanh lịch, phù hợp với phòng khách và phòng đọc sách.',
    price: 1250000,
    image: '/images/shop/image 7.png',
    rating: 4.4,
    reviews: 64,
    category: 'den',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế thanh lịch, chân kim loại cao cấp.' },
      { label: 'Màu sắc', value: 'Đen mờ, phù hợp với nhiều phong cách nội thất.' },
      { label: 'Công suất', value: '12W, có thể điều chỉnh cường độ ánh sáng.' }
    ],
    variants: [
      { id: 1, name: 'Đen', color: '#000000' },
      { id: 2, name: 'Bạc', color: '#C0C0C0' }
    ],
    images: [
      '/images/shop/image 7.png'
    ]
  },
  {
    id: 7,
    name: 'Ghế sofa',
    description: 'Ghế sofa êm ái, sang trọng, là điểm nhấn hoàn hảo cho phòng khách hiện đại.',
    price: 12500000,
    image: '/images/shop/image 13.png',
    rating: 4.9,
    reviews: 203,
    category: 'ghe',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế sang trọng, đường nét tinh tế, đệm êm ái.' },
      { label: 'Màu sắc', value: 'Xám trung tính, dễ dàng kết hợp với nội thất khác.' },
      { label: 'Kích thước', value: 'Dài 220cm, rộng 85cm, cao 80cm.' }
    ],
    variants: [
      { id: 1, name: 'Xám', color: '#808080' },
      { id: 2, name: 'Xanh navy', color: '#000080' },
      { id: 3, name: 'Kem', color: '#FFFDD0' }
    ],
    images: [
      '/images/shop/image 13.png'
    ]
  },
  {
    id: 8,
    name: 'Kệ sách',
    description: 'Kệ sách thiết kế mở, hiện đại và đa năng cho không gian sống.',
    price: 2150000,
    image: '/images/shop/image 67.png',
    rating: 4.7,
    reviews: 89,
    category: 'ke',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế mở, nhiều ngăn chứa linh hoạt.' },
      { label: 'Màu sắc', value: 'Trắng tinh tế, kết hợp với gỗ tự nhiên.' },
      { label: 'Kích thước', value: 'Cao 180cm, rộng 100cm, sâu 35cm.' }
    ],
    variants: [
      { id: 1, name: 'Trắng', color: '#FFFFFF' },
      { id: 2, name: 'Đen', color: '#000000' },
      { id: 3, name: 'Gỗ tự nhiên', color: '#DEB887' }
    ],
    images: [
      '/images/shop/image 67.png'
    ]
  },
  {
    id: 9,
    name: 'Tủ quần áo',
    description: 'Tủ quần áo rộng rãi với thiết kế tinh tế, nhiều ngăn chứa tiện dụng.',
    price: 5750000,
    image: '/images/shop/image 72.png',
    rating: 4.8,
    reviews: 124,
    category: 'tu',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế hiện đại, nhiều ngăn chứa và thanh treo.' },
      { label: 'Màu sắc', value: 'Màu gỗ sáng, tạo cảm giác rộng rãi, thoáng đãng.' },
      { label: 'Kích thước', value: 'Cao 200cm, rộng 150cm, sâu 60cm.' }
    ],
    variants: [
      { id: 1, name: 'Gỗ sáng', color: '#DEB887' },
      { id: 2, name: 'Gỗ tối', color: '#5C4033' },
      { id: 3, name: 'Trắng', color: '#FFFFFF' }
    ],
    images: [
      '/images/shop/image 72.png'
    ]
  },
  {
    id: 10,
    name: 'Bàn trang điểm',
    description: 'Bàn trang điểm với gương lớn và không gian lưu trữ mỹ phẩm tối ưu.',
    price: 3850000,
    image: '/images/shop/image 73.png',
    rating: 4.6,
    reviews: 92,
    category: 'ban',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế thanh lịch với gương lớn và các ngăn kéo tiện dụng.' },
      { label: 'Màu sắc', value: 'Trắng tinh tế, phù hợp với không gian phòng ngủ.' },
      { label: 'Kích thước', value: 'Rộng 120cm, sâu 45cm, cao 140cm (bao gồm gương).' }
    ],
    variants: [
      { id: 1, name: 'Trắng', color: '#FFFFFF' },
      { id: 2, name: 'Hồng pastel', color: '#FFD1DC' }
    ],
    images: [
      '/images/shop/image 73.png'
    ]
  },
  {
    id: 11,
    name: 'Kệ TV',
    description: 'Kệ TV hiện đại với thiết kế đa năng, tích hợp nhiều không gian lưu trữ.',
    price: 2950000,
    image: '/images/shop/image 78.png',
    rating: 4.5,
    reviews: 76,
    category: 'ke',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế tối giản, nhiều ngăn chứa tiện dụng.' },
      { label: 'Màu sắc', value: 'Gỗ tự nhiên kết hợp màu trắng, tạo điểm nhấn.' },
      { label: 'Kích thước', value: 'Dài 160cm, sâu 40cm, cao 45cm.' }
    ],
    variants: [
      { id: 1, name: 'Gỗ-Trắng', color: '#F5F5DC' },
      { id: 2, name: 'Gỗ-Đen', color: '#5C4033' }
    ],
    images: [
      '/images/shop/image 78.png'
    ]
  },
  {
    id: 12,
    name: 'Bàn cà phê',
    description: 'Bàn cà phê thiết kế tối giản, là điểm nhấn hoàn hảo cho phòng khách.',
    price: 1850000,
    image: '/images/shop/image 79.png',
    rating: 4.7,
    reviews: 108,
    category: 'ban',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế tối giản, mặt bàn hình chữ nhật.' },
      { label: 'Màu sắc', value: 'Mặt gỗ sáng màu, chân kim loại màu đen.' },
      { label: 'Kích thước', value: 'Dài 100cm, rộng 60cm, cao 40cm.' }
    ],
    variants: [
      { id: 1, name: 'Gỗ sáng', color: '#DEB887' },
      { id: 2, name: 'Gỗ tối', color: '#5C4033' },
      { id: 3, name: 'Đá cẩm thạch', color: '#F5F5DC' }
    ],
    images: [
      '/images/shop/image 79.png'
    ]
  },
  {
    id: 13,
    name: 'Giường ngủ',
    description: 'Giường ngủ sang trọng với đầu giường êm ái, tạo không gian nghỉ ngơi thoải mái.',
    price: 8500000,
    image: '/images/shop/image 80.png',
    rating: 4.9,
    reviews: 187,
    category: 'giuong',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế hiện đại với đầu giường êm ái, bọc vải cao cấp.' },
      { label: 'Màu sắc', value: 'Xám trung tính, dễ dàng kết hợp với các mảnh nội thất khác.' },
      { label: 'Kích thước', value: 'Rộng 160cm (Queen size), dài 200cm, cao 40cm (không bao gồm đầu giường).' }
    ],
    variants: [
      { id: 1, name: 'Xám', color: '#808080' },
      { id: 2, name: 'Xanh navy', color: '#000080' },
      { id: 3, name: 'Be', color: '#DFCDB4' }
    ],
    images: [
      '/images/shop/image 80.png'
    ]
  },
  {
    id: 14,
    name: 'Tủ đầu giường',
    description: 'Tủ đầu giường nhỏ gọn với ngăn kéo tiện dụng, hoàn hảo cho phòng ngủ.',
    price: 1650000,
    image: '/images/shop/image 81.png',
    rating: 4.6,
    reviews: 94,
    category: 'tu',
    details: [
      { label: 'Hình dáng', value: 'Thiết kế nhỏ gọn với ngăn kéo và kệ mở.' },
      { label: 'Màu sắc', value: 'Gỗ sáng màu, tạo cảm giác ấm cúng.' },
      { label: 'Kích thước', value: 'Rộng 45cm, sâu 35cm, cao 55cm.' }
    ],
    variants: [
      { id: 1, name: 'Gỗ sáng', color: '#DEB887' },
      { id: 2, name: 'Gỗ tối', color: '#5C4033' },
      { id: 3, name: 'Trắng', color: '#FFFFFF' }
    ],
    images: [
      '/images/shop/image 81.png'
    ]
  }
];

// Hàm tìm sản phẩm theo ID
export const getProductById = (id) => {
  return productData.find(product => product.id === parseInt(id));
};

// Hàm lấy tất cả sản phẩm
export const getAllProducts = () => {
  return productData;
}; 