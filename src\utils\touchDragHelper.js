/**
 * Touch Drag and Drop Helper Utilities
 * Hỗ trợ drag and drop trên mobile devices cho PhacY page
 */

// Utility để detect touch device
export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// Utility để tính khoảng cách giữa hai điểm
export const getDistance = (point1, point2) => {
  const deltaX = point2.x - point1.x;
  const deltaY = point2.y - point1.y;
  return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
};

// Utility để trigger haptic feedback
export const triggerHapticFeedback = (duration = 50) => {
  if (navigator.vibrate) {
    navigator.vibrate(duration);
  }
};

// Utility để tạo synthetic drag event từ touch event
export const createSyntheticDragEvent = (item, x, y, targetElement) => {
  return {
    preventDefault: () => {},
    currentTarget: targetElement,
    clientX: x,
    clientY: y,
    dataTransfer: {
      getData: () => JSON.stringify(item)
    }
  };
};

// Utility để check xem element có phải là canvas container không
export const isCanvasContainer = (element) => {
  const canvasContainer = document.querySelector('[data-canvas-container="true"]');
  return canvasContainer && (canvasContainer.contains(element) || element === canvasContainer);
};

// Constants cho drag behavior
export const DRAG_CONSTANTS = {
  DRAG_THRESHOLD: 10, // Minimum distance to start drag
  HAPTIC_START: 50,   // Haptic feedback duration on drag start
  HAPTIC_DROP: 100,   // Haptic feedback duration on successful drop
  PREVIEW_SIZE: 64,   // Size of drag preview in pixels
};

// Debug utility để log touch events (chỉ trong development)
export const logTouchEvent = (eventType, data) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[TouchDrag] ${eventType}:`, data);
  }
};
