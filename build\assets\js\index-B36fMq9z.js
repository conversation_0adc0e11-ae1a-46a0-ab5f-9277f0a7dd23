import{c as e,N as s,z as a,j as t,m as i,X as n,O as l,P as r,Q as c,U as d,T as o,v as h,V as x,W as m,y as g,Y as u,r as p,$ as y,a0 as b,A as j,k as f,a1 as N,a2 as v,n as w,l as k,a3 as C,a4 as T,C as D,a5 as S,E as V,L as H,a6 as M,D as U,a7 as $,a8 as P,a9 as L,S as A,aa as B}from"./index-DdBL2cja.js";import{B as z,d as q}from"./designService--nHfI8ad.js";import{C as F}from"./check-DdKjhOwX.js";import{P as Q,L as E}from"./pen-C65N3AwY.js";import{P as K}from"./plus-66Jg-RVc.js";import{T as I}from"./trash-2-1tdRbsHn.js";import{S as O}from"./share-2-Cf5bFpV9.js";
/**
 * @license lucide-react v0.484.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=e("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),R=e("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),G=e("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),W=e("phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),_=e("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),J=e("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]]),Y=({activeSection:e,setActiveSection:u,isSidebarOpen:p,setIsSidebarOpen:y})=>{const{user:b,logout:j,getDisplayName:f}=s(),N=a(),v=[{id:"profile",label:"Thông tin cá nhân",icon:d,description:"Quản lý thông tin tài khoản"},{id:"address",label:"Địa chỉ",icon:o,description:"Quản lý địa chỉ giao hàng"},{id:"orders",label:"Đơn hàng",icon:h,description:"Lịch sử mua hàng"},{id:"designs",label:"Bản thiết kế",icon:x,description:"Các thiết kế đã lưu"}];return t.jsxs(t.Fragment,{children:[p&&t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:()=>y(!1)}),t.jsx(i.div,{initial:{x:-300},animate:{x:p?0:-300},transition:{duration:.3,ease:"easeInOut"},className:`\n          fixed md:relative top-0 left-0 h-full md:h-auto\n          w-80 md:w-full bg-white shadow-lg md:shadow-none\n          z-50 md:z-auto overflow-y-auto\n          ${p?"block":"hidden md:block"}\n        `,children:t.jsxs("div",{className:"p-6",children:[t.jsxs("div",{className:"flex items-center justify-between mb-6 md:hidden",children:[t.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Menu tài khoản"}),t.jsx("button",{onClick:()=>y(!1),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:t.jsx(n,{className:"w-5 h-5 text-gray-500"})})]}),t.jsx("div",{className:"bg-gradient-to-r from-dexin-primary to-dexin-light rounded-lg p-4 mb-6",children:t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsxs(l,{className:"w-12 h-12 border-2 border-white",children:[t.jsx(r,{src:(null==b?void 0:b.avatar)&&"default_avatar.png"!==b.avatar?b.avatar:void 0,alt:f()||"User Avatar"}),t.jsx(c,{className:"bg-white text-dexin-primary text-sm font-medium",children:(w=f(),w?w.split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2):"U")})]}),t.jsxs("div",{className:"text-white",children:[t.jsx("h3",{className:"font-semibold text-sm",children:f()||`${null==b?void 0:b.firstName} ${null==b?void 0:b.lastName}`}),t.jsxs("p",{className:"text-xs text-dexin-bg",children:["@",null==b?void 0:b.userName]})]})]})}),t.jsx("nav",{className:"space-y-2",children:v.map((s=>{const a=s.icon,n=e===s.id;return t.jsxs(i.button,{onClick:()=>{return e=s.id,u(e),void(window.innerWidth<768&&y(!1));var e},className:`\n                    w-full text-left p-3 rounded-lg transition-all duration-200\n                    flex items-center space-x-3 group\n                    ${n?"bg-dexin-light-20 text-dexin-primary border-l-4 border-dexin-primary":"hover:bg-gray-50 text-gray-700 hover:text-dexin-primary"}\n                  `,whileHover:{scale:1.02},whileTap:{scale:.98},children:[t.jsx(a,{className:"w-5 h-5 "+(n?"text-dexin-primary":"text-gray-400 group-hover:text-dexin-primary")}),t.jsxs("div",{className:"flex-1",children:[t.jsx("div",{className:"font-medium "+(n?"text-dexin-primary":"text-gray-900"),children:s.label}),t.jsx("div",{className:"text-xs text-gray-500 mt-1",children:s.description})]})]},s.id)}))}),t.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200",children:t.jsxs(i.button,{onClick:async()=>{try{await j(),g.success("Đăng xuất thành công!"),N("/")}catch(e){g.error("Có lỗi xảy ra khi đăng xuất")}},className:"w-full flex items-center space-x-3 p-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors group",whileHover:{scale:1.02},whileTap:{scale:.98},children:[t.jsx(m,{className:"w-5 h-5"}),t.jsx("span",{className:"font-medium",children:"Đăng xuất"})]})})]})})]});var w},Z=["image/jpeg","image/jpg","image/png","image/webp"],ee=5242880,se=e=>{e&&e.startsWith("blob:")&&URL.revokeObjectURL(e)},ae=e=>{if(0===e)return"0 Bytes";const s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},te=({size:e="lg",showUploadButton:a=!0,className:d="",onUploadSuccess:o,onUploadError:h})=>{const{user:x,uploadAvatar:m,loading:N,getDisplayName:v,getAvatarUrl:w}=s(),[k,C]=p.useState(null),[T,D]=p.useState(null),[S,V]=p.useState(!1),[H,M]=p.useState(!1),U={sm:{avatar:"w-16 h-16",button:"w-6 h-6",icon:"w-3 h-3"},md:{avatar:"w-24 h-24",button:"w-8 h-8",icon:"w-4 h-4"},lg:{avatar:"w-32 h-32",button:"w-10 h-10",icon:"w-5 h-5"},xl:{avatar:"w-40 h-40",button:"w-12 h-12",icon:"w-6 h-6"}},$=U[e]||U.lg,P=p.useCallback((async e=>{const s=e[0];if(!s)return;const a=(e=>e?Z.includes(e.type)?e.size>ee?{isValid:!1,error:"File quá lớn. Vui lòng chọn file nhỏ hơn 5MB"}:{isValid:!0,error:null}:{isValid:!1,error:"Định dạng file không được hỗ trợ. Vui lòng chọn file JPG, PNG hoặc WebP"}:{isValid:!1,error:"Vui lòng chọn file ảnh"})(s);if(a.isValid)try{const e=await((e,s={})=>new Promise(((a,t)=>{const i={quality:.8,maxWidth:400,maxHeight:400,convertSize:1e6,...s};new u(e,{...i,success:e=>{a(e)},error:e=>{t(new Error("Không thể nén ảnh. Vui lòng thử lại."))}})})))(s),a=(e=>URL.createObjectURL(e))(e);D(e),C(a),g.success(`Đã chọn ảnh (${ae(e.size)})`)}catch(t){g.error(t.message||"Có lỗi xảy ra khi xử lý ảnh")}else g.error(a.error)}),[]),L=p.useCallback((async()=>{if(T){V(!0);try{const e=await m(T);e.success?(g.success(e.message),await new Promise((e=>setTimeout(e,500))),se(k),C(null),D(null),o&&o(e)):(g.error(e.message),h&&h(e))}catch(e){g.error("Có lỗi xảy ra khi upload ảnh"),h&&h({success:!1,message:e.message})}finally{V(!1)}}else g.error("Vui lòng chọn ảnh trước")}),[T,m,k,o,h]),A=p.useCallback((()=>{k&&se(k),C(null),D(null)}),[k]),{getRootProps:B,getInputProps:q,isDragActive:Q}=y({onDrop:P,accept:{"image/*":Z.map((e=>e.replace("image/",".")))},maxSize:ee,multiple:!1,onDragEnter:()=>M(!0),onDragLeave:()=>M(!1),onDropAccepted:()=>M(!1),onDropRejected:e=>{var s,a;M(!1);const t=e[0];"file-too-large"===(null==(s=t.errors[0])?void 0:s.code)?g.error("File quá lớn. Vui lòng chọn file nhỏ hơn 5MB"):"file-invalid-type"===(null==(a=t.errors[0])?void 0:a.code)?g.error("Định dạng file không được hỗ trợ"):g.error("File không hợp lệ")}}),E=k||w(),K=v();return t.jsxs("div",{className:`flex flex-col items-center space-y-4 ${d}`,children:[t.jsxs("div",{className:"relative group",children:[t.jsxs("div",{...B(),className:`\n            relative cursor-pointer transition-all duration-200\n            ${$.avatar}\n            ${Q||H?"scale-105 ring-4 ring-dexin-primary/30":""}\n          `,children:[t.jsx("input",{...q()}),t.jsxs(l,{className:`${$.avatar} border-4 border-white shadow-lg transition-all duration-200 group-hover:shadow-xl`,children:[t.jsx(r,{src:E,alt:K||"User Avatar",className:"object-cover"}),t.jsx(c,{className:"bg-dexin-primary text-white text-lg font-semibold",children:(I=K,I?I.split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2):"U")})]}),t.jsx("div",{className:"absolute inset-0 bg-black/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center",children:t.jsx(R,{className:`${$.icon} text-white`})}),t.jsx("div",{className:`absolute -bottom-1 -right-1 ${$.button} bg-dexin-primary rounded-full border-4 border-white shadow-lg flex items-center justify-center cursor-pointer hover:bg-dexin-primary/90 transition-colors`,children:t.jsx(b,{className:`${$.icon} text-white`})})]}),t.jsx(j,{children:(Q||H)&&t.jsx(i.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"absolute inset-0 bg-dexin-primary/20 rounded-full border-2 border-dashed border-dexin-primary flex items-center justify-center",children:t.jsx(b,{className:`${$.icon} text-dexin-primary`})})})]}),t.jsx(j,{children:T&&t.jsxs(i.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"flex flex-col items-center space-y-3 p-4 bg-gray-50 rounded-lg border",children:[t.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[t.jsx(f,{className:"w-4 h-4"}),t.jsx("span",{children:T.name}),t.jsxs("span",{children:["(",ae(T.size),")"]})]}),t.jsxs("div",{className:"flex space-x-2",children:[t.jsx(z,{onClick:L,disabled:S||N,size:"sm",className:"bg-dexin-primary hover:bg-dexin-primary/90",children:S?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Đang upload..."]}):t.jsxs(t.Fragment,{children:[t.jsx(F,{className:"w-4 h-4 mr-2"}),"Cập nhật"]})}),t.jsxs(z,{onClick:A,disabled:S,variant:"outline",size:"sm",children:[t.jsx(n,{className:"w-4 h-4 mr-2"}),"Hủy"]})]})]})})]});var I},ie=()=>{const{user:e,updateUser:a,loading:l}=s(),[r,c]=p.useState(!1),[o,h]=p.useState(!1),[x,m]=p.useState({firstName:(null==e?void 0:e.firstName)||"",lastName:(null==e?void 0:e.lastName)||"",email:(null==e?void 0:e.email)||"",phone:(null==e?void 0:e.phone)||"",userName:(null==e?void 0:e.userName)||""}),u=e=>{const{name:s,value:a}=e.target;m((e=>({...e,[s]:a})))};return l?t.jsx("div",{className:"flex items-center justify-center py-12",children:t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-dexin-primary"}),t.jsx("span",{className:"text-gray-600",children:"Đang tải..."})]})}):t.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[t.jsx("div",{className:"bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-8",children:t.jsxs("div",{className:"flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(te,{size:"lg",onUploadSuccess:()=>{g.success("Avatar đã được cập nhật!")},onUploadError:e=>{g.error(e.message||"Có lỗi xảy ra khi upload avatar")}})}),t.jsxs("div",{className:"text-white text-center md:text-left",children:[t.jsxs("h1",{className:"text-2xl font-bold",children:[null==e?void 0:e.firstName," ",null==e?void 0:e.lastName]}),t.jsxs("p",{className:"text-dexin-bg",children:["@",null==e?void 0:e.userName]}),t.jsx("p",{className:"text-sm text-dexin-bg capitalize",children:null==e?void 0:e.role}),(null==e?void 0:e.email)&&t.jsx("p",{className:"text-sm text-dexin-bg mt-1",children:null==e?void 0:e.email})]})]})}),t.jsxs("div",{className:"p-6",children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Thông tin tài khoản"}),r?t.jsxs("div",{className:"flex space-x-2",children:[t.jsx(i.button,{onClick:async()=>{if(x.firstName.trim()&&x.lastName.trim())if(x.email.trim()){h(!0);try{const e=await a({firstName:x.firstName.trim(),lastName:x.lastName.trim(),phone:x.phone.trim()});e.success?(c(!1),g.success(e.message||"Cập nhật thông tin thành công!")):g.error(e.message||"Có lỗi xảy ra khi cập nhật thông tin")}catch(e){g.error("Có lỗi xảy ra. Vui lòng thử lại.")}finally{h(!1)}}else g.error("Vui lòng nhập email");else g.error("Vui lòng nhập đầy đủ họ và tên")},disabled:o,className:"flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",whileHover:{scale:o?1:1.05},whileTap:{scale:o?1:.95},children:o?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),t.jsx("span",{children:"Đang lưu..."})]}):t.jsxs(t.Fragment,{children:[t.jsx(N,{className:"w-4 h-4"}),t.jsx("span",{children:"Lưu"})]})}),t.jsxs(i.button,{onClick:()=>{m({firstName:(null==e?void 0:e.firstName)||"",lastName:(null==e?void 0:e.lastName)||"",email:(null==e?void 0:e.email)||"",phone:(null==e?void 0:e.phone)||"",userName:(null==e?void 0:e.userName)||""}),c(!1)},disabled:o,className:"flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",whileHover:{scale:o?1:1.05},whileTap:{scale:o?1:.95},children:[t.jsx(n,{className:"w-4 h-4"}),t.jsx("span",{children:"Hủy"})]})]}):t.jsxs(i.button,{onClick:()=>{c(!0)},disabled:l,className:"flex items-center space-x-2 px-4 py-2 bg-dexin-light text-white rounded-lg hover:bg-dexin-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed",whileHover:{scale:l?1:1.05},whileTap:{scale:l?1:.95},children:[t.jsx(Q,{className:"w-4 h-4"}),t.jsx("span",{children:"Chỉnh sửa"})]})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Họ"}),r?t.jsx("input",{type:"text",name:"firstName",value:x.firstName,onChange:u,disabled:o,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary disabled:bg-gray-50 disabled:cursor-not-allowed"}):t.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 rounded-lg",children:[t.jsx(d,{className:"w-5 h-5 text-gray-400"}),t.jsx("span",{children:null==e?void 0:e.firstName})]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tên"}),r?t.jsx("input",{type:"text",name:"lastName",value:x.lastName,onChange:u,disabled:o,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary disabled:bg-gray-50 disabled:cursor-not-allowed"}):t.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 rounded-lg",children:[t.jsx(d,{className:"w-5 h-5 text-gray-400"}),t.jsx("span",{children:null==e?void 0:e.lastName})]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tên người dùng"}),t.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 rounded-lg",children:[t.jsx(d,{className:"w-5 h-5 text-gray-400"}),t.jsxs("span",{children:["@",null==e?void 0:e.userName]}),t.jsx("span",{className:"text-xs text-gray-500",children:"(Không thể thay đổi)"})]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),t.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 rounded-lg",children:[t.jsx(G,{className:"w-5 h-5 text-gray-400"}),t.jsx("span",{children:null==e?void 0:e.email}),t.jsx("span",{className:"text-xs text-gray-500",children:"(Không thể thay đổi)"})]})]}),t.jsxs("div",{className:"md:col-span-2",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số điện thoại"}),r?t.jsx("input",{type:"tel",name:"phone",value:x.phone,onChange:u,disabled:o,placeholder:"Nhập số điện thoại",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary disabled:bg-gray-50 disabled:cursor-not-allowed"}):t.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 rounded-lg",children:[t.jsx(W,{className:"w-5 h-5 text-gray-400"}),t.jsx("span",{children:(null==e?void 0:e.phone)||"Chưa cập nhật"})]})]})]}),t.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[t.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Thông tin khác"}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Giới tính"}),t.jsx("p",{className:"font-medium",children:"female"===(null==e?void 0:e.gender)?"Nữ":"male"===(null==e?void 0:e.gender)?"Nam":"Khác"})]}),t.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[t.jsx("p",{className:"text-sm text-gray-500",children:"Vai trò"}),t.jsx("p",{className:"font-medium capitalize",children:null==e?void 0:e.role})]})]})]})]})]})},ne=()=>{const[e,s]=p.useState([{id:1,name:"Nhà riêng",fullName:"Nguyễn Văn A",phone:"0123456789",address:"123 Nguyễn Văn Cừ, Phường 4",ward:"Phường 4",district:"Quận 5",city:"TP. Hồ Chí Minh",isDefault:!0,type:"home"},{id:2,name:"Văn phòng",fullName:"Nguyễn Văn A",phone:"0987654321",address:"456 Lê Văn Sỹ, Phường 12",ward:"Phường 12",district:"Quận 3",city:"TP. Hồ Chí Minh",isDefault:!1,type:"office"}]),[a,l]=p.useState(!1),[r,c]=p.useState(null),[d,h]=p.useState({name:"",fullName:"",phone:"",address:"",ward:"",district:"",city:"TP. Hồ Chí Minh",type:"home",isDefault:!1}),x=[{value:"home",label:"Nhà riêng",icon:v},{value:"office",label:"Văn phòng",icon:X}],m=e=>{const{name:s,value:a,type:t,checked:i}=e.target;h((e=>({...e,[s]:"checkbox"===t?i:a})))},u=()=>{l(!1),c(null),h({name:"",fullName:"",phone:"",address:"",ward:"",district:"",city:"TP. Hồ Chí Minh",type:"home",isDefault:!1})},y=e=>{const s=x.find((s=>s.value===e)),a=(null==s?void 0:s.icon)||v;return t.jsx(a,{className:"w-4 h-4"})};return t.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[t.jsx("div",{className:"bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("h2",{className:"text-xl font-bold text-white",children:"Địa chỉ giao hàng"}),t.jsx("p",{className:"text-dexin-bg text-sm mt-1",children:"Quản lý địa chỉ nhận hàng"})]}),t.jsxs("button",{onClick:()=>l(!0),className:"flex items-center space-x-2 px-4 py-2 bg-white text-dexin-primary rounded-lg hover:bg-gray-50 transition-colors",children:[t.jsx(K,{className:"w-4 h-4"}),t.jsx("span",{className:"font-medium",children:"Thêm địa chỉ"})]})]})}),t.jsxs("div",{className:"p-6",children:[t.jsx("div",{className:"space-y-4 mb-6",children:e.map((a=>t.jsx(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:t.jsxs("div",{className:"flex items-start justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[y(a.type),t.jsx("span",{className:"font-semibold text-gray-900",children:a.name}),a.isDefault&&t.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-dexin-light-20 text-dexin-primary",children:[t.jsx(_,{className:"w-3 h-3 mr-1"}),"Mặc định"]})]}),t.jsxs("div",{className:"text-gray-700 space-y-1",children:[t.jsx("p",{className:"font-medium",children:a.fullName}),t.jsxs("p",{className:"flex items-center space-x-1",children:[t.jsx(W,{className:"w-3 h-3"}),t.jsx("span",{children:a.phone})]}),t.jsxs("p",{className:"flex items-start space-x-1",children:[t.jsx(o,{className:"w-3 h-3 mt-1 flex-shrink-0"}),t.jsxs("span",{children:[a.address,", ",a.ward,", ",a.district,", ",a.city]})]})]})]}),t.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[!a.isDefault&&t.jsx("button",{onClick:()=>{return e=a.id,s((s=>s.map((s=>({...s,isDefault:s.id===e}))))),void g.success("Đã đặt làm địa chỉ mặc định!");var e},className:"p-2 text-gray-400 hover:text-dexin-primary hover:bg-dexin-light-10 rounded-lg transition-colors",title:"Đặt làm mặc định",children:t.jsx(_,{className:"w-4 h-4"})}),t.jsx("button",{onClick:()=>(e=>{h(e),c(e),l(!0)})(a),className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:t.jsx(Q,{className:"w-4 h-4"})}),!a.isDefault&&t.jsx("button",{onClick:()=>{return t=a.id,void((null==(i=e.find((e=>e.id===t)))?void 0:i.isDefault)?g.error("Không thể xóa địa chỉ mặc định"):(s((e=>e.filter((e=>e.id!==t)))),g.success("Xóa địa chỉ thành công!")));var t,i},className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"Xóa",children:t.jsx(I,{className:"w-4 h-4"})})]})]})},a.id)))}),t.jsx(j,{children:a&&t.jsxs(i.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"border border-gray-200 rounded-lg p-6 bg-gray-50",children:[t.jsxs("div",{className:"flex items-center justify-between mb-4",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:r?"Chỉnh sửa địa chỉ":"Thêm địa chỉ mới"}),t.jsx("button",{onClick:u,className:"p-1 hover:bg-gray-200 rounded-lg transition-colors",children:t.jsx(n,{className:"w-5 h-5 text-gray-500"})})]}),t.jsxs("form",{onSubmit:e=>{if(e.preventDefault(),d.name&&d.fullName&&d.phone&&d.address){if(r)s((e=>e.map((e=>e.id===r.id?{...d,id:r.id}:d.isDefault?{...e,isDefault:!1}:e)))),g.success("Cập nhật địa chỉ thành công!"),c(null);else{const e={...d,id:Date.now()};s((s=>d.isDefault?[...s.map((e=>({...e,isDefault:!1}))),e]:[...s,e])),g.success("Thêm địa chỉ thành công!")}h({name:"",fullName:"",phone:"",address:"",ward:"",district:"",city:"TP. Hồ Chí Minh",type:"home",isDefault:!1}),l(!1)}else g.error("Vui lòng điền đầy đủ thông tin")},className:"space-y-4",children:[t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tên địa chỉ *"}),t.jsx("input",{type:"text",name:"name",value:d.name,onChange:m,placeholder:"VD: Nhà riêng, Văn phòng",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary",required:!0})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Loại địa chỉ"}),t.jsx("select",{name:"type",value:d.type,onChange:m,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary",children:x.map((e=>t.jsx("option",{value:e.value,children:e.label},e.value)))})]})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Họ và tên *"}),t.jsx("input",{type:"text",name:"fullName",value:d.fullName,onChange:m,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary",required:!0})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số điện thoại *"}),t.jsx("input",{type:"tel",name:"phone",value:d.phone,onChange:m,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary",required:!0})]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Địa chỉ cụ thể *"}),t.jsx("input",{type:"text",name:"address",value:d.address,onChange:m,placeholder:"Số nhà, tên đường",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary",required:!0})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phường/Xã"}),t.jsx("input",{type:"text",name:"ward",value:d.ward,onChange:m,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quận/Huyện"}),t.jsx("input",{type:"text",name:"district",value:d.district,onChange:m,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tỉnh/Thành phố"}),t.jsx("select",{name:"city",value:d.city,onChange:m,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary",children:["TP. Hồ Chí Minh","Hà Nội","Đà Nẵng","Cần Thơ","Hải Phòng"].map((e=>t.jsx("option",{value:e,children:e},e)))})]})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx("input",{type:"checkbox",id:"isDefault",name:"isDefault",checked:d.isDefault,onChange:m,className:"h-4 w-4 text-dexin-primary focus:ring-dexin-primary border-gray-300 rounded"}),t.jsx("label",{htmlFor:"isDefault",className:"ml-2 text-sm text-gray-700",children:"Đặt làm địa chỉ mặc định"})]}),t.jsxs("div",{className:"flex space-x-3 pt-4",children:[t.jsxs("button",{type:"submit",className:"flex items-center space-x-2 px-4 py-2 bg-dexin-primary text-white rounded-lg hover:bg-dexin-light-90 transition-colors",children:[t.jsx(N,{className:"w-4 h-4"}),t.jsx("span",{children:r?"Cập nhật":"Lưu địa chỉ"})]}),t.jsx("button",{type:"button",onClick:u,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Hủy"})]})]})]})})]})]})},le=()=>{const[e,s]=p.useState("all"),a=[{id:"DH001",date:"2024-01-15",status:"delivered",total:245e4,items:[{name:"Bàn gỗ sồi",quantity:1,price:12e5},{name:"Ghế văn phòng",quantity:2,price:625e3}],shippingAddress:"123 Nguyễn Văn A, Quận 1, TP.HCM"},{id:"DH002",date:"2024-01-20",status:"shipping",total:89e4,items:[{name:"Đèn bàn LED",quantity:1,price:45e4},{name:"Tủ sách mini",quantity:1,price:44e4}],shippingAddress:"456 Lê Văn B, Quận 3, TP.HCM"},{id:"DH003",date:"2024-01-25",status:"processing",total:32e5,items:[{name:"Sofa 3 chỗ",quantity:1,price:32e5}],shippingAddress:"789 Trần Văn C, Quận 7, TP.HCM"},{id:"DH004",date:"2024-01-28",status:"cancelled",total:75e4,items:[{name:"Bàn cà phê",quantity:1,price:75e4}],shippingAddress:"321 Phạm Văn D, Quận 5, TP.HCM"}],n={all:{label:"Tất cả",color:"gray",icon:T},processing:{label:"Đang xử lý",color:"yellow",icon:C},shipping:{label:"Đang giao",color:"blue",icon:J},delivered:{label:"Đã giao",color:"green",icon:k},cancelled:{label:"Đã hủy",color:"red",icon:w}},l="all"===e?a:a.filter((s=>s.status===e)),r=e=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(e),c=e=>{const s=n[e],a=s.icon;return t.jsxs("span",{className:`\n        inline-flex items-center px-3 py-1 rounded-full text-xs font-medium\n        ${"processing"===e?"bg-yellow-100 text-yellow-800":""}\n        ${"shipping"===e?"bg-blue-100 text-blue-800":""}\n        ${"delivered"===e?"bg-green-100 text-green-800":""}\n        ${"cancelled"===e?"bg-red-100 text-red-800":""}\n      `,children:[t.jsx(a,{className:"w-3 h-3 mr-1"}),s.label]})};return t.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[t.jsxs("div",{className:"bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4",children:[t.jsx("h2",{className:"text-xl font-bold text-white",children:"Lịch sử đơn hàng"}),t.jsx("p",{className:"text-dexin-bg text-sm mt-1",children:"Theo dõi tất cả đơn hàng của bạn"})]}),t.jsx("div",{className:"border-b border-gray-200",children:t.jsx("div",{className:"flex overflow-x-auto",children:Object.entries(n).map((([i,n])=>{const l=n.icon,r=e===i,c="all"===i?a.length:a.filter((e=>e.status===i)).length;return t.jsxs("button",{onClick:()=>s(i),className:`\n                  flex items-center space-x-2 px-4 py-3 border-b-2 transition-colors whitespace-nowrap\n                  ${r?"border-dexin-primary text-dexin-primary bg-dexin-light-10":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}\n                `,children:[t.jsx(l,{className:"w-4 h-4"}),t.jsx("span",{className:"font-medium",children:n.label}),t.jsx("span",{className:`\n                  px-2 py-1 rounded-full text-xs\n                  ${r?"bg-dexin-primary text-white":"bg-gray-200 text-gray-600"}\n                `,children:c})]},i)}))})}),t.jsx("div",{className:"p-6",children:0===l.length?t.jsxs("div",{className:"text-center py-12",children:[t.jsx(T,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Không có đơn hàng"}),t.jsx("p",{className:"text-gray-500",children:"all"===e?"Bạn chưa có đơn hàng nào.":`Không có đơn hàng ${n[e].label.toLowerCase()}.`})]}):t.jsx("div",{className:"space-y-6",children:l.map((e=>{return t.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[t.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-4 mb-2 sm:mb-0",children:[t.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["#",e.id]}),c(e.status)]}),t.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(D,{className:"w-4 h-4"}),t.jsx("span",{children:(s=e.date,new Date(s).toLocaleDateString("vi-VN",{year:"numeric",month:"long",day:"numeric"}))})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(S,{className:"w-4 h-4"}),t.jsx("span",{className:"font-semibold text-dexin-primary",children:r(e.total)})]})]})]}),t.jsx("div",{className:"space-y-2 mb-4",children:e.items.map(((e,s)=>t.jsxs("div",{className:"flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0",children:[t.jsxs("div",{children:[t.jsx("span",{className:"font-medium text-gray-900",children:e.name}),t.jsxs("span",{className:"text-gray-500 ml-2",children:["x",e.quantity]})]}),t.jsx("span",{className:"font-medium text-gray-900",children:r(e.price)})]},s)))}),t.jsx("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:t.jsxs("p",{className:"text-sm text-gray-600",children:[t.jsx("span",{className:"font-medium",children:"Địa chỉ giao hàng:"})," ",e.shippingAddress]})}),t.jsx("div",{className:"flex justify-end",children:t.jsxs(i.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-4 py-2 bg-dexin-light text-white rounded-lg hover:bg-dexin-primary transition-colors",children:[t.jsx(V,{className:"w-4 h-4"}),t.jsx("span",{children:"Xem chi tiết"})]})})]},e.id);var s}))})})]})},re=()=>{var e;const{user:a}=s(),[n,l]=p.useState("all"),[r,c]=p.useState([]),[d,o]=p.useState(!0),[h,m]=p.useState(null);p.useEffect((()=>{(async()=>{if(null==a?void 0:a.id)try{o(!0);const e=await q.getDesignsByUser(a.id);e.success?c(e.data):$.error(e.message)}catch(e){$.error("Có lỗi xảy ra khi tải danh sách thiết kế")}finally{o(!1)}else o(!1)})()}),[a]);const g=[{id:"all",label:"Tất cả",count:r.length},{id:"paid",label:"Đã thanh toán",count:r.filter((e=>"Hoàn tất thanh toán"===e.status)).length},{id:"unpaid",label:"Chưa thanh toán",count:r.filter((e=>"Chưa trả"===e.status)).length},{id:"cancelled",label:"Đã hủy",count:r.filter((e=>"Hủy"===e.status)).length}],u="all"===n?r:r.filter((e=>{switch(n){case"paid":return"Hoàn tất thanh toán"===e.status;case"unpaid":return"Chưa trả"===e.status;case"cancelled":return"Hủy"===e.status;default:return!0}})),y=e=>"Hoàn tất thanh toán"===e.status;return a?d?t.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[t.jsx("div",{className:"bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4",children:t.jsx("h2",{className:"text-xl font-bold text-white",children:"Bản thiết kế của tôi"})}),t.jsxs("div",{className:"p-6 text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-dexin-primary mx-auto mb-4"}),t.jsx("p",{className:"text-gray-600",children:"Đang tải danh sách thiết kế..."})]})]}):t.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[t.jsx("div",{className:"bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4",children:t.jsx("div",{className:"flex items-center justify-between",children:t.jsxs("div",{children:[t.jsx("h2",{className:"text-xl font-bold text-white",children:"Bản thiết kế của tôi"}),t.jsx("p",{className:"text-dexin-bg text-sm mt-1",children:"Quản lý các thiết kế đã tạo"})]})})}),t.jsx("div",{className:"border-b border-gray-200",children:t.jsx("div",{className:"flex overflow-x-auto",children:g.map((e=>{const s=n===e.id;return t.jsxs("button",{onClick:()=>l(e.id),className:`\n                  flex items-center space-x-2 px-4 py-3 border-b-2 transition-colors whitespace-nowrap\n                  ${s?"border-dexin-primary text-dexin-primary bg-dexin-light-10":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}\n                `,children:[t.jsx("span",{className:"font-medium",children:e.label}),t.jsx("span",{className:`\n                  px-2 py-1 rounded-full text-xs\n                  ${s?"bg-dexin-primary text-white":"bg-gray-200 text-gray-600"}\n                `,children:e.count})]},e.id)}))})}),t.jsx("div",{className:"p-6",children:0===u.length?t.jsxs("div",{className:"text-center py-12",children:[t.jsx(x,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Chưa có thiết kế"}),t.jsx("p",{className:"text-gray-500 mb-4",children:"all"===n?"Bạn chưa tạo thiết kế nào.":`Chưa có thiết kế ${null==(e=g.find((e=>e.id===n)))?void 0:e.label.toLowerCase()}.`})]}):t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u.map((e=>{const s=!y(e),a=h===e.designId;return t.jsxs(i.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},whileHover:{y:-5},className:"bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300",children:[t.jsxs("div",{className:"relative h-48 bg-gray-100",children:[e.thumbnailUrl?t.jsx("img",{src:e.thumbnailUrl,alt:e.title,className:"w-full h-full object-cover "+(s?"filter blur-sm":"")}):t.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:t.jsx(M,{className:"w-12 h-12 text-gray-400"})}),s&&t.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:t.jsxs("div",{className:"text-center text-white",children:[t.jsx(E,{className:"w-8 h-8 mx-auto mb-2"}),t.jsx("p",{className:"text-sm font-medium",children:"Cần thanh toán"})]})}),t.jsxs("div",{className:"absolute top-2 right-2 flex space-x-1",children:[t.jsx("button",{onClick:()=>(e=>{y(e)?navigator.share?navigator.share({title:e.title,text:`Xem thiết kế: ${e.title}`,url:e.pathUrl}):(navigator.clipboard.writeText(e.pathUrl),$.success("Đã copy link thiết kế vào clipboard")):$.error("Vui lòng thanh toán để chia sẻ thiết kế")})(e),disabled:s,className:"p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all "+(s?"opacity-50 cursor-not-allowed":""),title:s?"Cần thanh toán để chia sẻ":"Chia sẻ",children:t.jsx(O,{className:"w-4 h-4 text-gray-600"})}),t.jsx("button",{onClick:()=>(e=>{y(e)?e.pathUrl?(window.open(e.pathUrl,"_blank"),$.success("Đang tải xuống thiết kế...")):$.error("Không tìm thấy file thiết kế"):$.error("Vui lòng thanh toán để tải xuống thiết kế")})(e),disabled:s,className:"p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all "+(s?"opacity-50 cursor-not-allowed":""),title:s?"Cần thanh toán để tải xuống":"Tải xuống",children:t.jsx(U,{className:"w-4 h-4 text-gray-600"})})]}),t.jsx("div",{className:"absolute top-2 left-2",children:t.jsx("span",{className:"px-2 py-1 rounded-full text-xs font-medium "+("Hoàn tất thanh toán"===e.status?"bg-green-100 text-green-800":"Chưa trả"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status})})]}),t.jsxs("div",{className:"p-4",children:[t.jsx("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-1",children:e.title}),t.jsx("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:e.note||"Không có mô tả"}),t.jsxs("div",{className:"flex items-center justify-between mb-3",children:[t.jsxs("span",{className:"text-lg font-bold text-dexin-primary",children:[e.price.toLocaleString("vi-VN")," VNĐ"]}),t.jsx("div",{className:"text-xs text-gray-500",children:t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(D,{className:"w-3 h-3"}),t.jsx("span",{children:(n=e.createdAt,new Date(n).toLocaleDateString("vi-VN",{year:"numeric",month:"short",day:"numeric"}))})]})})]}),e.staff&&t.jsxs("div",{className:"text-xs text-gray-500 mb-3",children:["Thiết kế bởi: ",e.staff.firstName," ",e.staff.lastName]}),t.jsxs("div",{className:"flex space-x-2",children:[s?t.jsx("button",{onClick:()=>(async e=>{var s;if("Chưa trả"===e.status)try{m(e.designId);const a=window.location.origin,t=`${a}/payment/success`,i=`${a}/payment/cancel`,n=Math.floor(Date.now()/1e3)+3600,l={productName:e.title,description:`Thanh toán thiết kế: ${e.title}`,price:e.price,returnUrl:t,cancelUrl:i,expiredAt:n},r=await P.createPayment(e.designId,l);r.success&&(null==(s=r.data)?void 0:s.checkoutUrl)?window.location.href=r.data.checkoutUrl:$.error(r.message||"Không thể tạo đơn thanh toán")}catch(a){$.error("Có lỗi xảy ra khi tạo đơn thanh toán")}finally{m(null)}else $.error("Thiết kế này không cần thanh toán")})(e),disabled:a,className:"flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-dexin-primary text-white rounded-lg hover:bg-dexin-light transition-colors text-sm disabled:opacity-50",children:a?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-b-2 border-white"}),t.jsx("span",{children:"Đang xử lý..."})]}):t.jsxs(t.Fragment,{children:[t.jsx(S,{className:"w-3 h-3"}),t.jsx("span",{children:"Thanh toán"})]})}):t.jsxs("button",{onClick:()=>(e=>{y(e)?e.pathUrl?window.open(e.pathUrl,"_blank"):$.error("Không tìm thấy file thiết kế"):$.error("Vui lòng thanh toán để xem thiết kế")})(e),className:"flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm",children:[t.jsx(V,{className:"w-3 h-3"}),t.jsx("span",{children:"Xem thiết kế"})]}),t.jsx("button",{onClick:()=>(e=>{window.confirm(`Bạn có chắc muốn xóa thiết kế "${e.title}"?`)&&$.info("Tính năng xóa thiết kế đang được phát triển")})(e),className:"px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"Xóa thiết kế",children:t.jsx(I,{className:"w-4 h-4"})})]})]})]},e.designId);var n}))})})]}):t.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-white rounded-lg shadow-lg overflow-hidden",children:[t.jsx("div",{className:"bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4",children:t.jsx("h2",{className:"text-xl font-bold text-white",children:"Bản thiết kế của tôi"})}),t.jsxs("div",{className:"p-6 text-center",children:[t.jsx(f,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Vui lòng đăng nhập"}),t.jsx("p",{className:"text-gray-500 mb-4",children:"Bạn cần đăng nhập để xem danh sách thiết kế của mình."}),t.jsx(H,{to:"/login",className:"inline-flex items-center space-x-2 px-4 py-2 bg-dexin-primary text-white rounded-lg hover:bg-dexin-light transition-colors",children:t.jsx("span",{children:"Đăng nhập ngay"})})]})]})},ce=()=>{const{loading:e,refreshUser:a,user:n}=s(),l=L(),[r,c]=p.useState("profile"),[d,o]=p.useState(!1);p.useEffect((()=>{(async()=>{if(null==n?void 0:n.id)try{await a()}catch(e){}})()}),[null==n?void 0:n.id,a]),p.useEffect((()=>{var e;(null==(e=l.state)?void 0:e.activeSection)&&(c(l.state.activeSection),window.history.replaceState({},document.title))}),[l.state]);const h=()=>({profile:"Thông tin cá nhân",address:"Địa chỉ giao hàng",orders:"Lịch sử đơn hàng",designs:"Bản thiết kế"}[r]||"Tài khoản");return e?t.jsx("div",{className:"min-h-screen bg-gray-50 py-8 flex items-center justify-center",children:t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-dexin-primary"}),t.jsx("span",{className:"text-gray-600",children:"Đang tải..."})]})}):t.jsxs(t.Fragment,{children:[t.jsx(A,{title:`${h()} - Tài khoản DEXIN`,description:"Quản lý thông tin tài khoản, địa chỉ, đơn hàng và bản thiết kế trên DEXIN",keywords:"tài khoản, thông tin cá nhân, đơn hàng, địa chỉ, thiết kế nội thất"}),t.jsx("div",{className:"min-h-screen bg-gray-50",children:t.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t.jsx("div",{className:"md:hidden mb-6",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:h()}),t.jsx("button",{onClick:()=>o(!0),className:"p-2 bg-white rounded-lg shadow-sm border border-gray-200 hover:bg-gray-50 transition-colors",children:t.jsx(B,{className:"w-5 h-5 text-gray-600"})})]})}),t.jsxs("div",{className:"flex flex-col md:flex-row gap-6",children:[t.jsx("div",{className:"md:w-80 md:flex-shrink-0",children:t.jsx(Y,{activeSection:r,setActiveSection:c,isSidebarOpen:d,setIsSidebarOpen:o})}),t.jsx("div",{className:"flex-1 min-w-0",children:t.jsx(i.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"w-full",children:(()=>{switch(r){case"profile":default:return t.jsx(ie,{});case"address":return t.jsx(ne,{});case"orders":return t.jsx(le,{});case"designs":return t.jsx(re,{})}})()},r)})]})]})})]})};export{ce as default};
