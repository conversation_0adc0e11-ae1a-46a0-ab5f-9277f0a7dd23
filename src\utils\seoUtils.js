// SEO utilities for DEXIN

export const seoConfig = {
  siteName: 'DEXIN',
  siteUrl: 'https://dexin.io.vn',
  defaultTitle: 'DEXIN - Nền tảng thiết kế nội thất với AI',
  defaultDescription: 'DEXIN là nền tảng thiết kế nội thất thông minh với AI, cung cấp công cụ thiết kế 2D, cửa hàng nội thất và tư vấn chuyên nghiệp. Tạo không gian sống hoàn hảo với DEXIN.',
  defaultKeywords: 'thiết kế nội thất, AI, nội thất, thiế<PERSON> kế <PERSON>, cử<PERSON> hàng nội thất, trang trí nh<PERSON>, DEXIN, furniture, interior design',
  defaultImage: '/images/Logo DEXIN finall 2.png',
  author: 'DEXIN Team',
  locale: 'vi_VN',
  themeColor: '#FF6B93'
};

// SEO data cho từng trang
export const pagesSEO = {
  home: {
    title: 'DEXIN - Nền tảng thiết kế nội thất với AI',
    description: '<PERSON>h<PERSON><PERSON> phá DEXIN - nền tảng thiết kế nội thất thông minh với AI. Thiết kế 2D, cửa hàng nội thất, tư vấn chuyên nghiệp. Tạo không gian sống hoàn hảo ngay hôm nay!',
    keywords: 'thiết kế nội thất AI, DEXIN, nội thất thông minh, thiết kế 2D, cửa hàng nội thất, trang trí nhà',
    url: '/'
  },
  store: {
    title: 'Cửa hàng nội thất DEXIN - Mua sắm nội thất chất lượng',
    description: 'Khám phá cửa hàng nội thất DEXIN với hàng ngàn sản phẩm chất lượng cao. Ghế, giường, cây xanh và nhiều món đồ nội thất khác với giá tốt nhất.',
    keywords: 'cửa hàng nội thất, mua nội thất online, ghế sofa, giường ngủ, cây xanh, đồ trang trí, DEXIN store',
    url: '/store'
  },
  phacy: {
    title: 'Phác Ý - Công cụ thiết kế 2D nội thất miễn phí',
    description: 'Sử dụng công cụ Phác Ý của DEXIN để thiết kế nội thất 2D miễn phí. Kéo thả đồ nội thất, tạo bản vẽ chuyên nghiệp và xuất file PDF dễ dàng.',
    keywords: 'thiết kế 2D, phác thảo nội thất, công cụ thiết kế miễn phí, vẽ nội thất online, DEXIN phác ý',
    url: '/phac-y'
  },
  chungnhip: {
    title: 'Chung Nhịp - Cộng đồng thiết kế nội thất DEXIN',
    description: 'Tham gia cộng đồng Chung Nhịp của DEXIN để chia sẻ ý tưởng thiết kế, học hỏi kinh nghiệm và kết nối với những người yêu thích nội thất.',
    keywords: 'cộng đồng thiết kế, chia sẻ ý tưởng nội thất, học thiết kế, DEXIN community, chung nhịp',
    url: '/chung-nhip'
  },
  chuyennha: {
    title: 'Chuyển Nhà - Dịch vụ tư vấn và hỗ trợ chuyển nhà',
    description: 'Dịch vụ Chuyển Nhà của DEXIN cung cấp tư vấn thiết kế, lên kế hoạch và hỗ trợ chuyển nhà hoàn hảo. Biến ngôi nhà mới thành không gian sống lý tưởng.',
    keywords: 'chuyển nhà, tư vấn thiết kế nhà mới, dịch vụ chuyển nhà, thiết kế nhà ở, DEXIN chuyển nhà',
    url: '/chuyen-nha'
  },
  wishlist: {
    title: 'Danh sách yêu thích - DEXIN Wishlist',
    description: 'Quản lý danh sách sản phẩm nội thất yêu thích của bạn trên DEXIN. Lưu trữ và theo dõi những món đồ bạn muốn mua.',
    keywords: 'wishlist, danh sách yêu thích, lưu sản phẩm, theo dõi nội thất, DEXIN wishlist',
    url: '/wishlist'
  },
  cart: {
    title: 'Giỏ hàng - DEXIN Shopping Cart',
    description: 'Xem lại các sản phẩm nội thất trong giỏ hàng của bạn. Thanh toán nhanh chóng và an toàn với DEXIN.',
    keywords: 'giỏ hàng, shopping cart, thanh toán, mua nội thất, DEXIN cart',
    url: '/cart'
  },
  login: {
    title: 'Đăng nhập - DEXIN',
    description: 'Đăng nhập vào tài khoản DEXIN để trải nghiệm đầy đủ các tính năng thiết kế nội thất và mua sắm.',
    keywords: 'đăng nhập, login, tài khoản DEXIN, đăng nhập DEXIN',
    url: '/login'
  },
  signup: {
    title: 'Đăng ký - DEXIN',
    description: 'Tạo tài khoản DEXIN miễn phí để sử dụng công cụ thiết kế 2D, mua sắm nội thất và tham gia cộng đồng.',
    keywords: 'đăng ký, signup, tạo tài khoản, đăng ký DEXIN, tài khoản miễn phí',
    url: '/signup'
  }
};

// Hàm tạo structured data cho sản phẩm
export const generateProductStructuredData = (product) => {
  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "image": product.image,
    "brand": {
      "@type": "Brand",
      "name": "DEXIN"
    },
    "offers": {
      "@type": "Offer",
      "price": product.price,
      "priceCurrency": "VND",
      "availability": "https://schema.org/InStock",
      "seller": {
        "@type": "Organization",
        "name": "DEXIN"
      }
    }
  };
};

// Hàm tạo breadcrumb structured data
export const generateBreadcrumbStructuredData = (breadcrumbs) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": `${seoConfig.siteUrl}${item.url}`
    }))
  };
};

// Hàm tạo FAQ structured data
export const generateFAQStructuredData = (faqs) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
};

// Hàm tối ưu hóa hình ảnh cho SEO
export const optimizeImageForSEO = (src, alt, title) => {
  return {
    src,
    alt: alt || 'DEXIN - Thiết kế nội thất',
    title: title || alt || 'DEXIN - Thiết kế nội thất',
    loading: 'lazy'
  };
};

// Hàm tạo meta description từ nội dung
export const generateMetaDescription = (content, maxLength = 160) => {
  if (!content) return seoConfig.defaultDescription;

  const cleanContent = content.replace(/<[^>]*>/g, '').trim();
  if (cleanContent.length <= maxLength) return cleanContent;

  return cleanContent.substring(0, maxLength - 3) + '...';
};

// Hàm tạo keywords từ nội dung
export const generateKeywords = (content, additionalKeywords = []) => {
  const baseKeywords = seoConfig.defaultKeywords.split(', ');
  const contentKeywords = content ? content.toLowerCase().match(/\b\w{4,}\b/g) || [] : [];

  const allKeywords = [...baseKeywords, ...additionalKeywords, ...contentKeywords];
  const uniqueKeywords = [...new Set(allKeywords)];

  return uniqueKeywords.slice(0, 15).join(', ');
};

// Hàm tạo internal linking suggestions
export const generateInternalLinks = (currentPage, allPages) => {
  const suggestions = [];
  const currentKeywords = currentPage.keywords?.split(', ') || [];

  allPages.forEach(page => {
    if (page.url !== currentPage.url) {
      const pageKeywords = page.keywords?.split(', ') || [];
      const commonKeywords = currentKeywords.filter(keyword =>
        pageKeywords.some(pk => pk.toLowerCase().includes(keyword.toLowerCase()))
      );

      if (commonKeywords.length > 0) {
        suggestions.push({
          url: page.url,
          title: page.title,
          relevanceScore: commonKeywords.length,
          suggestedAnchorText: page.title.split(' - ')[0] // Lấy phần đầu của title
        });
      }
    }
  });

  return suggestions.sort((a, b) => b.relevanceScore - a.relevanceScore).slice(0, 5);
};

// Hàm tạo Local Business structured data
export const generateLocalBusinessStructuredData = (businessInfo) => {
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": businessInfo.name || "DEXIN",
    "description": businessInfo.description || "Nền tảng thiết kế nội thất thông minh với AI",
    "url": "https://dexin.io.vn",
    "logo": "https://dexin.io.vn/images/Logo DEXIN finall 2.png",
    "image": businessInfo.image || "https://dexin.io.vn/images/hometile hero.jpg",
    "telephone": businessInfo.phone || "+84-xxx-xxx-xxx",
    "email": businessInfo.email || "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": businessInfo.address || "123 Đường ABC",
      "addressLocality": businessInfo.city || "TP. Hồ Chí Minh",
      "addressRegion": businessInfo.region || "TP. Hồ Chí Minh",
      "postalCode": businessInfo.postalCode || "70000",
      "addressCountry": "VN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": businessInfo.latitude || 10.8231,
      "longitude": businessInfo.longitude || 106.6297
    },
    "openingHours": businessInfo.openingHours || [
      "Mo-Fr 08:00-18:00",
      "Sa 08:00-16:00"
    ],
    "priceRange": businessInfo.priceRange || "$$",
    "paymentAccepted": businessInfo.paymentMethods || ["Cash", "Credit Card", "Bank Transfer"],
    "currenciesAccepted": "VND"
  };
};

// Hàm kiểm tra SEO score
export const calculateSEOScore = (pageData) => {
  let score = 0;
  const checks = [];

  // Title check (20 points)
  if (pageData.title && pageData.title.length >= 30 && pageData.title.length <= 60) {
    score += 20;
    checks.push({ name: 'Title Length', status: 'pass', points: 20 });
  } else {
    checks.push({ name: 'Title Length', status: 'fail', points: 0, suggestion: 'Title should be 30-60 characters' });
  }

  // Meta description check (15 points)
  if (pageData.description && pageData.description.length >= 120 && pageData.description.length <= 160) {
    score += 15;
    checks.push({ name: 'Meta Description', status: 'pass', points: 15 });
  } else {
    checks.push({ name: 'Meta Description', status: 'fail', points: 0, suggestion: 'Description should be 120-160 characters' });
  }

  // Keywords check (10 points)
  if (pageData.keywords && pageData.keywords.split(', ').length >= 5) {
    score += 10;
    checks.push({ name: 'Keywords', status: 'pass', points: 10 });
  } else {
    checks.push({ name: 'Keywords', status: 'fail', points: 0, suggestion: 'Include at least 5 relevant keywords' });
  }

  // URL structure check (10 points)
  if (pageData.url && pageData.url.length <= 100 && !pageData.url.includes('?')) {
    score += 10;
    checks.push({ name: 'URL Structure', status: 'pass', points: 10 });
  } else {
    checks.push({ name: 'URL Structure', status: 'fail', points: 0, suggestion: 'URL should be clean and under 100 characters' });
  }

  // Images with alt text check (15 points)
  if (pageData.imagesWithAlt && pageData.imagesWithAlt >= pageData.totalImages * 0.8) {
    score += 15;
    checks.push({ name: 'Image Alt Text', status: 'pass', points: 15 });
  } else {
    checks.push({ name: 'Image Alt Text', status: 'fail', points: 0, suggestion: 'Add alt text to at least 80% of images' });
  }

  // Internal links check (10 points)
  if (pageData.internalLinks && pageData.internalLinks >= 3) {
    score += 10;
    checks.push({ name: 'Internal Links', status: 'pass', points: 10 });
  } else {
    checks.push({ name: 'Internal Links', status: 'fail', points: 0, suggestion: 'Include at least 3 internal links' });
  }

  // Content length check (10 points)
  if (pageData.contentLength && pageData.contentLength >= 300) {
    score += 10;
    checks.push({ name: 'Content Length', status: 'pass', points: 10 });
  } else {
    checks.push({ name: 'Content Length', status: 'fail', points: 0, suggestion: 'Content should be at least 300 words' });
  }

  // Structured data check (10 points)
  if (pageData.hasStructuredData) {
    score += 10;
    checks.push({ name: 'Structured Data', status: 'pass', points: 10 });
  } else {
    checks.push({ name: 'Structured Data', status: 'fail', points: 0, suggestion: 'Add relevant structured data' });
  }

  return {
    score,
    maxScore: 100,
    grade: score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F',
    checks
  };
};
