import React, { useState, useEffect, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { motion, AnimatePresence, LayoutGroup } from 'motion/react';
import { useWishlist } from "../../../context/WishlistContext";
import { useCart } from "../../../context/CartContext";
import { Heart, ShoppingBag, Minus, Plus, ChevronRight, MoreHorizontal, SlidersVertical  } from 'lucide-react';
import { toast } from 'react-toastify';
import Button from "../../../components/common/Button";
import { getProductById } from '../../../data/products';

// Dữ liệu đánh giá mẫu
const reviewsData = [
  {
    id: 1,
    name: '<PERSON>',
    verified: true,
    rating: 5,
    comment: 'Gh<PERSON> rất đẹp. <PERSON><PERSON>g gói cẩn thận, giao hàng nhanh. Rất hài lòng!',
    date: '10/03/2025',
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    verified: true,
    rating: 5,
    comment: '<PERSON><PERSON><PERSON> có thiết kế tinh tế, khung gỗ chắc chắn, phù hợp với nhiều không gian.',
    date: '12/03/2025',
  },
  {
    id: 3,
    name: 'Thảo Vy',
    verified: true,
    rating: 4.5,
    comment: 'Ghế chắc chắn nhưng hơi nặng, di chuyển không quá linh hoạt.',
    date: '14/03/2025',
  },
  {
    id: 4,
    name: 'Huyền My',
    verified: true,
    rating: 5,
    comment: 'Nệm êm ái, ngồi lâu không bị mỏi, rất thích hợp để làm việc. Nhưng giao hàng hơi chậm',
    date: '16/03/2025',
  },
  {
    id: 5,
    name: 'Đức Anh',
    verified: true,
    rating: 3,
    comment: 'Gốc chân ghế hơi sắc, để gây trầy sàn nếu không có lót cao su!',
    date: '17/03/2025',
  },
  {
    id: 6,
    name: 'Quang Tú',
    verified: true,
    rating: 4.5,
    comment: 'Giá cả hợp lý so với chất lượng, rất đáng để đầu tư.',
    date: '20/03/2025',
  }
];

// Component trang chi tiết sản phẩm
const ProductDetail = () => {
  const { productId } = useParams();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { addToCart } = useCart();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [currentImage, setCurrentImage] = useState(0);
  const [activeTab, setActiveTab] = useState('description'); // Thêm state quản lý tab
  const [reviewFilter, setReviewFilter] = useState('newest');

  // Giả lập việc lấy dữ liệu sản phẩm từ API
  useEffect(() => {
    // Trường hợp thực tế sẽ gọi API
    const fetchProduct = () => {
      setLoading(true);
      try {
        setTimeout(() => {
          const foundProduct = getProductById(parseInt(productId));
          if (foundProduct) {
            setProduct(foundProduct);
            // Mặc định chọn biến thể đầu tiên
            if (foundProduct.variants && foundProduct.variants.length > 0) {
              setSelectedVariant(foundProduct.variants[0].id);
            }
          }
          setLoading(false);
        }, 500); // Giả lập độ trễ mạng
      } catch (error) {
        console.error('Lỗi khi lấy dữ liệu sản phẩm:', error);
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId]);

  // Kiểm tra xem sản phẩm có trong danh sách yêu thích không
  const inWishlist = product ? isInWishlist(product.id) : false;

  // Xử lý thêm/xóa khỏi danh sách yêu thích
  const handleToggleWishlist = () => {
    if (!product) return;

    if (inWishlist) {
      removeFromWishlist(product.id);
      toast.info('Đã xóa khỏi mục yêu thích', {
        icon: () => <span style={{ fontSize: '1.5rem', marginRight: '10px' }}>💔</span>,
        className: 'toast-message'
      });
    } else {
      addToWishlist(product);
      toast.success('Đã thêm vào mục yêu thích', {
        icon: () => <span style={{ fontSize: '1.5rem', marginRight: '10px' }}>❤️</span>,
        className: 'toast-message'
      });
    }
  };

  // Định dạng giá tiền
  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Tăng số lượng
  const increaseQuantity = () => {
    setQuantity(prev => prev + 1);
  };

  // Giảm số lượng, tối thiểu là 1
  const decreaseQuantity = () => {
    setQuantity(prev => (prev > 1 ? prev - 1 : 1));
  };

  // Xử lý khi thay đổi ảnh
  const handleImageChange = (index) => {
    setCurrentImage(index);
  };

  // Xử lý thêm vào giỏ hàng
  const handleAddToCart = () => {
    if (!product) return;

    // Thêm thông tin biến thể nếu có
    const selectedVariantInfo = product.variants && product.variants.find(v => v.id === selectedVariant);
    
    // Tạo đối tượng sản phẩm để thêm vào giỏ hàng
    const cartItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: quantity,
      // Thêm thông tin variant đúng format
      variantId: selectedVariantInfo ? selectedVariantInfo.id : null,
      variantName: selectedVariantInfo ? selectedVariantInfo.name : '',
      variantColor: selectedVariantInfo ? selectedVariantInfo.color : ''
    };
    
    // Gọi hàm thêm vào giỏ hàng từ context
    addToCart(cartItem, quantity);
    
    // Hiển thị toast thông báo với ID duy nhất
    const displayName = selectedVariantInfo 
      ? `${product.name} (${selectedVariantInfo.name})`
      : product.name;
      
    toast.success(`Đã thêm "${displayName}" vào giỏ hàng`, {
      icon: () => <span style={{ fontSize: '1.2rem', marginRight: '10px' }}>🛒</span>,
      className: 'toast-message',
      toastId: `detail-cart-${product.id}-${selectedVariantInfo?.id || 'no-variant'}`, // Thêm variant ID vào toast ID
      autoClose: 2000 // Đóng sau 2 giây
    });
  };

  // Hàm sắp xếp và lọc đánh giá
  const sortedReviews = useMemo(() => {
    let filteredReviews = [...reviewsData];

    switch (reviewFilter) {
      case 'newest':
        filteredReviews.sort((a, b) => new Date(b.date.split('/').reverse().join('-')) - new Date(a.date.split('/').reverse().join('-')));
        break;
      case 'highest':
        filteredReviews.sort((a, b) => b.rating - a.rating);
        break;
      case 'lowest':
        filteredReviews.sort((a, b) => a.rating - b.rating);
        break;
      default:
        break;
    }

    return filteredReviews;
  }, [reviewFilter]);

  // Hàm xử lý thay đổi filter
  const handleReviewFilterChange = (e) => {
    setReviewFilter(e.target.value);
  };

  // Hiển thị thông báo loading khi đang tải dữ liệu
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12 flex items-center justify-center min-h-[60vh]">
        <div className="animate-pulse">
          <div className="w-12 h-12 border-4 border-dexin-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  // Hiển thị thông báo không tìm thấy sản phẩm
  if (!product) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-[60vh]">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Không tìm thấy sản phẩm</h1>
          <p className="text-gray-600 mb-6">Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
          <Link to="/store">
            <Button variant="primary">Quay lại cửa hàng</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white py-3 border-b">
        <div className="container mx-auto px-4">
          <nav className="flex text-sm">
            <Link to="/" className="text-gray-500 hover:text-dexin-primary">Trang chủ</Link>
            <ChevronRight className="w-4 h-4 mx-2 text-gray-400" />
            <Link to="/store" className="text-gray-500 hover:text-dexin-primary">Cửa hàng</Link>
            <ChevronRight className="w-4 h-4 mx-2 text-gray-400" />
            <span className="text-dexin-primary">{product.name}</span>
          </nav>
        </div>
      </div>

      {/* Product Detail Section */}
      <motion.div 
        className="container mx-auto px-4 py-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <motion.div 
          className="bg-white rounded-xl shadow-sm overflow-hidden p-6 md:p-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1, duration: 0.3 }}
        >
          <div className="flex flex-col md:flex-row md:space-x-8">
            {/* Product Images */}
            <motion.div 
              className="md:w-1/2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              <div className="flex flex-col-reverse md:flex-row gap-4">
                {/* Hình ảnh chính - bên trái trên desktop */}
                <motion.div 
                  className="relative flex-1 aspect-square bg-gray-50 flex items-center justify-center rounded-lg overflow-hidden"
                  layout
                  layoutId={`product-image-${product.id}`}
                >
                  <motion.img
                    key={currentImage}
                    src={product.images && product.images.length > 0 ? product.images[currentImage] : product.image}
                    alt={product.name}
                    className="max-h-full max-w-full object-contain"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                </motion.div>
                
                {/* Thumbnails - hiển thị dọc bên phải trên desktop */}
                {product.images && product.images.length > 0 && (
                  <LayoutGroup>
                    <div className="flex mt-4 md:mt-0 md:flex-col md:w-1/5 space-x-4 md:space-x-0 md:space-y-4 overflow-x-auto md:overflow-y-auto">
                      {product.images.map((img, index) => (
                        <motion.button
                          key={index}
                          layout
                          className={`flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 ${currentImage === index ? 'border-dexin-primary' : 'border-gray-200'}`}
                          onClick={() => handleImageChange(index)}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ 
                            opacity: 1, 
                            scale: 1,
                            transition: { 
                              type: 'spring', 
                              stiffness: 300, 
                              damping: 15 
                            }
                          }}
                          exit={{ 
                            opacity: 0, 
                            scale: 0.8,
                            transition: { duration: 0.2 }
                          }}
                        >
                          <img 
                            src={img} 
                            alt={`${product.name} - ${index + 1}`} 
                            className="w-full h-full object-cover" 
                            loading="lazy"
                          />
                        </motion.button>
                      ))}
                    </div>
                  </LayoutGroup>
                )}
              </div>
            </motion.div>

            {/* Product Info */}
            <motion.div 
              className="md:w-1/2 mt-8 md:mt-0"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              <motion.h1 
                className="text-3xl font-bold text-gray-800 mb-2"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.3 }}
              >
                {product.name}
              </motion.h1>
              
              {/* Rating */}
              <motion.div 
                className="flex items-center mt-2 mb-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.3 }}
              >
                <div className="flex">
                  {[...Array(5)].map((_, i) => {
                    const starValue = i + 1;
                    const isFullStar = product.rating >= starValue;
                    const isHalfStar = !isFullStar && product.rating > starValue - 1 && product.rating < starValue;
                    
                    return (
                      <motion.svg 
                        key={i} 
                        className={`w-5 h-5 ${isFullStar ? 'text-yellow-400' : isHalfStar ? 'text-yellow-400' : 'text-gray-300'}`} 
                        fill="currentColor" 
                        viewBox="0 0 20 20"
                        initial={{ opacity: 0, scale: 0.5 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.5 + i * 0.1, duration: 0.2 }}
                      >
                        {isHalfStar ? (
                          <>
                            <defs>
                              <linearGradient id={`halfStar-${i}`}>
                                <stop offset="50%" stopColor="currentColor" />
                                <stop offset="50%" stopColor="#D1D5DB" />
                              </linearGradient>
                            </defs>
                            <path 
                              d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" 
                              fill={`url(#halfStar-${i})`}
                            />
                          </>
                        ) : (
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        )}
                      </motion.svg>
                    );
                  })}
                </div>
                <span className="ml-2 text-sm text-gray-600">{product.rating}/5 ({product.reviews})</span>
              </motion.div>

              {/* Price */}
              <motion.div 
                className="mt-4"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.3 }}
              >
                <span className="text-3xl font-bold text-dexin-primary">{formatPrice(product.price)}</span>
              </motion.div>

              {/* Description */}
              <motion.div 
                className="mt-5 pb-6 border-b border-gray-200"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.3 }}
              >
                <p className="text-gray-600 leading-relaxed">{product.description}</p>
              </motion.div>

              {/* Color Variants */}
              {product.variants && product.variants.length > 0 && (
                <motion.div 
                  className="mt-6 pb-6 border-b border-gray-200"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8, duration: 0.3 }}
                >
                  <h3 className="text-base font-semibold text-gray-800 mb-3">Chọn màu sắc</h3>
                  <div className="flex items-center space-x-4 mt-2">
                    {product.variants.map((variant, index) => (
                      <motion.button
                        key={variant.id}
                        className={`w-12 h-12 rounded-full focus:outline-none ${selectedVariant === variant.id ? 'ring-2 ring-dexin-primary ring-offset-2' : ''}`}
                        style={{ backgroundColor: variant.color }}
                        onClick={() => setSelectedVariant(variant.id)}
                        aria-label={variant.name}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.8 + index * 0.1, duration: 0.3 }}
                      ></motion.button>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Quantity */}
              <motion.div 
                className="mt-6 pb-6 border-b border-gray-200"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9, duration: 0.3 }}
              >
                <h3 className="text-base font-semibold text-gray-800 mb-3">Chọn số lượng</h3>
                <div className="inline-flex items-center mt-2 rounded-lg overflow-hidden border border-gray-300 shadow-sm">
                  <motion.button
                    className="h-12 w-12 flex items-center justify-center bg-gray-100 hover:bg-gray-200"
                    onClick={decreaseQuantity}
                    whileHover={{ backgroundColor: "#E5E7EB" }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Minus className="w-5 h-5 text-gray-600" />
                  </motion.button>
                  <motion.div
                    className="w-16 h-12 flex items-center justify-center bg-white border-l border-r border-gray-300"
                    layout
                  >
                    <motion.span 
                      key={quantity} 
                      className="text-gray-800 font-medium"
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      {quantity}
                    </motion.span>
                  </motion.div>
                  <motion.button
                    className="h-12 w-12 flex items-center justify-center bg-gray-100 hover:bg-gray-200"
                    onClick={increaseQuantity}
                    whileHover={{ backgroundColor: "#E5E7EB" }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Plus className="w-5 h-5 text-gray-600" />
                  </motion.button>
                </div>
              </motion.div>

              {/* Action Buttons */}
              <motion.div 
                className="mt-6 flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1, duration: 0.3 }}
              >
                <motion.button 
                  className="w-full py-3 flex items-center justify-center bg-dexin-light hover:bg-pink-600 text-white font-medium rounded-lg"
                  onClick={handleAddToCart}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <ShoppingBag className="w-5 h-5 mr-2" />
                  Thêm vào giỏ hàng
                </motion.button>
                <motion.button 
                  className={`py-3 px-4 flex items-center justify-center rounded-lg ${inWishlist ? 'bg-pink-100 text-dexin-primary' : 'bg-gray-100 text-gray-700'}`}
                  onClick={handleToggleWishlist}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  animate={inWishlist ? { scale: [1, 1.2, 1] } : { scale: 1 }}
                  transition={inWishlist ? { duration: 0.3 } : { duration: 0.1 }}
                >
                  <Heart className={`w-5 h-5 ${inWishlist ? 'fill-dexin-primary text-dexin-primary' : ''}`} />
                </motion.button>
              </motion.div>
            </motion.div>
          </div>

          {/* Product Details */}
          <motion.div 
            className="mt-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.1, duration: 0.4 }}
          >
            {/* Tab navigation */}
            <div className="border-b border-gray-200">
              <div className="flex justify-center text-center">
                <div className="relative mx-4 w-1/3">
                  <motion.button
                    className={`py-4 px-8 font-medium text-xl ${activeTab === 'description' ? 'text-dexin-primary' : 'text-gray-500 hover:text-gray-700'}`}
                    onClick={() => setActiveTab('description')}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.2, duration: 0.3 }}
                  >
                    Mô Tả Sản Phẩm
                  </motion.button>
                  {activeTab === 'description' && (
                    <motion.div 
                      className="absolute bottom-0 left-0 w-full h-0.5 bg-dexin-light"
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: 1 }}
                      transition={{ duration: 0.4 }}
                    />
                  )}
                </div>
                <div className="relative mx-4 w-1/3">
                  <motion.button
                    className={`py-4 px-8 font-medium text-xl ${activeTab === 'reviews' ? 'text-dexin-primary' : 'text-gray-500 hover:text-gray-700'}`}
                    onClick={() => setActiveTab('reviews')}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.25, duration: 0.3 }}
                  >
                    Đánh giá từ khách hàng
                  </motion.button>
                  {activeTab === 'reviews' && (
                    <motion.div 
                      className="absolute bottom-0 left-0 w-full h-0.5 bg-dexin-light"
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: 1 }}
                      transition={{ duration: 0.4 }}
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Tab content */}
            <div className="py-8">
              {/* Mô tả sản phẩm tab */}
              {activeTab === 'description' && (
                <div className="grid md:grid-cols-2 gap-12 items-center">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.2, duration: 0.4 }}
                    className="flex justify-center"
                  >
                    <img
                      src={product.images ? product.images[0] : product.image}
                      alt={product.name}
                      className="w-4/5 h-auto rounded-lg"
                    />
                  </motion.div>
                  <div className="flex flex-col justify-center">
                    {product.details && product.details.map((detail, index) => (
                      <motion.div 
                        key={index} 
                        className="mb-6"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 1.2 + index * 0.1, duration: 0.3 }}
                      >
                        <h3 className="font-semibold text-gray-800 text-lg">{detail.label}:</h3>
                        <p className="text-gray-600 mt-2">{detail.value}</p>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Đánh giá khách hàng tab */}
              {activeTab === 'reviews' && (
                <div className="pt-4">
                  <div className="flex justify-between items-center mb-8">
                    <h2 className="text-2xl font-bold">Tất cả đánh giá <span className="text-gray-500 ml-1 font-normal">({product.reviews})</span></h2>
                    <div className="flex items-center gap-2">
                      <div className="relative">
                        <button className="flex items-center justify-center h-10 w-10 rounded-full border border-gray-200 hover:bg-gray-50">
                          <motion.div
                            whileHover={{ rotate: 180 }}
                            transition={{ duration: 0.3 }}
                          >
                            <SlidersVertical className="h-5 w-5 text-dexin-light" />
                          </motion.div>
                        </button>
                      </div>
                      <div className="relative">
                        <select 
                          className="appearance-none bg-gray-100 py-2 px-4 pr-10 rounded-full text-gray-700 font-medium focus:outline-none focus:ring-2 focus:ring-dexin-light focus:border-transparent"
                          value={reviewFilter}
                          onChange={handleReviewFilterChange}
                        >
                          <option value="newest">Gần Nhất</option>
                          <option value="highest">Đánh giá cao nhất</option>
                          <option value="lowest">Đánh giá thấp nhất</option>
                        </select>
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                          <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                          </svg>
                        </div>
                      </div>
                      <Button variant="primary" className="rounded-full py-2 px-6">Viết đánh giá</Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {sortedReviews.map((review, index) => (
                      <motion.div 
                        key={review.id} 
                        className="border border-gray-100 rounded-lg p-6 bg-white shadow-sm hover:shadow-md transition-shadow duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * index, duration: 0.3 }}
                      >
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-bold">{review.name}</h3>
                            {review.verified && (
                              <span className="text-dexin-primary">
                                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" fill="currentColor" fillOpacity="0.2"/>
                                  <path d="M7.5 12.5L10.5 15.5L16.5 9.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                              </span>
                            )}
                          </div>
                          <button className="text-gray-400 hover:text-gray-600">
                            <MoreHorizontal className="h-5 w-5" />
                          </button>
                        </div>
                        
                        <div className="flex items-center mb-4">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => {
                              const starValue = i + 1;
                              const isFullStar = review.rating >= starValue;
                              const isHalfStar = !isFullStar && review.rating > starValue - 1 && review.rating < starValue;
                              
                              return (
                                <svg 
                                  key={i} 
                                  className={`w-5 h-5 ${isFullStar ? 'text-yellow-400' : isHalfStar ? 'text-yellow-400' : 'text-gray-300'}`} 
                                  fill="currentColor" 
                                  viewBox="0 0 20 20"
                                >
                                  {isHalfStar ? (
                                    <>
                                      <defs>
                                        <linearGradient id={`halfStar-${i}`}>
                                          <stop offset="50%" stopColor="currentColor" />
                                          <stop offset="50%" stopColor="#D1D5DB" />
                                        </linearGradient>
                                      </defs>
                                      <path 
                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" 
                                        fill={`url(#halfStar-${i})`}
                                      />
                                    </>
                                  ) : (
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  )}
                                </svg>
                              );
                            })}
                          </div>
                        </div>
                        
                        <p className="text-gray-700 mb-4 min-h-[3rem]">"{review.comment}"</p>
                        <p className="text-gray-500 text-sm">{review.date}</p>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default ProductDetail; 