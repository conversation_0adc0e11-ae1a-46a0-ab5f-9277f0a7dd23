import React, { memo, useMemo, useCallback, useEffect, useState, Fragment } from 'react';
import { useReducedMotion } from 'motion/react';
import { Heart, ShoppingCart, Eye, X } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useWishlist } from '../../../context/WishlistContext';
import { useCart } from '../../../context/CartContext';
import { toast } from 'react-toastify';
import { Dialog, Transition } from '@headlessui/react';
import ProductVariantModal from '../../../components/common/ProductVariantModal';
import { getProductById } from '../../../data/products';

// Tối ưu hiệu năng bằng cách preload các icon
const preloadIcons = () => {
  // Tạo các SVG paths cho các icon để browser có thể cache
  const iconPaths = {
    heart: 'M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z',
    shoppingCart: 'M9 20a1 1 0 1 0 0 2 1 1 0 0 0 0-2zm0 0h8a1 1 0 1 0 0 2H9M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6',
    eye: 'M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z M12 9a3 3 0 1 0 0 6 3 3 0 0 0 0-6z'
  };

  // Tạo các SVG elements và thêm vào DOM
  Object.entries(iconPaths).forEach(([name, path]) => {
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '0');
    svg.setAttribute('height', '0');
    svg.setAttribute('class', 'absolute invisible');

    const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    pathElement.setAttribute('d', path);
    svg.appendChild(pathElement);

    document.body.appendChild(svg);
  });
};

// Hàm định dạng giá tiền - đưa ra ngoài component để tránh tạo lại mỗi lần render
const formatPrice = (price) => {
  return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);
};

// Tạo sẵn các ngôi sao để tránh tính toán lại mỗi lần render
const StarRating = memo(({ rating, reviews }) => {
  return (
    <div className="flex items-center mt-2">
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <svg 
            key={`star-rating-${i}`} 
            className={`w-4 h-4 ${i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'}`} 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
        ))}
        <span className="ml-1 text-xs text-gray-600">({reviews})</span>
      </div>
    </div>
  );
});

// Tối ưu hóa các icon để tránh re-render không cần thiết
const EyeIcon = memo(() => <Eye className="h-5 w-5" />);
const HeartIcon = memo(({ isFilled }) => {
  return isFilled ? (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="transition-transform duration-300 transform scale-110"
    >
      <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
    </svg>
  ) : (
    <Heart className="h-5 w-5 transition-transform duration-300" />
  );
});
const CartIcon = memo(() => <ShoppingCart className="h-5 w-5" />);

// Product Card Component - sử dụng memo để tránh re-render không cần thiết
const ProductCard = memo(({ product, onQuickView, handleAddToCart }) => {
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  // Tính toán giá tiền một lần duy nhất cho mỗi sản phẩm
  const formattedPrice = useMemo(() => formatPrice(product.price), [product.price]);

  // Sử dụng state để theo dõi trạng thái tải hình ảnh
  const [imageLoaded, setImageLoaded] = useState(false);
  // Sử dụng state để theo dõi trạng thái hover
  const [isHovered, setIsHovered] = useState(false);
  // Kiểm tra xem sản phẩm có trong danh sách yêu thích không
  const inWishlist = isInWishlist(product.id);

  // Xử lý sự kiện khi hình ảnh được tải xong
  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  // Xử lý sự kiện hover
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  // Xử lý thêm/xóa sản phẩm khỏi danh sách yêu thích
  const handleToggleWishlist = useCallback((e) => {
    // Ngăn sự kiện click lan ra ngoài (để không redirect khi click vào nút yêu thích)
    e.stopPropagation();
    e.preventDefault();
    
    if (inWishlist) {
      removeFromWishlist(product.id);
      toast.info('Đã xóa khỏi mục yêu thích', {
        icon: () => <span style={{ fontSize: '1.5rem', marginRight: '10px' }}>💔</span>,
        className: 'toast-message'
      });
    } else {
      addToWishlist(product);
      toast.success('Đã thêm vào mục yêu thích', {
        icon: () => <span style={{ fontSize: '1.5rem', marginRight: '10px' }}>❤️</span>,
        className: 'toast-message'
      });
    }
  }, [product, inWishlist, addToWishlist, removeFromWishlist]);

  // Handle Quick View
  const handleQuickView = useCallback((e) => {
    // Ngăn sự kiện click lan ra ngoài
    e.stopPropagation();
    e.preventDefault();
    
    // Gọi hàm quick view được truyền từ component cha
    onQuickView(product);
  }, [product, onQuickView]);
  
  return (
    <Link to={`/product/${product.id}`} className="block">
      <div
        className="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md will-change-transform will-change-opacity group"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Phần hình ảnh sản phẩm */}
        <div className="relative">
          {/* Placeholder cho hình ảnh đang tải */}
          {!imageLoaded && (
            <div className="absolute inset-0 bg-gray-50 animate-pulse flex items-center justify-center">
              <svg className="w-10 h-10 text-gray-200" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
          )}

          {/* Hình ảnh sản phẩm */}
          <div className="overflow-hidden h-64 flex items-center justify-center bg-gray-50">
            <img
              src={product.image}
              alt={product.name}
              className={`w-auto h-auto max-h-60 max-w-[90%] object-contain content-visibility-auto transition-all duration-300 ease-in-out ${imageLoaded ? 'opacity-100' : 'opacity-0'} ${isHovered ? 'scale-105' : 'scale-100'}`}
              loading="lazy"
              decoding="async"
              onLoad={handleImageLoad}
            />
          </div>

          {/* Quick view button - hiển thị khi hover */}
          {imageLoaded && (
            <button
              className="absolute top-2 right-2 p-2 bg-white rounded-full hover:bg-gray-100 transition-all duration-200 opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100"
              aria-label="Quick view"
              onClick={handleQuickView}
            >
              <EyeIcon />
            </button>
          )}
        </div>

        {/* Nút "Thêm vào giỏ hàng" - hiển thị dưới hình ảnh khi hover */}
        <div
          className="w-full bg-dexin-light text-white py-3 text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          onClick={(e) => handleAddToCart(e, product)}
        >
          <button className="flex items-center justify-center w-full">
            <CartIcon className="mr-2" />
            <span>Thêm vào giỏ hàng</span>
          </button>
        </div>

        {/* Thông tin sản phẩm */}
        <div className="p-4">
          <h3 className="text-base font-medium text-gray-800 line-clamp-1">{product.name}</h3>

          {/* Giá */}
          <div className="mt-2 text-base font-bold text-dexin-primary">
            {formattedPrice}
          </div>

          {/* Rating */}
          <div className="flex justify-between items-center">
            <StarRating rating={product.rating} reviews={product.reviews} />

            {/* Nút yêu thích */}
            <button
              className={`p-1.5 ${inWishlist ? 'text-dexin-primary' : 'text-gray-400 hover:text-dexin-primary'} transition-colors duration-200 relative group`}
              aria-label={inWishlist ? "Remove from wishlist" : "Add to wishlist"}
              onClick={handleToggleWishlist}
            >
              <div className={`transition-all duration-300 ${inWishlist ? 'animate-heartBeat' : ''}`}>
                <HeartIcon isFilled={inWishlist} />
              </div>
            </button>
          </div>
        </div>
      </div>
    </Link>
  );
});

// Main Content Component
const StoreContent = ({
  filteredProducts,
  setSearchTerm,
  setActiveCategory,
  currentPage,
  setCurrentPage,
  productsPerPage
}) => {
  // Kiểm tra nếu người dùng ưu tiên giảm chuyển động
  const prefersReducedMotion = useReducedMotion();

  // Lấy hàm addToCart từ context
  const { addToCart } = useCart();

  // State cho Variant Modal
  const [variantModalOpen, setVariantModalOpen] = useState(false);
  const [selectedProductForVariant, setSelectedProductForVariant] = useState(null);

  // Handle Add to Cart - ở cấp StoreContent
  const handleAddToCart = useCallback((e, product) => {
    // Ngăn sự kiện click lan ra ngoài
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    
    // Lấy thông tin sản phẩm đầy đủ từ data
    const fullProduct = getProductById(product.id);
    
    // Kiểm tra xem sản phẩm có variants không
    if (fullProduct && fullProduct.variants && fullProduct.variants.length > 0) {
      // Nếu có variants, mở modal chọn variant
      setSelectedProductForVariant(fullProduct);
      setVariantModalOpen(true);
    } else {
      // Nếu không có variants, thêm trực tiếp vào giỏ hàng
      addToCart(product);
      
      toast.success(`Đã thêm "${product.name}" vào giỏ hàng`, {
        icon: () => <span style={{ fontSize: '1.2rem', marginRight: '10px' }}>🛒</span>,
        className: 'toast-message',
        toastId: `cart-${product.id}`, // Thêm ID duy nhất cho toast
        autoClose: 2000 // Đóng sau 2 giây
      });
    }
  }, [addToCart]);

  // Handle Add to Cart từ Variant Modal
  const handleAddToCartFromModal = useCallback((productWithVariant) => {
    addToCart(productWithVariant, productWithVariant.quantity);
    
    const displayName = productWithVariant.variantName 
      ? `${productWithVariant.name} (${productWithVariant.variantName})`
      : productWithVariant.name;
      
    toast.success(`Đã thêm "${displayName}" vào giỏ hàng`, {
      icon: () => <span style={{ fontSize: '1.2rem', marginRight: '10px' }}>🛒</span>,
      className: 'toast-message',
      toastId: `variant-cart-${productWithVariant.id}-${productWithVariant.variantId}`,
      autoClose: 2000
    });
  }, [addToCart]);

  // Đóng Variant Modal
  const closeVariantModal = useCallback(() => {
    setVariantModalOpen(false);
    setTimeout(() => {
      setSelectedProductForVariant(null);
    }, 300);
  }, []);

  // Preload icons khi component mount
  useEffect(() => {
    preloadIcons();

    // Thêm một số tối ưu hiệu năng bổ sung
    const optimizePerformance = () => {
      // Thêm event listener để tối ưu hiệu năng khi scroll
      const handleScroll = () => {
        // Sử dụng requestAnimationFrame để tránh blocking main thread
        requestAnimationFrame(() => {
          // Tối ưu hiệu năng khi scroll bằng cách tạm dừng animations
          document.body.classList.add('scrolling');

          // Xóa class sau khi scroll kết thúc
          clearTimeout(window.scrollTimer);
          window.scrollTimer = setTimeout(() => {
            document.body.classList.remove('scrolling');
          }, 150);
        });
      };

      window.addEventListener('scroll', handleScroll, { passive: true });

      return () => {
        window.removeEventListener('scroll', handleScroll);
        clearTimeout(window.scrollTimer);
      };
    };

    return optimizePerformance();
  }, []);

  // Tính toán sản phẩm hiển thị cho trang hiện tại - sử dụng useMemo để tránh tính toán lại
  const currentProducts = useMemo(() => {
    const indexOfLastProduct = currentPage * productsPerPage;
    const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
    return filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  }, [filteredProducts, currentPage, productsPerPage]);

  // Tính tổng số trang - sử dụng useMemo để tránh tính toán lại
  const totalPages = useMemo(() =>
    Math.ceil(filteredProducts.length / productsPerPage),
    [filteredProducts.length, productsPerPage]
  );

  // Xử lý chuyển trang - tối ưu hiệu năng
  const handlePageChange = useCallback((pageNumber) => {
    // Thêm class để tạm thời vô hiệu hóa animations khi chuyển trang
    document.body.classList.add('page-transitioning');

    // Sử dụng setTimeout để tránh blocking main thread
    setTimeout(() => {
      setCurrentPage(pageNumber);

      // Cuộn lên đầu danh sách sản phẩm
      window.scrollTo({
        top: document.getElementById('products')?.offsetTop - 100 || 0,
        behavior: prefersReducedMotion ? 'auto' : 'smooth' // Sử dụng 'auto' cho thiết bị yếu
      });

      // Xóa class sau khi chuyển trang
      setTimeout(() => {
        document.body.classList.remove('page-transitioning');
      }, 100);
    }, 0);
  }, [setCurrentPage, prefersReducedMotion]);

  // Tạo component con cho banner để tránh re-render không cần thiết
  const FavoriteBanner = memo(() => {
    return (
      <div key="favorite-banner" className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Sản phẩm nổi bật</h2>
        <Link to="/wishlist">
          <button className="text-dexin-primary border border-dexin-primary rounded-full px-4 py-2 hover:text-dexin-light transition-colors duration-200 flex items-center">
            <Heart className="h-5 w-5 mr-1" />
            <span>Mục yêu thích</span>
          </button>
        </Link>
      </div>
    );
  });

  // Tạo component con cho trạng thái trống để tránh re-render không cần thiết
  const EmptyState = memo(({ onClearFilter }) => {
    return (
      <div key="empty-state" className="text-center py-12 bg-white rounded-lg shadow-sm">
        <p className="text-gray-500 text-lg">
          Không tìm thấy sản phẩm phù hợp
        </p>
        <button
          className="mt-4 px-4 py-2 bg-dexin-light text-white rounded-md hover:bg-dexin-primary transition-colors duration-200"
          onClick={onClearFilter}
        >
          Xóa bộ lọc
        </button>
      </div>
    );
  });

  // Tạo component con cho nút phân trang để tránh re-render không cần thiết
  const PaginationButton = memo(({ pageNumber, isActive, onClick, children }) => {
    return (
      <button
        key={`page-${pageNumber}`}
        onClick={() => onClick(pageNumber)}
        className={`px-3 py-1 rounded-md transition-colors duration-150 ${
          isActive
            ? 'bg-dexin-primary text-white border border-dexin-primary'
            : 'bg-white border border-gray-300 text-gray-600 hover:bg-dexin-light hover:text-white hover:border-dexin-light'
        }`}
      >
        {children}
      </button>
    );
  });

  // Xử lý xóa bộ lọc
  const handleClearFilter = useCallback(() => {
    setActiveCategory('all');
    setSearchTerm('');
  }, [setActiveCategory, setSearchTerm]);

  // Tạo mảng các nút phân trang - sử dụng useMemo để tránh tính toán lại
  const paginationButtons = useMemo(() => {
    if (totalPages <= 1) return [];

    // Tối ưu hóa cách tạo các nút phân trang
    // Thay vì tạo mảng đầy đủ rồi lọc, chỉ tạo các nút cần thiết
    const buttons = [];

    // Luôn hiển thị nút trang đầu tiên
    buttons.push(
      <PaginationButton
        key={`page-1`}
        pageNumber={1}
        isActive={currentPage === 1}
        onClick={handlePageChange}
      >
        1
      </PaginationButton>
    );

    // Thêm dấu ... nếu cần
    if (currentPage > 3) {
      buttons.push(
        <span key="ellipsis-start" className="px-2 text-gray-600">...</span>
      );
    }

    // Thêm các nút ở giữa (trang hiện tại và các trang xung quanh)
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i === 1 || i === totalPages) continue; // Bỏ qua nếu đã thêm ở trên hoặc sẽ thêm ở dưới

      buttons.push(
        <PaginationButton
          key={`page-${i}`}
          pageNumber={i}
          isActive={currentPage === i}
          onClick={handlePageChange}
        >
          {i}
        </PaginationButton>
      );
    }

    // Thêm dấu ... nếu cần
    if (currentPage < totalPages - 2) {
      buttons.push(
        <span key="ellipsis-end" className="px-2 text-gray-600">...</span>
      );
    }

    // Luôn hiển thị nút trang cuối cùng nếu có nhiều hơn 1 trang
    if (totalPages > 1) {
      buttons.push(
        <PaginationButton
          key={`page-${totalPages}`}
          pageNumber={totalPages}
          isActive={currentPage === totalPages}
          onClick={handlePageChange}
        >
          {totalPages}
        </PaginationButton>
      );
    }

    return buttons;
  }, [totalPages, currentPage, handlePageChange]);

  // State cho Quick View
  const [quickViewProduct, setQuickViewProduct] = useState(null);
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);

  // Hàm mở Quick View
  const openQuickView = useCallback((product) => {
    // Lấy thông tin sản phẩm đầy đủ từ data
    const fullProduct = getProductById(product.id);
    setQuickViewProduct(fullProduct);
    setIsQuickViewOpen(true);
  }, []);

  // Hàm đóng Quick View
  const closeQuickView = useCallback(() => {
    setIsQuickViewOpen(false);
    // Thêm delay để đảm bảo animation hoàn tất trước khi reset
    setTimeout(() => {
      setQuickViewProduct(null);
    }, 300);
  }, []);

  // Handle Add to Cart từ Quick View
  const handleAddToCartFromQuickView = useCallback((product) => {
    // Lấy thông tin sản phẩm đầy đủ
    const fullProduct = getProductById(product.id);
    
    if (fullProduct && fullProduct.variants && fullProduct.variants.length > 0) {
      // Đóng quick view và mở variant modal
      closeQuickView();
      setSelectedProductForVariant(fullProduct);
      setVariantModalOpen(true);
    } else {
      addToCart(product);
      toast.success(`Đã thêm "${product.name}" vào giỏ hàng`, {
        icon: () => <span style={{ fontSize: '1.2rem', marginRight: '10px' }}>🛒</span>,
        className: 'toast-message',
        toastId: `quickview-cart-${product.id}`,
        autoClose: 2000
      });
      closeQuickView();
    }
  }, [addToCart, closeQuickView]);

  return (
    <div className="flex-1">
      {/* Favorite Products Banner */}
      <FavoriteBanner />

      {/* Products Grid - 3 sản phẩm trên một hàng */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {currentProducts.map((product, index) => {
          // Tính toán delay dựa trên index và trang hiện tại
          // Trang đầu tiên có delay dài hơn để tránh giật lag
          const isFirstPage = currentPage === 1;
          const delay = prefersReducedMotion
            ? 0
            : isFirstPage
              ? Math.min(index * 0.03, 0.2) // Giảm delay cho trang đầu tiên
              : Math.min(index * 0.02, 0.1); // Giảm delay hơn nữa cho các trang khác

          return (
            <div
              key={product.id}
              className={`opacity-0 animate-scaleIn ${prefersReducedMotion ? 'animation-duration-200' : 'animation-duration-300'} animation-fill-forwards transform-gpu backface-hidden`}
              data-delay={delay}
              onAnimationStart={(e) => {
                e.currentTarget.style.animationDelay = `${e.currentTarget.dataset.delay}s`;
              }}
            >
              <ProductCard product={product} onQuickView={openQuickView} handleAddToCart={handleAddToCart} />
            </div>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <EmptyState onClearFilter={handleClearFilter} />
      )}

      {/* Pagination */}
      {filteredProducts.length > 0 && totalPages > 1 && (
        <div className="flex justify-center mt-12">
          <nav className="flex items-center space-x-2">
            {/* Nút Previous */}
            {currentPage > 1 && (
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                className="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-600 hover:bg-dexin-light hover:text-white hover:border-dexin-light transition-colors duration-150"
              >
                &laquo;
              </button>
            )}

            {/* Các nút số trang */}
            {paginationButtons}

            {/* Nút Next */}
            {currentPage < totalPages && (
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                className="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-600 hover:bg-dexin-light hover:text-white hover:border-dexin-light transition-colors duration-150"
              >
                &raquo;
              </button>
            )}
          </nav>
        </div>
      )}

      {/* Quick View Modal */}
      <Transition appear show={isQuickViewOpen}>
        <Dialog as="div" className="fixed inset-0 z-50 overflow-y-auto" onClose={closeQuickView}>
          <div className="min-h-screen px-4 flex items-center justify-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
            </Transition.Child>

            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="relative w-full max-w-4xl mx-auto bg-white rounded-2xl shadow-xl p-8">
                {/* Nút đóng */}
                <button 
                  onClick={closeQuickView} 
                  className="absolute top-4 right-4 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                >
                  <X className="w-6 h-6 text-gray-600" />
                </button>

                {quickViewProduct && (
                  <div className="grid md:grid-cols-2 gap-8">
                    {/* Hình ảnh sản phẩm */}
                    <div className="flex items-center justify-center">
                      <img 
                        src={quickViewProduct.image} 
                        alt={quickViewProduct.name} 
                        className="max-h-96 object-contain" 
                      />
                    </div>

                    {/* Thông tin sản phẩm */}
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 mb-4">{quickViewProduct.name}</h2>
                      
                      {/* Rating */}
                      <div className="flex items-center mb-4">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <svg 
                              key={`quick-view-star-${i}`} 
                              className={`w-5 h-5 ${i < Math.floor(quickViewProduct.rating) ? 'text-yellow-400' : 'text-gray-300'}`} 
                              fill="currentColor" 
                              viewBox="0 0 20 20"
                            >
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          ))}
                        </div>
                        <span className="ml-2 text-sm text-gray-600">({quickViewProduct.reviews})</span>
                      </div>

                      {/* Giá */}
                      <div className="text-3xl font-bold text-dexin-primary mb-4">
                        {formatPrice(quickViewProduct.price)}
                      </div>

                      {/* Mô tả ngắn */}
                      <p className="text-gray-600 mb-6">{quickViewProduct.description}</p>

                      {/* Nút thêm vào giỏ hàng */}
                      <div className="flex space-x-4">
                        <Link 
                          to={`/product/${quickViewProduct.id}`} 
                          className="flex-1 bg-dexin-light text-white py-3 rounded-lg text-center hover:bg-pink-600 transition-colors"
                        >
                          Xem chi tiết
                        </Link>
                        <button 
                          onClick={() => handleAddToCartFromQuickView(quickViewProduct)}
                          className="flex-1 bg-white border border-dexin-light text-dexin-light py-3 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex items-center justify-center">
                            <ShoppingCart className="h-5 w-5 mr-2" />
                            <span>Thêm vào giỏ hàng</span>
                          </div>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition>

      {/* Product Variant Modal */}
      <ProductVariantModal
        isOpen={variantModalOpen}
        onClose={closeVariantModal}
        product={selectedProductForVariant}
        onAddToCart={handleAddToCartFromModal}
      />
    </div>
  );
};

export default StoreContent;
