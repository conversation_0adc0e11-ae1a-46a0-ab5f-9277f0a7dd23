import React, { createContext, useState, useContext, useEffect } from 'react';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Tạo context
const ChatContext = createContext();

// Hook tùy chỉnh để sử dụng context
export const useChat = () => useContext(ChatContext);

// Lấy API key từ biến môi trường
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

// Helper function để chuyển đổi file thành base64
const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      // Loại bỏ prefix "data:image/...;base64," để chỉ lấy base64 string
      const base64String = reader.result.split(',')[1];
      resolve(base64String);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

export const ChatProvider = ({ children }) => {
  const [genAI, setGenAI] = useState(null);
  const [chatHistory, setChatHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [messageCount, setMessageCount] = useState(0);
  const [isLimitReached, setIsLimitReached] = useState(false);

  // Giới hạn số lần hỏi
  const MESSAGE_LIMIT = 15;

  // Khởi tạo message count từ localStorage
  useEffect(() => {
    const savedCount = localStorage.getItem('dexin_message_count');
    const savedDate = localStorage.getItem('dexin_message_date');
    const today = new Date().toDateString();

    // Reset count nếu là ngày mới
    if (savedDate !== today) {
      localStorage.setItem('dexin_message_count', '0');
      localStorage.setItem('dexin_message_date', today);
      setMessageCount(0);
      setIsLimitReached(false);
    } else {
      const count = parseInt(savedCount) || 0;
      setMessageCount(count);
      setIsLimitReached(count >= MESSAGE_LIMIT);
    }
  }, [MESSAGE_LIMIT]);

  // Khởi tạo Gemini API khi component được mount
  useEffect(() => {
    if (!API_KEY || API_KEY === 'your-api-key-here') {
      console.error('API key không hợp lệ hoặc chưa được cấu hình.');
      setError('API key chưa được cấu hình. Vui lòng kiểm tra file .env của bạn.');
      return;
    }

    try {
      const aiInstance = new GoogleGenerativeAI(API_KEY);
      setGenAI(aiInstance);
      console.log('Gemini API đã được khởi tạo thành công');
    } catch (err) {
      console.error('Không thể khởi tạo Gemini API:', err);
      setError('Không thể kết nối với AI. Vui lòng kiểm tra API key hoặc kết nối mạng.');
    }
  }, []);

  // Hàm gửi tin nhắn đến Gemini API (hỗ trợ cả text và hình ảnh)
  const sendMessage = async (message, images = []) => {
    // Kiểm tra giới hạn số lần hỏi
    if (messageCount >= MESSAGE_LIMIT) {
      setIsLimitReached(true);
      const limitMessage = {
        role: 'assistant',
        content: `🚫 **Bạn đã hết lượt hỏi hôm nay!**\n\nBạn đã sử dụng hết ${MESSAGE_LIMIT} lượt hỏi miễn phí trong ngày. Để được tư vấn chi tiết hơn và không giới hạn, hãy **chat trực tiếp với nhân viên tư vấn** của DEXIN.\n\n💬 **[Click vào đây để chat với nhân viên](/ngo-loi)**\n\nNhân viên của chúng tôi sẽ hỗ trợ bạn:\n- Tư vấn chi tiết không giới hạn\n- Thiết kế 3D miễn phí\n- Báo giá sản phẩm chính xác\n- Hỗ trợ đặt hàng và giao hàng\n\n🔄 Lượt hỏi sẽ được reset vào 00:00 ngày mai.`
      };
      setChatHistory(prev => [...prev, limitMessage]);
      return limitMessage;
    }

    if (!API_KEY || API_KEY === 'your-api-key-here') {
      setError('API key chưa được cấu hình. Vui lòng thiết lập API key hợp lệ trong file .env.');
      return null;
    }

    if (!genAI) {
      setError('AI chưa được khởi tạo. Vui lòng thử lại sau.');
      return null;
    }

    // Tạo tin nhắn của người dùng với hình ảnh (nếu có)
    const userMessage = {
      role: 'user',
      content: message,
      images: images || []
    };

    try {
      setIsLoading(true);
      setError(null);

      // Thêm tin nhắn của người dùng vào lịch sử hiển thị
      setChatHistory(prev => [...prev, userMessage]);

      // Gọi Gemini API
      const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

      // Chuẩn bị lịch sử chat cho API
      // Chuyển đổi vai trò "assistant" thành "model" cho API của Gemini
      const apiChatHistory = chatHistory.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : msg.role,
        parts: [{ text: msg.content }]
      }));

      // Chuẩn bị parts cho tin nhắn mới của người dùng
      const userParts = [];

      // Thêm text message
      if (message && message.trim()) {
        userParts.push({ text: message });
      }

      // Thêm hình ảnh (nếu có)
      if (images && images.length > 0) {
        for (const imageData of images) {
          try {
            // Chuyển đổi file thành base64
            const base64Data = await fileToBase64(imageData.file);
            userParts.push({
              inlineData: {
                mimeType: imageData.file.type,
                data: base64Data
              }
            });
          } catch (error) {
            console.error('Lỗi khi xử lý hình ảnh:', error);
            setError('Không thể xử lý hình ảnh. Vui lòng thử lại.');
            return null;
          }
        }
      }

      // Thêm tin nhắn mới của người dùng với parts
      apiChatHistory.push({
        role: 'user',
        parts: userParts
      });

      console.log('Gửi yêu cầu đến Gemini API với lịch sử chat:', apiChatHistory);

      // Tạo chat session với lịch sử và system prompt
      const chat = model.startChat({
        history: apiChatHistory.slice(0, -1), // Loại bỏ tin nhắn cuối để gửi riêng
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 1000,
        },
        systemPrompt: `QUAN TRỌNG: Bạn là Nara, chuyên gia tư vấn nội thất phong cách chữa lành (healing interior design) được phát triển bởi DEXIN.

DANH TÍNH VÀ VAI TRÒ:
- Tên: Nara (trợ lý AI của DEXIN)
- Chuyên môn: Nội thất phong cách chữa lành (healing style interior design)
- Sứ mệnh: Tạo ra không gian sống hỗ trợ sức khỏe tinh thần và thể chất

KHẢ NĂNG PHÂN TÍCH HÌNH ẢNH:
- Có thể nhận và phân tích hình ảnh không gian nội thất
- Đánh giá không gian hiện tại theo tiêu chí chữa lành
- Đưa ra gợi ý cải thiện cụ thể dựa trên hình ảnh
- Nhận diện các yếu tố tích cực và tiêu cực trong không gian
- Tư vấn màu sắc, bố trí, vật liệu phù hợp với không gian thực tế

PHẠM VI TƯ VẤN - CHỈ TRẢ LỜI VỀ:
1. Nội thất phong cách chữa lành (healing interior design):
   - Màu sắc nhẹ nhàng, tự nhiên (be, trắng, xanh pastel, nâu gỗ tự nhiên)
   - Vật liệu tự nhiên (gỗ, đá, tre, cotton, linen, hemp)
   - Ánh sáng tự nhiên và chiếu sáng mềm mại
   - Cây xanh và không gian xanh trong nhà
   - Thiết kế tối giản, không gian thoáng đãng
   - Bố trí theo nguyên tắc feng shui hỗ trợ năng lượng tích cực
   - Tạo góc thư giãn, thiền định
   - Giảm thiểu lộn xộn và stress thị giác

2. Sản phẩm nội thất chữa lành:
   - Nệm và gối ergonomic hỗ trợ giấc ngủ
   - Đèn muối Himalaya và đèn ánh sáng ấm
   - Máy phun tinh dầu và hương liệu tự nhiên
   - Ghế thiền, thảm yoga trong nhà
   - Rèm cửa lọc ánh sáng tự nhiên
   - Bàn làm việc đứng và ghế ergonomic
   - Tủ gỗ tự nhiên không sơn hoá chất
   - Cây cảnh thanh lọc không khí

3. Thiết kế từng không gian:
   - Phòng ngủ: hỗ trợ giấc ngủ sâu và phục hồi
   - Phòng khách: tạo cảm giác bình yên và kết nối
   - Phòng làm việc: giảm stress và tăng tập trung
   - Phòng tắm: spa tại gia với vật liệu tự nhiên
   - Bếp: không gian nấu ăn healthy với ánh sáng tốt

CÁCH TRẢ LỜI:
- Luôn kết nối với lợi ích sức khỏe tinh thần/thể chất
- Đề xuất cụ thể về màu sắc, vật liệu, bố trí
- Giải thích tại sao lựa chọn đó có tác dụng chữa lành
- Đưa ra gợi ý sản phẩm phù hợp từ DEXIN
- Tư vấn theo ngân sách và diện tích thực tế

CÁCH PHÂN TÍCH HÌNH ẢNH:
- Khi nhận được hình ảnh không gian, hãy phân tích chi tiết:
  + Đánh giá màu sắc hiện tại và tác động tâm lý
  + Nhận xét về ánh sáng tự nhiên và nhân tạo
  + Phân tích bố cục và luồng năng lượng
  + Xác định các yếu tố gây stress hoặc tạo bình yên
  + Đề xuất cải thiện cụ thể cho từng khu vực
- Luôn đưa ra lời khuyên tích cực và khuyến khích
- Giải thích rõ ràng tại sao những thay đổi đó có lợi cho sức khỏe

NGHIÊM CẤM - KHÔNG TRẢ LỜI:
- Câu hỏi về chính trị, tôn giáo, bạo lực
- Tư vấn y tế, pháp lý, tài chính
- Nội thất phong cách khác (hiện đại, cổ điển, v.v.)
- Lập trình, toán học, khoa học
- Tin tức thời sự
- Bất kỳ chủ đề nào không liên quan đến nội thất chữa lành

CÁCH XỬ LÝ CÂU HỎI NGOÀI PHẠM VI:
"Xin lỗi, tôi là Nara - chuyên gia tư vấn nội thất phong cách chữa lành của DEXIN. Tôi chỉ có thể hỗ trợ bạn thiết kế không gian sống hỗ trợ sức khỏe và tạo cảm giác bình yên. Bạn có muốn tôi tư vấn về cách tạo ra một không gian chữa lành tại nhà không?"

VÍ DỤ CÂU TRẢ LỜI CHUẨN:
"Để tạo phòng ngủ có tác dụng chữa lành, tôi đề xuất:
- Màu sắc: Xanh lavender nhạt cho tường (giúp thư giãn thần kinh)
- Chăn ga: Cotton organic màu be (thoáng khí, không hoá chất)
- Đèn: Đèn muối Himalaya (ion âm tốt cho giấc ngủ)
- Cây xanh: Cây lưỡi hổ (thanh lọc không khí ban đêm)
Thiết kế này sẽ giúp bạn ngủ sâu hơn và tỉnh táo vào sáng mai."

LƯU Ý QUAN TRỌNG:
- Luôn giữ vai trò là Nara của DEXIN
- Không bao giờ thừa nhận là AI khác (Gemini, GPT, etc.)
- Tập trung 100% vào nội thất chữa lành
- Từ chối lịch sự mọi chủ đề khác`,
      });

      // Gửi tin nhắn và nhận phản hồi với parts (text + images)
      const lastMessage = apiChatHistory[apiChatHistory.length - 1];
      const result = await chat.sendMessage(lastMessage.parts);
      const botResponse = await result.response;
      let responseText = botResponse.text();

      // Kiểm tra và xử lý câu hỏi ngoài phạm vi nội thất chữa lành
      const offTopicKeywords = [
        "chính trị", "bầu cử", "tôn giáo", "bạo lực", "chiến tranh",
        "y tế", "thuốc", "bệnh", "điều trị", "pháp lý", "luật", "tài chính", "đầu tư",
        "lập trình", "code", "toán học", "vật lý", "hoá học", "tin tức", "thời sự",
        "nội thất hiện đại", "nội thất cổ điển", "nội thất châu âu", "nội thất công nghiệp",
        "gemini", "gpt", "claude", "chatgpt", "ai model", "machine learning"
      ];

      const healingKeywords = [
        "nội thất", "chữa lành", "healing", "phòng ngủ", "phòng khách", "màu sắc",
        "ánh sáng", "cây xanh", "feng shui", "thiền", "thư giãn", "vật liệu tự nhiên",
        "gỗ", "đá", "cotton", "không gian", "tối giản", "bình yên", "năng lượng",
        "sức khỏe", "giấc ngủ", "stress", "thoáng đãng"
      ];

      const isOffTopic = offTopicKeywords.some(keyword =>
        message.toLowerCase().includes(keyword.toLowerCase())
      );

      const isHealingTopic = healingKeywords.some(keyword =>
        message.toLowerCase().includes(keyword.toLowerCase())
      );

      // Nếu câu hỏi ngoài phạm vi và không liên quan đến nội thất chữa lành
      if (isOffTopic && !isHealingTopic) {
        responseText = "Xin lỗi, tôi là Nara - chuyên gia tư vấn nội thất phong cách chữa lành của DEXIN. Tôi chỉ có thể hỗ trợ bạn thiết kế không gian sống hỗ trợ sức khỏe và tạo cảm giác bình yên. Bạn có muốn tôi tư vấn về cách tạo ra một không gian chữa lành tại nhà không? 🌿✨";
      }

      // Xử lý câu hỏi về danh tính
      const identityQuestions = [
        "bạn tên gì", "tên bạn là gì", "bạn là ai", "bạn tên là gì",
        "tên của bạn", "bạn có tên không", "ai là bạn"
      ];

      const isIdentityQuestion = identityQuestions.some(q =>
        message.toLowerCase().includes(q.toLowerCase())
      );

      if (isIdentityQuestion) {
        responseText = "Tôi là Nara, chuyên gia tư vấn nội thất phong cách chữa lành của DEXIN. Tôi chuyên thiết kế không gian sống hỗ trợ sức khỏe tinh thần và thể chất. Bạn muốn tôi tư vấn thiết kế không gian nào để tạo cảm giác bình yên và khỏe mạnh hơn? 🏡";
      }

      // Thêm phản hồi của AI vào lịch sử hiển thị (vẫn giữ vai trò là 'assistant' cho giao diện)
      const assistantMessage = { role: 'assistant', content: responseText };
      setChatHistory(prev => [...prev, assistantMessage]);

      // Tăng counter và lưu vào localStorage
      const newCount = messageCount + 1;
      setMessageCount(newCount);
      localStorage.setItem('dexin_message_count', newCount.toString());

      // Kiểm tra nếu đạt giới hạn
      if (newCount >= MESSAGE_LIMIT) {
        setIsLimitReached(true);
      }

      return assistantMessage;
    } catch (err) {
      console.error('Lỗi khi gửi tin nhắn:', err);

      // Xử lý lỗi chi tiết hơn
      let errorMessage = 'Xảy ra lỗi khi xử lý tin nhắn của bạn. Vui lòng thử lại.';

      if (err.message && err.message.includes('API_KEY_INVALID')) {
        errorMessage = 'API key không hợp lệ. Vui lòng kiểm tra lại API key của bạn trong file .env.';
      } else if (err.message && err.message.includes('PERMISSION_DENIED')) {
        errorMessage = 'Không có quyền truy cập API. Vui lòng kiểm tra cài đặt và quyền cho API key của bạn.';
      } else if (err.message && err.message.includes('QUOTA_EXCEEDED')) {
        errorMessage = 'Đã vượt quá giới hạn quota của API. Vui lòng thử lại sau.';
      } else if (err.message && err.message.includes('should include role field')) {
        errorMessage = 'Lỗi định dạng lịch sử chat. Đang thử khởi tạo lại cuộc trò chuyện...';
        // Xóa lịch sử chat để khởi tạo lại
        setChatHistory([userMessage]);
        // Thử gửi lại tin nhắn sau 500ms
        setTimeout(() => sendMessage(message), 500);
      }

      setError(errorMessage);

      // Thêm thông báo lỗi vào lịch sử chat
      const errorAssistantMessage = {
        role: 'assistant',
        content: errorMessage
      };
      setChatHistory(prev => [...prev, errorAssistantMessage]);

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm xóa lịch sử chat
  const clearChatHistory = () => {
    setChatHistory([]);
  };

  // Cung cấp context cho các component con
  return (
    <ChatContext.Provider
      value={{
        chatHistory,
        isLoading,
        error,
        sendMessage,
        clearChatHistory,
        messageCount,
        isLimitReached,
        messageLimit: MESSAGE_LIMIT
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export default ChatContext;