import React from 'react';
import { motion } from 'motion/react';

const MessagesList = React.memo(({ messages }) => (
  <div className="flex-grow overflow-y-auto p-6 bg-white">
    {messages.map((message, index) => (
      <motion.div
        key={message.id}
        className={`mb-4 sm:mb-6 flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.3,
          delay: index * 0.1,
          ease: "easeOut"
        }}
      >
        {message.sender === 'staff' && (
          <motion.div
            className="mr-2 sm:mr-3 self-end mb-1"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.2 }}
          >
            <div className="relative w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0">
              <motion.img
                src="/images/Avatar.png"
                alt="Tư vấn viê<PERSON><PERSON><PERSON><PERSON>"
                className="w-full h-full rounded-full object-cover"
                whileHover={{ rotate: 10 }}
                transition={{ duration: 0.2 }}
              />
            </div>
          </motion.div>
        )}
        <div className="flex flex-col max-w-[75%] sm:max-w-[70%]">
          {message.sender === 'staff' && (
            <motion.div
              className="flex items-center mb-1"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <p className="text-sm sm:text-sm text-gray-800 mr-auto">Tư vấn viên Thiên Ngân</p>
              <span className="text-xs sm:text-xs text-gray-500 ml-2">{message.time}</span>
            </motion.div>
          )}
          <motion.div
            className={`px-3 sm:px-4 py-2 sm:py-3 shadow-sm break-words ${
              message.sender === 'user'
                ? 'bg-dexin-light-20 text-dark rounded-tl-xl sm:rounded-tl-2xl rounded-tr-xl sm:rounded-tr-2xl rounded-bl-xl sm:rounded-bl-2xl'
                : 'bg-white border-2 border-dexin-light-50 rounded-tr-xl sm:rounded-tr-2xl rounded-tl-xl sm:rounded-tl-2xl rounded-br-xl sm:rounded-br-2xl text-gray-800'
            }`}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            {/* Render message content based on type */}
            {message.type === 'files' && message.files ? (
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700 mb-2">📎 Files ({message.files.length})</p>
                {message.files.slice(0, 3).map((file, index) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium text-gray-700 truncate">{file.name}</p>
                      <p className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    </div>
                  </div>
                ))}
                {message.files.length > 3 && (
                  <p className="text-xs text-gray-500">+{message.files.length - 3} file khác</p>
                )}
              </div>
            ) : message.type === 'images' && message.images ? (
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700 mb-2">🖼️ Images ({message.images.length})</p>
                <div className="grid grid-cols-2 gap-2">
                  {message.images.slice(0, 4).map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={image.preview}
                        alt={image.name}
                        className="w-full h-20 object-cover rounded-lg"
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b-lg truncate">
                        {image.name}
                      </div>
                    </div>
                  ))}
                  {message.images.length > 4 && (
                    <div className="w-full h-20 bg-gray-200 rounded-lg flex items-center justify-center text-sm text-gray-600">
                      +{message.images.length - 4} ảnh
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <p className="mb-0.5 text-sm sm:text-sm leading-relaxed whitespace-pre-wrap">
                {typeof message.text === 'string' ? message.text : 'Tin nhắn không hợp lệ'}
              </p>
            )}

            {message.sender === 'user' && (
              <p className="text-xs sm:text-xs text-gray-500 text-right mt-1">
                {message.time}
              </p>
            )}
          </motion.div>
        </div>
        {message.sender === 'user' && (
          <motion.div
            className="ml-2 sm:ml-3 self-end mb-1"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-dexin-light-50 flex items-center justify-center text-white font-medium shadow-sm flex-shrink-0"
              whileHover={{ rotate: -10 }}
              transition={{ duration: 0.2 }}
            >
              B
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    ))}

    <motion.div
      className="relative flex justify-center items-center my-4 sm:my-6"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <div className="border-t border-dexin-light-20 absolute w-full"></div>
      <motion.div
        className="bg-white px-4 z-10"
        whileHover={{ scale: 1.1 }}
        transition={{ duration: 0.2 }}
      >
        <span className="text-[10px] sm:text-xs text-dexin-light mx-3 bg-white px-4 font-medium">Tin nhắn mới</span>
      </motion.div>
    </motion.div>
  </div>
));

export default MessagesList;