import{r as e,I as a,J as i,K as t,j as s,M as n,t as r,u as l,L as c,B as o,G as d,m,v as x,H as h,y as g}from"./index-DdBL2cja.js";import{g as y}from"./products-C2yRfJkG.js";import{M as p}from"./minus-Fx_j7jOv.js";import{P as u}from"./plus-66Jg-RVc.js";import{S as j}from"./sliders-vertical-DIr8HYFG.js";import{E as f}from"./ellipsis-DahYom63.js";const v=e.createContext(null);const N=e=>!e.isLayoutDirty&&e.willUpdate(!1);function b(){const e=new Set,a=new WeakMap,i=()=>e.forEach(N);return{add:t=>{e.add(t),a.set(t,t.addEventListener("willUpdate",i))},remove:t=>{e.delete(t);const s=a.get(t);s&&(s(),a.delete(t)),i()},dirty:i}}const w=e=>!0===e,C=({children:n,id:r,inherit:l=!0})=>{const c=e.useContext(t),o=e.useContext(v),[d,m]=function(){const t=function(){const i=e.useRef(!1);return a((()=>(i.current=!0,()=>{i.current=!1})),[]),i}(),[s,n]=e.useState(0),r=e.useCallback((()=>{t.current&&n(s+1)}),[s]);return[e.useCallback((()=>i.postRender(r)),[r]),s]}(),x=e.useRef(null),h=c.id||o;null===x.current&&((e=>w(!0===e)||"id"===e)(l)&&h&&(r=r?h+"-"+r:h),x.current={id:r,group:w(l)&&c.group||b()});const g=e.useMemo((()=>({...x.current,forceRender:d})),[m]);return s.jsx(t.Provider,{value:g,children:n})},k=[{id:1,name:"Minh Hoàng",verified:!0,rating:5,comment:"Ghế rất đẹp. Đóng gói cẩn thận, giao hàng nhanh. Rất hài lòng!",date:"10/03/2025"},{id:2,name:"Lan Phương",verified:!0,rating:5,comment:"Ghế có thiết kế tinh tế, khung gỗ chắc chắn, phù hợp với nhiều không gian.",date:"12/03/2025"},{id:3,name:"Thảo Vy",verified:!0,rating:4.5,comment:"Ghế chắc chắn nhưng hơi nặng, di chuyển không quá linh hoạt.",date:"14/03/2025"},{id:4,name:"Huyền My",verified:!0,rating:5,comment:"Nệm êm ái, ngồi lâu không bị mỏi, rất thích hợp để làm việc. Nhưng giao hàng hơi chậm",date:"16/03/2025"},{id:5,name:"Đức Anh",verified:!0,rating:3,comment:"Gốc chân ghế hơi sắc, để gây trầy sàn nếu không có lót cao su!",date:"17/03/2025"},{id:6,name:"Quang Tú",verified:!0,rating:4.5,comment:"Giá cả hợp lý so với chất lượng, rất đáng để đầu tư.",date:"20/03/2025"}],S=()=>{const{productId:a}=n(),{addToWishlist:i,removeFromWishlist:t,isInWishlist:v}=r(),{addToCart:N}=l(),[b,w]=e.useState(null),[S,M]=e.useState(!0),[T,D]=e.useState(1),[$,E]=e.useState(null),[H,L]=e.useState(0),[z,B]=e.useState("description"),[G,I]=e.useState("newest");e.useEffect((()=>{(()=>{M(!0);try{setTimeout((()=>{const e=y(parseInt(a));e&&(w(e),e.variants&&e.variants.length>0&&E(e.variants[0].id)),M(!1)}),500)}catch(e){M(!1)}})()}),[a]);const R=!!b&&v(b.id),F=e.useMemo((()=>{let e=[...k];switch(G){case"newest":e.sort(((e,a)=>new Date(a.date.split("/").reverse().join("-"))-new Date(e.date.split("/").reverse().join("-"))));break;case"highest":e.sort(((e,a)=>a.rating-e.rating));break;case"lowest":e.sort(((e,a)=>e.rating-a.rating))}return e}),[G]);return S?s.jsx("div",{className:"container mx-auto px-4 py-12 flex items-center justify-center min-h-[60vh]",children:s.jsx("div",{className:"animate-pulse",children:s.jsx("div",{className:"w-12 h-12 border-4 border-dexin-primary border-t-transparent rounded-full animate-spin"})})}):b?s.jsxs("div",{className:"bg-gray-50",children:[s.jsx("div",{className:"bg-white py-3 border-b",children:s.jsx("div",{className:"container mx-auto px-4",children:s.jsxs("nav",{className:"flex text-sm",children:[s.jsx(c,{to:"/",className:"text-gray-500 hover:text-dexin-primary",children:"Trang chủ"}),s.jsx(d,{className:"w-4 h-4 mx-2 text-gray-400"}),s.jsx(c,{to:"/store",className:"text-gray-500 hover:text-dexin-primary",children:"Cửa hàng"}),s.jsx(d,{className:"w-4 h-4 mx-2 text-gray-400"}),s.jsx("span",{className:"text-dexin-primary",children:b.name})]})})}),s.jsx(m.div,{className:"container mx-auto px-4 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:s.jsxs(m.div,{className:"bg-white rounded-xl shadow-sm overflow-hidden p-6 md:p-8",initial:{opacity:0},animate:{opacity:1},transition:{delay:.1,duration:.3},children:[s.jsxs("div",{className:"flex flex-col md:flex-row md:space-x-8",children:[s.jsx(m.div,{className:"md:w-1/2",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.2,duration:.4},children:s.jsxs("div",{className:"flex flex-col-reverse md:flex-row gap-4",children:[s.jsx(m.div,{className:"relative flex-1 aspect-square bg-gray-50 flex items-center justify-center rounded-lg overflow-hidden",layout:!0,layoutId:`product-image-${b.id}`,children:s.jsx(m.img,{src:b.images&&b.images.length>0?b.images[H]:b.image,alt:b.name,className:"max-h-full max-w-full object-contain",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3}},H)}),b.images&&b.images.length>0&&s.jsx(C,{children:s.jsx("div",{className:"flex mt-4 md:mt-0 md:flex-col md:w-1/5 space-x-4 md:space-x-0 md:space-y-4 overflow-x-auto md:overflow-y-auto",children:b.images.map(((e,a)=>s.jsx(m.button,{layout:!0,className:"flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 "+(H===a?"border-dexin-primary":"border-gray-200"),onClick:()=>(e=>{L(e)})(a),whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1,transition:{type:"spring",stiffness:300,damping:15}},exit:{opacity:0,scale:.8,transition:{duration:.2}},children:s.jsx("img",{src:e,alt:`${b.name} - ${a+1}`,className:"w-full h-full object-cover",loading:"lazy"})},a)))})})]})}),s.jsxs(m.div,{className:"md:w-1/2 mt-8 md:mt-0",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.3,duration:.4},children:[s.jsx(m.h1,{className:"text-3xl font-bold text-gray-800 mb-2",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:.4,duration:.3},children:b.name}),s.jsxs(m.div,{className:"flex items-center mt-2 mb-4",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5,duration:.3},children:[s.jsx("div",{className:"flex",children:[...Array(5)].map(((e,a)=>{const i=a+1,t=b.rating>=i,n=!t&&b.rating>i-1&&b.rating<i;return s.jsx(m.svg,{className:"w-5 h-5 "+(t||n?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{delay:.5+.1*a,duration:.2},children:n?s.jsxs(s.Fragment,{children:[s.jsx("defs",{children:s.jsxs("linearGradient",{id:`halfStar-${a}`,children:[s.jsx("stop",{offset:"50%",stopColor:"currentColor"}),s.jsx("stop",{offset:"50%",stopColor:"#D1D5DB"})]})}),s.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z",fill:`url(#halfStar-${a})`})]}):s.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},a)}))}),s.jsxs("span",{className:"ml-2 text-sm text-gray-600",children:[b.rating,"/5 (",b.reviews,")"]})]}),s.jsx(m.div,{className:"mt-4",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6,duration:.3},children:s.jsx("span",{className:"text-3xl font-bold text-dexin-primary",children:(W=b.price,new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND",minimumFractionDigits:0,maximumFractionDigits:0}).format(W))})}),s.jsx(m.div,{className:"mt-5 pb-6 border-b border-gray-200",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.7,duration:.3},children:s.jsx("p",{className:"text-gray-600 leading-relaxed",children:b.description})}),b.variants&&b.variants.length>0&&s.jsxs(m.div,{className:"mt-6 pb-6 border-b border-gray-200",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.8,duration:.3},children:[s.jsx("h3",{className:"text-base font-semibold text-gray-800 mb-3",children:"Chọn màu sắc"}),s.jsx("div",{className:"flex items-center space-x-4 mt-2",children:b.variants.map(((e,a)=>s.jsx(m.button,{className:"w-12 h-12 rounded-full focus:outline-none "+($===e.id?"ring-2 ring-dexin-primary ring-offset-2":""),style:{backgroundColor:e.color},onClick:()=>E(e.id),"aria-label":e.name,whileHover:{scale:1.1},whileTap:{scale:.9},initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.8+.1*a,duration:.3}},e.id)))})]}),s.jsxs(m.div,{className:"mt-6 pb-6 border-b border-gray-200",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.9,duration:.3},children:[s.jsx("h3",{className:"text-base font-semibold text-gray-800 mb-3",children:"Chọn số lượng"}),s.jsxs("div",{className:"inline-flex items-center mt-2 rounded-lg overflow-hidden border border-gray-300 shadow-sm",children:[s.jsx(m.button,{className:"h-12 w-12 flex items-center justify-center bg-gray-100 hover:bg-gray-200",onClick:()=>{D((e=>e>1?e-1:1))},whileHover:{backgroundColor:"#E5E7EB"},whileTap:{scale:.9},children:s.jsx(p,{className:"w-5 h-5 text-gray-600"})}),s.jsx(m.div,{className:"w-16 h-12 flex items-center justify-center bg-white border-l border-r border-gray-300",layout:!0,children:s.jsx(m.span,{className:"text-gray-800 font-medium",initial:{opacity:0,y:-5},animate:{opacity:1,y:0},transition:{duration:.2},children:T},T)}),s.jsx(m.button,{className:"h-12 w-12 flex items-center justify-center bg-gray-100 hover:bg-gray-200",onClick:()=>{D((e=>e+1))},whileHover:{backgroundColor:"#E5E7EB"},whileTap:{scale:.9},children:s.jsx(u,{className:"w-5 h-5 text-gray-600"})})]})]}),s.jsxs(m.div,{className:"mt-6 flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:1,duration:.3},children:[s.jsxs(m.button,{className:"w-full py-3 flex items-center justify-center bg-dexin-light hover:bg-pink-600 text-white font-medium rounded-lg",onClick:()=>{if(!b)return;const e=b.variants&&b.variants.find((e=>e.id===$)),a={id:b.id,name:b.name,price:b.price,image:b.image,quantity:T,variantId:e?e.id:null,variantName:e?e.name:"",variantColor:e?e.color:""};N(a,T);const i=e?`${b.name} (${e.name})`:b.name;g.success(`Đã thêm "${i}" vào giỏ hàng`,{icon:()=>s.jsx("span",{style:{fontSize:"1.2rem",marginRight:"10px"},children:"🛒"}),className:"toast-message",toastId:`detail-cart-${b.id}-${(null==e?void 0:e.id)||"no-variant"}`,autoClose:2e3})},whileHover:{scale:1.02},whileTap:{scale:.98},children:[s.jsx(x,{className:"w-5 h-5 mr-2"}),"Thêm vào giỏ hàng"]}),s.jsx(m.button,{className:"py-3 px-4 flex items-center justify-center rounded-lg "+(R?"bg-pink-100 text-dexin-primary":"bg-gray-100 text-gray-700"),onClick:()=>{b&&(R?(t(b.id),g.info("Đã xóa khỏi mục yêu thích",{icon:()=>s.jsx("span",{style:{fontSize:"1.5rem",marginRight:"10px"},children:"💔"}),className:"toast-message"})):(i(b),g.success("Đã thêm vào mục yêu thích",{icon:()=>s.jsx("span",{style:{fontSize:"1.5rem",marginRight:"10px"},children:"❤️"}),className:"toast-message"})))},whileHover:{scale:1.1},whileTap:{scale:.9},animate:R?{scale:[1,1.2,1]}:{scale:1},transition:R?{duration:.3}:{duration:.1},children:s.jsx(h,{className:"w-5 h-5 "+(R?"fill-dexin-primary text-dexin-primary":"")})})]})]})]}),s.jsxs(m.div,{className:"mt-12",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.1,duration:.4},children:[s.jsx("div",{className:"border-b border-gray-200",children:s.jsxs("div",{className:"flex justify-center text-center",children:[s.jsxs("div",{className:"relative mx-4 w-1/3",children:[s.jsx(m.button,{className:"py-4 px-8 font-medium text-xl "+("description"===z?"text-dexin-primary":"text-gray-500 hover:text-gray-700"),onClick:()=>B("description"),initial:{opacity:0},animate:{opacity:1},transition:{delay:1.2,duration:.3},children:"Mô Tả Sản Phẩm"}),"description"===z&&s.jsx(m.div,{className:"absolute bottom-0 left-0 w-full h-0.5 bg-dexin-light",initial:{scaleX:0},animate:{scaleX:1},transition:{duration:.4}})]}),s.jsxs("div",{className:"relative mx-4 w-1/3",children:[s.jsx(m.button,{className:"py-4 px-8 font-medium text-xl "+("reviews"===z?"text-dexin-primary":"text-gray-500 hover:text-gray-700"),onClick:()=>B("reviews"),initial:{opacity:0},animate:{opacity:1},transition:{delay:1.25,duration:.3},children:"Đánh giá từ khách hàng"}),"reviews"===z&&s.jsx(m.div,{className:"absolute bottom-0 left-0 w-full h-0.5 bg-dexin-light",initial:{scaleX:0},animate:{scaleX:1},transition:{duration:.4}})]})]})}),s.jsxs("div",{className:"py-8",children:["description"===z&&s.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[s.jsx(m.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:1.2,duration:.4},className:"flex justify-center",children:s.jsx("img",{src:b.images?b.images[0]:b.image,alt:b.name,className:"w-4/5 h-auto rounded-lg"})}),s.jsx("div",{className:"flex flex-col justify-center",children:b.details&&b.details.map(((e,a)=>s.jsxs(m.div,{className:"mb-6",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:1.2+.1*a,duration:.3},children:[s.jsxs("h3",{className:"font-semibold text-gray-800 text-lg",children:[e.label,":"]}),s.jsx("p",{className:"text-gray-600 mt-2",children:e.value})]},a)))})]}),"reviews"===z&&s.jsxs("div",{className:"pt-4",children:[s.jsxs("div",{className:"flex justify-between items-center mb-8",children:[s.jsxs("h2",{className:"text-2xl font-bold",children:["Tất cả đánh giá ",s.jsxs("span",{className:"text-gray-500 ml-1 font-normal",children:["(",b.reviews,")"]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"relative",children:s.jsx("button",{className:"flex items-center justify-center h-10 w-10 rounded-full border border-gray-200 hover:bg-gray-50",children:s.jsx(m.div,{whileHover:{rotate:180},transition:{duration:.3},children:s.jsx(j,{className:"h-5 w-5 text-dexin-light"})})})}),s.jsxs("div",{className:"relative",children:[s.jsxs("select",{className:"appearance-none bg-gray-100 py-2 px-4 pr-10 rounded-full text-gray-700 font-medium focus:outline-none focus:ring-2 focus:ring-dexin-light focus:border-transparent",value:G,onChange:e=>{I(e.target.value)},children:[s.jsx("option",{value:"newest",children:"Gần Nhất"}),s.jsx("option",{value:"highest",children:"Đánh giá cao nhất"}),s.jsx("option",{value:"lowest",children:"Đánh giá thấp nhất"})]}),s.jsx("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700",children:s.jsx("svg",{className:"fill-current h-4 w-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:s.jsx("path",{d:"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"})})})]}),s.jsx(o,{variant:"primary",className:"rounded-full py-2 px-6",children:"Viết đánh giá"})]})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:F.map(((e,a)=>s.jsxs(m.div,{className:"border border-gray-100 rounded-lg p-6 bg-white shadow-sm hover:shadow-md transition-shadow duration-300",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*a,duration:.3},children:[s.jsxs("div",{className:"flex justify-between items-start mb-4",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("h3",{className:"text-lg font-bold",children:e.name}),e.verified&&s.jsx("span",{className:"text-dexin-primary",children:s.jsxs("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("path",{d:"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z",fill:"currentColor",fillOpacity:"0.2"}),s.jsx("path",{d:"M7.5 12.5L10.5 15.5L16.5 9.5",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})})]}),s.jsx("button",{className:"text-gray-400 hover:text-gray-600",children:s.jsx(f,{className:"h-5 w-5"})})]}),s.jsx("div",{className:"flex items-center mb-4",children:s.jsx("div",{className:"flex",children:[...Array(5)].map(((a,i)=>{const t=i+1,n=e.rating>=t,r=!n&&e.rating>t-1&&e.rating<t;return s.jsx("svg",{className:"w-5 h-5 "+(n||r?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",children:r?s.jsxs(s.Fragment,{children:[s.jsx("defs",{children:s.jsxs("linearGradient",{id:`halfStar-${i}`,children:[s.jsx("stop",{offset:"50%",stopColor:"currentColor"}),s.jsx("stop",{offset:"50%",stopColor:"#D1D5DB"})]})}),s.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z",fill:`url(#halfStar-${i})`})]}):s.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},i)}))})}),s.jsxs("p",{className:"text-gray-700 mb-4 min-h-[3rem]",children:['"',e.comment,'"']}),s.jsx("p",{className:"text-gray-500 text-sm",children:e.date})]},e.id)))})]})]})]})]})})]}):s.jsx("div",{className:"container mx-auto px-4 py-12 min-h-[60vh]",children:s.jsxs("div",{className:"text-center",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Không tìm thấy sản phẩm"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa."}),s.jsx(c,{to:"/store",children:s.jsx(o,{variant:"primary",children:"Quay lại cửa hàng"})})]})});var W};export{S as default};
