import React from 'react';
import { MessageCircle<PERSON><PERSON>, Brush, Heart  } from 'lucide-react';
const AISection = () => {
  return (
    <section className="bg-dexin-light-20 py-10 sm:py-16 md:py-20">
      <div className="container mx-auto px-4 sm:px-6">
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-8 sm:mb-14 md:mb-20 text-dexin-primary font-dexin">
          Các tính năng chính
        </h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 mb-4 sm:mb-6">
              <MessageCircleMore className="h-full w-full" />
            </div>
            <h3 className="text-lg sm:text-xl font-bold text-dexin-primary mb-2 font-dexin"><PERSON><PERSON><PERSON><br />c<PERSON>ng chuyên gia</h3>
            <p className="text-gray-600">Mở lòng và nhận những<br />đề xuất phù hợp nhất.</p>
          </div>
          
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 mb-4 sm:mb-6">
              <Brush className="h-full w-full" />
            </div>
            <h3 className="text-lg sm:text-xl font-bold text-dexin-primary mb-2 font-dexin">Thiết kế 2D<br />trực quan</h3>
            <p className="text-gray-600">Dễ dàng thử nghiệm phong cách<br />trước khi áp dụng thực tế.</p>
          </div>
          
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 mb-4 sm:mb-6">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-full w-full text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 className="text-lg sm:text-xl font-bold text-dexin-primary mb-2 font-dexin">Bản vẽ 3D<br />cá nhân hóa</h3>
            <p className="text-gray-600">Hình dung trước không gian<br />với bản thiết kế 3D</p>
          </div>
          
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 mb-4 sm:mb-6">
              <Heart className="h-full w-full" />
            </div>
            <h3 className="text-lg sm:text-xl font-bold text-dexin-primary mb-2 font-dexin">Cộng đồng<br />trải nghiệm decor</h3>
            <p className="text-gray-600">Cùng nhau chia sẻ ý tưởng<br />và truyền cảm hứng.</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AISection; 