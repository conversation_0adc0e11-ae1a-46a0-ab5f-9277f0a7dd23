import{c as e,j as a,m as t,L as s,r as i,z as n,B as r,u as l,G as c,A as d}from"./index-DdBL2cja.js";import{M as m}from"./minus-Fx_j7jOv.js";import{P as x}from"./plus-66Jg-RVc.js";import{T as o}from"./trash-2-1tdRbsHn.js";
/**
 * @license lucide-react v0.484.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=e("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),y=e=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(e),j=({item:e,updateQuantity:i,removeItem:n})=>a.jsxs(t.div,{className:"flex flex-col md:flex-row md:items-center py-4 border-b border-gray-200",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[a.jsx("div",{className:"md:w-1/6 flex justify-center mb-4 md:mb-0",children:a.jsx("div",{className:"h-24 w-24 bg-gray-50 rounded-md flex items-center justify-center",children:a.jsx("img",{src:e.image,alt:e.name,className:"max-h-20 max-w-20 object-contain"})})}),a.jsxs("div",{className:"md:w-2/6 md:px-4",children:[a.jsx(s,{to:`/product/${e.id}`,className:"text-gray-800 font-medium hover:text-dexin-primary",children:e.name}),a.jsxs("div",{className:"text-gray-500 text-sm mt-1",children:[e.variantName&&a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("span",{children:"Màu sắc:"}),a.jsxs("div",{className:"flex items-center gap-1",children:[a.jsx("div",{className:"w-4 h-4 rounded-full border border-gray-300",style:{backgroundColor:e.variantColor}}),a.jsx("span",{children:e.variantName})]})]}),e.size&&a.jsxs("div",{children:["Kích thước: ",e.size]})]})]}),a.jsx("div",{className:"md:w-1/6 text-dexin-primary font-bold mt-2 md:mt-0 md:text-center",children:y(e.price)}),a.jsx("div",{className:"md:w-1/6 flex items-center justify-between md:justify-center mt-2 md:mt-0",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("button",{onClick:()=>{e.quantity>1?i(e.cartKey,e.quantity-1):n(e.cartKey)},className:"h-8 w-8 flex items-center justify-center bg-gray-100 rounded-l-md hover:bg-gray-200",children:a.jsx(m,{className:"w-4 h-4 text-gray-600"})}),a.jsx("div",{className:"h-8 w-10 flex items-center justify-center bg-white border border-gray-200",children:e.quantity}),a.jsx("button",{onClick:()=>{i(e.cartKey,e.quantity+1)},className:"h-8 w-8 flex items-center justify-center bg-gray-100 rounded-r-md hover:bg-gray-200",children:a.jsx(x,{className:"w-4 h-4 text-gray-600"})})]})}),a.jsx("div",{className:"md:w-1/6 font-bold text-right mt-2 md:mt-0",children:y(e.price*e.quantity)}),a.jsx("div",{className:"flex justify-end mt-2 md:mt-0 md:ml-4",children:a.jsx("button",{onClick:()=>n(e.cartKey),className:"text-gray-400 hover:text-red-500 transition-colors duration-200","aria-label":"Xóa sản phẩm",children:a.jsx(o,{className:"h-5 w-5"})})})]}),g=e=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(e),p=({cartTotal:e})=>{const[t,s]=i.useState(""),[l,c]=i.useState(0),d=n();return a.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6 sticky top-24",children:[a.jsx("h2",{className:"text-xl font-bold mb-4",children:"Thanh toán"}),a.jsxs("div",{className:"mb-4",children:[a.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[a.jsx("span",{className:"text-gray-600",children:"Tạm tính"}),a.jsx("span",{className:"font-medium",children:g(e)})]}),l>0&&a.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100 text-green-600",children:[a.jsx("span",{children:"Giảm giá"}),a.jsxs("span",{children:["- ",g(l)]})]}),a.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[a.jsx("span",{className:"text-gray-600",children:"Vận chuyển"}),a.jsx("span",{className:"font-medium",children:"0 ₫"})]}),a.jsxs("div",{className:"flex justify-between py-2 font-bold text-lg mt-2",children:[a.jsx("span",{children:"Tổng tiền"}),a.jsx("span",{className:"text-dexin-primary",children:g(e-l)})]})]}),a.jsxs("div",{className:"mb-6",children:[a.jsxs("div",{className:"flex mb-2 items-center",children:[a.jsx(h,{className:"h-4 w-4 text-dexin-primary mr-2"}),a.jsx("span",{className:"text-sm font-medium",children:"Mã giảm giá"})]}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{type:"text",value:t,onChange:e=>s(e.target.value),placeholder:"Nhập mã giảm giá",className:"flex-1 border border-gray-300 rounded-l-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-dexin-light"}),a.jsx("button",{onClick:()=>{"DEXIN10"===t.toUpperCase()?c(.1*e):(c(0),alert("Mã giảm giá không hợp lệ!"))},className:"bg-dexin-light text-white px-4 py-2 rounded-r-lg hover:bg-pink-600 transition-colors",children:"Áp dụng"})]}),t&&"DEXIN10"===t.toUpperCase()&&l>0&&a.jsx("div",{className:"text-green-600 text-sm mt-2",children:"Đã áp dụng mã giảm giá DEXIN10 (giảm 10%)"})]}),a.jsx(r,{variant:"primary",size:"lg",className:"w-full mb-4",onClick:()=>{d("/cart/checkout")},children:"Tiến hành thanh toán"}),a.jsxs("div",{className:"text-xs text-gray-500 leading-relaxed",children:[a.jsx("p",{className:"mb-2",children:"Bằng cách đặt hàng, bạn đồng ý với Điều khoản sử dụng của DEXIN"}),a.jsx("p",{children:"Khách hàng có thể hủy đơn hàng trong vòng 24 giờ sau khi đặt"})]})]})},u=()=>a.jsxs(t.div,{className:"text-center py-16",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[a.jsx(t.div,{className:"flex justify-center mb-4",initial:{scale:.8},animate:{scale:1},transition:{duration:.5,delay:.2},children:a.jsx("svg",{className:"w-20 h-20 text-gray-300",fill:"currentColor",viewBox:"0 0 20 20",children:a.jsx("path",{d:"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"})})}),a.jsx(t.h2,{className:"text-2xl font-bold text-gray-800 mb-4",initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,delay:.3},children:"Giỏ hàng của bạn đang trống"}),a.jsx(t.p,{className:"text-gray-600 mb-8",initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,delay:.4},children:"Hãy thêm sản phẩm vào giỏ hàng để tiếp tục mua sắm"}),a.jsx(t.div,{initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,delay:.5},children:a.jsx(s,{to:"/store",children:a.jsx(r,{variant:"primary",size:"lg",children:"Tiếp tục mua sắm"})})})]}),N=()=>{const{cartItems:e,cartTotal:i,updateQuantity:n,removeFromCart:r}=l();return a.jsx("div",{className:"bg-gray-50 min-h-screen py-8",children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsxs("div",{className:"mb-8",children:[a.jsx(t.h1,{className:"text-3xl font-bold text-center text-dexin-primary mb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:"Giỏ Hàng"}),a.jsx(t.nav,{className:"flex justify-center mb-8",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:a.jsx("ol",{className:"flex items-center space-x-2 md:space-x-4",children:[{id:1,name:"Giỏ hàng của bạn",isCurrent:!0},{id:2,name:"Thông tin thanh toán",isCurrent:!1},{id:3,name:"Hoàn tất đơn hàng",isCurrent:!1}].map(((e,t)=>a.jsxs("li",{className:"flex items-center",children:[t>0&&a.jsx(c,{className:"h-4 w-4 text-gray-400 mx-2"}),a.jsxs("div",{className:"flex items-center "+(e.isCurrent?"text-dexin-primary font-medium":"text-gray-500"),children:[a.jsx("span",{className:"h-6 w-6 flex items-center justify-center rounded-full mr-2 "+(e.isCurrent?"bg-dexin-light text-white":"bg-gray-200 text-gray-600"),children:e.id}),a.jsx("span",{children:e.name})]})]},e.id)))})})]}),0===e.length?a.jsx(u,{}):a.jsxs("div",{className:"flex flex-col lg:flex-row gap-8",children:[a.jsx(t.div,{className:"lg:w-2/3",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.3},children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[a.jsxs("div",{className:"hidden md:flex md:items-center text-gray-600 font-medium pb-4 border-b border-gray-200",children:[a.jsx("div",{className:"md:w-1/6 text-center",children:"Sản phẩm"}),a.jsx("div",{className:"md:w-2/6 md:px-4",children:"Thông tin"}),a.jsx("div",{className:"md:w-1/6 text-center",children:"Đơn giá"}),a.jsx("div",{className:"md:w-1/6 text-center",children:"Số lượng"}),a.jsx("div",{className:"md:w-1/6 text-right",children:"Tổng tiền"}),a.jsx("div",{className:"w-8"})]}),a.jsx(d,{children:e.map(((e,t)=>a.jsx(j,{item:e,updateQuantity:n,removeItem:r},e.cartKey||e.id)))}),a.jsx("div",{className:"mt-6",children:a.jsxs(s,{to:"/store",className:"text-dexin-primary hover:underline flex items-center",children:[a.jsx(c,{className:"h-4 w-4 mr-1 rotate-180"}),a.jsx("span",{children:"Tiếp tục mua sắm"})]})})]})}),a.jsx(t.div,{className:"lg:w-1/3",initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.4},children:a.jsx(p,{cartTotal:i})})]})]})})};export{N as default};
