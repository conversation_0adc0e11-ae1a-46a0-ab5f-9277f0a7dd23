import{u as e,r as s,y as t,j as a,m as i,H as n,L as r,t as l,B as o,v as c}from"./index-DdBL2cja.js";import{P as m,S as d}from"./ProductVariantModal-Btj1bdW7.js";import{g as h}from"./products-C2yRfJkG.js";import{T as x}from"./trash-2-1tdRbsHn.js";import"./minus-Fx_j7jOv.js";import"./plus-66Jg-RVc.js";const u=s.memo((({rating:e,reviews:s})=>{const t=Math.floor(e),i=e%1>=.5;return a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex items-center mr-1",children:[...Array(5)].map(((e,s)=>a.jsx("span",{className:"text-dexin-gold",children:s<t||s===t&&i?"★":"☆"},s)))}),a.jsxs("span",{className:"text-xs text-gray-500",children:["(",s,")"]})]})}));s.memo((()=>a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:a.jsx("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})})));const g=s.memo((()=>a.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[a.jsx("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),a.jsx("circle",{cx:"12",cy:"12",r:"3"})]}))),p=s.memo((({product:i,onOpenVariantModal:n})=>{const{removeFromWishlist:o}=l(),{addToCart:c}=e(),m=s.useMemo((()=>{return e=i.price,new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND",minimumFractionDigits:0,maximumFractionDigits:0}).format(e);var e}),[i.price]),[p,j]=s.useState(!1),[y,v]=s.useState(!1),b=s.useCallback((()=>{j(!0)}),[]),f=s.useCallback((()=>{v(!0)}),[]),w=s.useCallback((()=>{v(!1)}),[]),N=s.useCallback((e=>{e.stopPropagation(),e.preventDefault(),o(i.id),t.info(`Đã xóa "${i.name}" khỏi mục yêu thích`,{icon:()=>a.jsx("span",{style:{fontSize:"1.2rem",marginRight:"10px"},children:"💔"}),className:"toast-message"})}),[i.id,i.name,o]),C=s.useCallback((e=>{e.stopPropagation(),e.preventDefault(),t.info("Chức năng xem nhanh đang được phát triển",{icon:()=>a.jsx("span",{style:{fontSize:"1.5rem",marginRight:"10px"},children:"👁️"}),className:"toast-message"})}),[]),k=s.useCallback(((e,s)=>{e.stopPropagation(),e.preventDefault();const i=h(s.id);i&&i.variants&&i.variants.length>0?n(i):(c(s),t.success(`Đã thêm "${s.name}" vào giỏ hàng`,{icon:()=>a.jsx("span",{style:{fontSize:"1.2rem",marginRight:"10px"},children:"🛒"}),className:"toast-message",toastId:`wishlist-cart-${s.id}`,autoClose:2e3}))}),[c,n]);return a.jsx(r,{to:`/product/${i.id}`,className:"block",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden group relative transition-all duration-300 hover:shadow-md",onMouseEnter:f,onMouseLeave:w,children:[a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"overflow-hidden h-64 flex items-center justify-center bg-gray-50",children:a.jsx("img",{src:i.image,alt:i.name,className:`w-auto h-auto max-h-60 max-w-[90%] object-contain content-visibility-auto transition-all duration-300 ease-in-out ${p?"opacity-100":"opacity-0"} ${y?"scale-105":"scale-100"}`,loading:"lazy",decoding:"async",onLoad:b})}),p&&a.jsx("button",{className:"absolute top-2 right-2 p-2 bg-white rounded-full hover:bg-gray-100 transition-all duration-200 opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100","aria-label":"Quick view",onClick:C,children:a.jsx(g,{})}),a.jsx("div",{className:"w-full bg-dexin-light text-white py-3 text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300",onClick:e=>k(e,i),children:a.jsxs("button",{className:"flex items-center justify-center w-full",children:[a.jsx(d,{className:"mr-2 h-5 w-5"}),a.jsx("span",{children:"Thêm vào giỏ hàng"})]})})]}),a.jsxs("div",{className:"p-4",children:[a.jsx("h3",{className:"text-base font-medium text-gray-800 line-clamp-1",children:i.name}),a.jsx("div",{className:"mt-2 text-base font-bold text-dexin-primary",children:m}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx(u,{rating:i.rating,reviews:i.reviews}),a.jsx("button",{className:"p-1.5 text-dexin-primary hover:text-gray-400 transition-colors duration-200","aria-label":"Remove from wishlist",onClick:N,children:a.jsx(x,{size:18})})]})]})]})})})),j=s.memo((()=>a.jsxs("div",{className:"text-center py-12",children:[a.jsx("div",{className:"inline-flex justify-center items-center w-24 h-24 bg-pink-100 rounded-full mb-6",children:a.jsx(n,{className:"h-12 w-12 text-dexin-primary"})}),a.jsx("h3",{className:"text-xl font-medium text-gray-800 mb-2",children:"Danh sách yêu thích trống"}),a.jsx("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Bạn chưa thêm sản phẩm nào vào danh sách yêu thích. Hãy khám phá cửa hàng và thêm các sản phẩm bạn yêu thích."}),a.jsx(r,{to:"/store",children:a.jsx("button",{className:"bg-dexin-primary text-white px-6 py-2 rounded-full hover:bg-dexin-primary/90 transition-colors",onClick:()=>{t.success("Chuyển đến trang cửa hàng",{icon:()=>a.jsx("span",{style:{fontSize:"1.5rem",marginRight:"10px"},children:"🛍️"}),className:"toast-message"})},children:"Khám phá cửa hàng"})})]}))),y=s.memo((({currentPage:e,setCurrentPage:i,totalPages:n})=>{const r=s.useCallback((s=>{i(s),window.scrollTo({top:0,behavior:"smooth"}),s!==e&&t.info(`Đã chuyển đến trang ${s}`,{icon:()=>a.jsx("span",{style:{fontSize:"1.5rem",marginRight:"10px"},children:"📄"}),className:"toast-message",autoClose:2e3})}),[i,e]);return n<=1?null:a.jsx("div",{className:"flex justify-center mt-8 space-x-2",children:[...Array(n)].map(((s,t)=>a.jsx("button",{onClick:()=>r(t+1),className:"px-3 py-1 rounded-md transition-colors duration-150 "+(e===t+1?"bg-dexin-primary text-white border border-dexin-primary":"bg-white border border-gray-300 text-gray-600 hover:bg-dexin-light hover:text-white hover:border-dexin-light"),children:t+1},t)))})})),v=({wishlistItems:n,currentPage:r,setCurrentPage:l,totalPages:o})=>{const{addToCart:c}=e(),[d,h]=s.useState(!1),[x,u]=s.useState(null),g=s.useCallback((e=>{u(e),h(!0)}),[]),v=s.useCallback((()=>{h(!1),setTimeout((()=>{u(null)}),300)}),[]),b=s.useCallback((e=>{c(e,e.quantity);const s=e.variantName?`${e.name} (${e.variantName})`:e.name;t.success(`Đã thêm "${s}" vào giỏ hàng`,{icon:()=>a.jsx("span",{style:{fontSize:"1.2rem",marginRight:"10px"},children:"🛒"}),className:"toast-message",toastId:`wishlist-variant-cart-${e.id}-${e.variantId}`,autoClose:2e3})}),[c]);return 0===n.length?a.jsx(j,{}):a.jsxs("div",{children:[a.jsx("h2",{className:"text-xl font-semibold whitespace-nowrap mb-6",children:"Sản phẩm yêu thích của bạn ❤️"}),a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:n.map(((e,s)=>a.jsx(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*s},children:a.jsx(p,{product:e,onOpenVariantModal:g})},e.id)))}),a.jsx(y,{currentPage:r,setCurrentPage:l,totalPages:o}),a.jsx(m,{isOpen:d,onClose:v,product:x,onAddToCart:b})]})},b=()=>{const{wishlistItems:e,clearWishlist:n}=l(),[m,d]=s.useState(1),[h]=s.useState(6);s.useEffect((()=>{d(1)}),[e.length]);const x=s.useMemo((()=>{const s=m*h,t=s-h;return e.slice(t,s)}),[e,m,h]),u=s.useMemo((()=>Math.ceil(e.length/h)),[e.length,h]);return a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-dexin-bg py-8 md:py-12",children:a.jsx("div",{className:"container mx-auto px-4 sm:px-6",children:a.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[a.jsxs(i.div,{className:"mb-6 md:mb-0 md:w-1/2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[a.jsxs("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-4",children:["Danh sách yêu thích ",a.jsxs("span",{className:"text-dexin-primary",children:["(",e.length,")"]})]}),a.jsx("p",{className:"text-gray-600 mb-6 max-w-lg",children:"Những sản phẩm bạn đã đánh dấu yêu thích sẽ được hiển thị ở đây. Dễ dàng theo dõi và mua sắm khi bạn sẵn sàng."}),a.jsxs("div",{className:"flex space-x-4",children:[a.jsx(r,{to:"/store",children:a.jsxs(o,{variant:"outline",className:"flex items-center",children:[a.jsx(c,{className:"h-5 w-5 mr-2"}),a.jsx("span",{children:"Tiếp tục mua sắm"})]})}),e.length>0&&a.jsx(o,{variant:"ghost",className:"text-gray-500 hover:text-dexin-primary",onClick:()=>{n(),t.info("Đã xóa tất cả sản phẩm",{icon:()=>a.jsx("span",{style:{fontSize:"1rem",marginRight:"10px"},children:"🗑️"}),className:"toast-message"})},children:"Xóa tất cả"})]})]}),a.jsx(i.div,{className:"md:w-1/2 flex justify-center md:justify-end",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.2},children:a.jsx("img",{src:"/images/wishlist-hero.png",alt:"Wishlist",className:"w-auto h-auto max-w-full max-h-[300px] md:max-h-full object-contain",onError:e=>{e.target.src="/images/jogging.png"}})})]})})}),a.jsx("div",{className:"container mx-auto px-4 sm:px-6 py-8 md:py-12",children:a.jsx(v,{wishlistItems:x,currentPage:m,setCurrentPage:d,totalPages:u})})]})};export{b as default};
