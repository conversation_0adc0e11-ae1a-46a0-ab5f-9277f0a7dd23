import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { motion } from 'motion/react';

const ProtectedRoute = ({ children, requiredRole = null, fallbackPath = '/login' }) => {
  const { user, loading, isAuthenticated, hasRole } = useAuth();

  // Hiển thị loading khi đang kiểm tra authentication
  if (loading) {
    return (
      <div className="h-screen w-full bg-dexin-bg flex items-center justify-center">
        <motion.div
          className="flex flex-col items-center space-y-4"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="w-12 h-12 border-4 border-dexin-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="text-dexin-primary font-medium"><PERSON>ang kiểm tra quyền truy cập...</p>
        </motion.div>
      </div>
    );
  }

  // Nếu chưa đăng nhập, chuyển hướng đến trang login
  if (!isAuthenticated) {
    return <Navigate to={fallbackPath} replace />;
  }

  // Nếu có yêu cầu role cụ thể và user không có role đó
  if (requiredRole && !hasRole(requiredRole)) {
    console.log('🚫 Access denied. Required role:', requiredRole, 'User role:', user?.role);

    // Nếu là staff cố truy cập trang user, chuyển về staff dashboard
    if (user?.role?.toLowerCase() === 'staff') {
      console.log('➡️ Staff user redirected to staff dashboard');
      return <Navigate to="/staff" replace />;
    }
    // Nếu là user cố truy cập trang staff, chuyển về trang chủ
    console.log('➡️ Regular user redirected to home page');
    return <Navigate to="/" replace />;
  }

  // Nếu tất cả điều kiện đều thỏa mãn, render children
  return children;
};

export default ProtectedRoute;
