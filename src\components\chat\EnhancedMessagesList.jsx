import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { MoreVertical, Download, Save, Eye, File, X, ChevronLeft, ChevronRight, Loader, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import { toast } from 'react-toastify';

const MessageDropdown = ({ isOpen, onClose, messageType, onDownload, onSave, onViewDetails }) => (
  <AnimatePresence>
    {isOpen && (
      <motion.div
        className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[150px]"
        initial={{ opacity: 0, scale: 0.8, y: -10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: -10 }}
        transition={{ duration: 0.2 }}
      >
        <div className="py-1">
          {messageType === 'files' && (
            <button
              onClick={onDownload}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Tải xuống
            </button>
          )}
          {messageType === 'images' && (
            <button
              onClick={onSave}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
            >
              <Save className="h-4 w-4 mr-2" />
              Lưu ảnh
            </button>
          )}
          <button
            onClick={onViewDetails}
            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
          >
            <Eye className="h-4 w-4 mr-2" />
            Xem chi tiết
          </button>
        </div>
      </motion.div>
    )}
  </AnimatePresence>
);

const FullSizeImageViewer = ({ images, isOpen, onClose, initialIndex = 0 }) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isLoading, setIsLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const imageRef = useRef(null);
  const imageContainerRef = useRef(null);

  // Reset states when modal opens
  useEffect(() => {
    if (isOpen) {
      setCurrentIndex(initialIndex);
      setIsLoading(true);
      setImageError(false);
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else {
      // Restore body scroll
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, initialIndex]);

  // Wheel event handler for zoom
  useEffect(() => {
    const handleWheelEvent = (e) => {
      if (!isOpen || !imageContainerRef.current) return;

      // Prevent default scroll behavior
      e.preventDefault();

      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      setScale(prevScale => Math.max(0.5, Math.min(3, prevScale + delta)));
    };

    if (isOpen && imageContainerRef.current) {
      const container = imageContainerRef.current;
      // Add wheel event listener with passive: false to allow preventDefault
      container.addEventListener('wheel', handleWheelEvent, { passive: false });

      return () => {
        container.removeEventListener('wheel', handleWheelEvent);
      };
    }
  }, [isOpen]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          handlePrevious();
          break;
        case 'ArrowRight':
          handleNext();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentIndex]);

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setIsLoading(true);
      setImageError(false);
      setScale(1);
      setPosition({ x: 0, y: 0 });
    }
  };

  const handleNext = () => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setIsLoading(true);
      setImageError(false);
      setScale(1);
      setPosition({ x: 0, y: 0 });
    }
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setImageError(true);
  };

  // Handle double click to reset zoom
  const handleDoubleClick = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  if (!isOpen || !images || images.length === 0) return null;

  const currentImage = images[currentIndex];

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-[60]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        {/* Close button */}
        <motion.button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <X className="h-6 w-6" />
        </motion.button>

        {/* Zoom controls */}
        <div className="absolute top-4 left-4 z-10 flex flex-col space-y-2">
          <motion.button
            onClick={() => setScale(prevScale => Math.min(3, prevScale + 0.2))}
            className="p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ZoomIn className="h-5 w-5" />
          </motion.button>
          <motion.button
            onClick={() => setScale(prevScale => Math.max(0.5, prevScale - 0.2))}
            className="p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ZoomOut className="h-5 w-5" />
          </motion.button>
          <motion.button
            onClick={handleDoubleClick}
            className="p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <RotateCcw className="h-5 w-5" />
          </motion.button>
        </div>

        {/* Navigation buttons */}
        {images.length > 1 && (
          <>
            <motion.button
              onClick={(e) => {
                e.stopPropagation();
                handlePrevious();
              }}
              disabled={currentIndex === 0}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
              whileHover={{ scale: currentIndex === 0 ? 1 : 1.1 }}
              whileTap={{ scale: currentIndex === 0 ? 1 : 0.9 }}
            >
              <ChevronLeft className="h-6 w-6" />
            </motion.button>

            <motion.button
              onClick={(e) => {
                e.stopPropagation();
                handleNext();
              }}
              disabled={currentIndex === images.length - 1}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
              whileHover={{ scale: currentIndex === images.length - 1 ? 1 : 1.1 }}
              whileTap={{ scale: currentIndex === images.length - 1 ? 1 : 0.9 }}
            >
              <ChevronRight className="h-6 w-6" />
            </motion.button>
          </>
        )}

        {/* Image container */}
        <motion.div
          className="relative max-w-[95vw] max-h-[95vh] flex flex-col items-center"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Loading indicator */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Loader className="h-8 w-8 text-white" />
              </motion.div>
            </div>
          )}

          {/* Error state */}
          {imageError && (
            <div className="flex flex-col items-center justify-center p-8 text-white">
              <X className="h-12 w-12 mb-4 text-red-400" />
              <p className="text-lg">Không thể tải ảnh</p>
              <p className="text-sm text-gray-300 mt-2">{currentImage.name}</p>
            </div>
          )}

          {/* Image */}
          {!imageError && (
            <div
              ref={imageContainerRef}
              className="overflow-hidden cursor-grab active:cursor-grabbing"
              onDoubleClick={handleDoubleClick}
            >
              <img
                ref={imageRef}
                src={currentImage.preview}
                alt={currentImage.name}
                className="max-w-full max-h-[80vh] object-contain transition-transform duration-200"
                onLoad={handleImageLoad}
                onError={handleImageError}
                style={{
                  display: isLoading ? 'none' : 'block',
                  transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)`
                }}
                draggable={false}
              />
            </div>
          )}

          {/* Image info */}
          <motion.div
            className="mt-4 text-center text-white"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <p className="text-lg font-medium">{currentImage.name}</p>
            <div className="flex items-center justify-center space-x-4 mt-2">
              {images.length > 1 && (
                <p className="text-sm text-gray-300">
                  {currentIndex + 1} / {images.length}
                </p>
              )}
              <p className="text-sm text-gray-300">
                {Math.round(scale * 100)}%
              </p>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

const ImagePreview = ({ images, isOpen, onClose, onImageClick }) => (
  <AnimatePresence>
    {isOpen && (
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white rounded-lg p-4 max-w-4xl max-h-[90vh] overflow-auto"
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0.8 }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Xem ảnh ({images.length})</h3>
            <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
              <X className="h-5 w-5" />
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {images.map((image, index) => (
              <div key={index} className="space-y-2">
                <img
                  src={image.preview}
                  alt={image.name}
                  className="w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => onImageClick && onImageClick(index)}
                />
                <p className="text-sm text-gray-600 truncate">{image.name}</p>
              </div>
            ))}
          </div>
        </motion.div>
      </motion.div>
    )}
  </AnimatePresence>
);

const MessageItem = ({ message, index }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [fullSizeViewerOpen, setFullSizeViewerOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const dropdownRef = useRef(null);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    };

    if (dropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownOpen]);

  const handleDownload = () => {
    if (message.files) {
      try {
        message.files.forEach((file) => {
          const url = URL.createObjectURL(file);
          const a = document.createElement('a');
          a.href = url;
          a.download = file.name;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        });
        toast.success(`Đã tải xuống ${message.files.length} file!`);
      } catch (error) {
        console.error('Error downloading files:', error);
        toast.error('Có lỗi xảy ra khi tải xuống file!');
      }
    }
    setDropdownOpen(false);
  };

  const handleSaveImages = () => {
    if (message.images) {
      try {
        message.images.forEach((image) => {
          const a = document.createElement('a');
          a.href = image.preview;
          a.download = image.name;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        });
        toast.success(`Đã lưu ${message.images.length} ảnh!`);
      } catch (error) {
        console.error('Error saving images:', error);
        toast.error('Có lỗi xảy ra khi lưu ảnh!');
      }
    }
    setDropdownOpen(false);
  };

  const handleViewDetails = () => {
    if (message.type === 'images') {
      setPreviewOpen(true);
    } else if (message.type === 'files') {
      // Show file details in toast
      const fileDetails = message.files.map(file =>
        `${file.name} (${formatFileSize(file.size)})`
      ).join('\n');
      toast.info(`Chi tiết file:\n${fileDetails}`, {
        style: { whiteSpace: 'pre-line' }
      });
    } else {
      toast.info('Xem chi tiết tin nhắn');
    }
    setDropdownOpen(false);
  };

  // Handle image click from preview modal
  const handleImageClick = (imageIndex) => {
    setSelectedImageIndex(imageIndex);
    setPreviewOpen(false);
    setFullSizeViewerOpen(true);
  };

  // Handle direct image click from message bubble
  const handleDirectImageClick = (imageIndex = 0) => {
    setSelectedImageIndex(imageIndex);
    setFullSizeViewerOpen(true);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderImageContent = () => {
    if (!message.images || message.images.length === 0) return null;

    if (message.images.length === 1) {
      // Single image - larger display
      return (
        <div className="mt-2">
          <img
            src={message.images[0].preview}
            alt={message.images[0].name}
            className="max-w-[300px] max-h-[200px] rounded-lg object-cover cursor-pointer hover:opacity-90 transition-opacity"
            onClick={() => handleDirectImageClick(0)}
          />
        </div>
      );
    } else {
      // Multiple images - responsive grid layout
      const getGridLayout = (count) => {
        if (count === 2) return 'grid-cols-2';
        if (count === 3) return 'grid-cols-3';
        if (count >= 4) return 'grid-cols-2';
        return 'grid-cols-1';
      };

      return (
        <div className={`mt-2 grid ${getGridLayout(message.images.length)} gap-2 max-w-[300px] sm:max-w-[350px]`}>
          {message.images.slice(0, 4).map((image, idx) => (
            <div key={idx} className="relative">
              <img
                src={image.preview}
                alt={image.name}
                className="w-full h-16 sm:h-20 object-cover rounded cursor-pointer hover:opacity-90 transition-opacity"
                onClick={() => handleDirectImageClick(idx)}
              />
              {idx === 3 && message.images.length > 4 && (
                <div
                  className="absolute inset-0 bg-black bg-opacity-50 rounded flex items-center justify-center text-white text-xs sm:text-sm font-medium cursor-pointer"
                  onClick={() => setPreviewOpen(true)}
                >
                  +{message.images.length - 4}
                </div>
              )}
            </div>
          ))}
        </div>
      );
    }
  };

  const renderFileContent = () => {
    if (!message.files || message.files.length === 0) return null;

    return (
      <div className="mt-2 space-y-2 max-w-[300px] sm:max-w-[350px]">
        {message.files.map((file, idx) => (
          <motion.div
            key={idx}
            className="flex items-center space-x-3 p-2 sm:p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <File className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-xs sm:text-sm font-medium text-gray-700 truncate">{file.name}</p>
              <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
            </div>
          </motion.div>
        ))}
      </div>
    );
  };

  const hasAttachments = message.type === 'files' || message.type === 'images';

  return (
    <>
      <motion.div
        className={`mb-4 sm:mb-6 flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.3,
          delay: index * 0.1,
          ease: "easeOut"
        }}
      >
        <div className="flex items-start space-x-2 max-w-[75%] sm:max-w-[70%]">
          {message.sender === 'user' && hasAttachments && (
            <div className="relative" ref={dropdownRef}>
              <motion.button
                onClick={() => setDropdownOpen(!dropdownOpen)}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <MoreVertical className="h-4 w-4" />
              </motion.button>
              <MessageDropdown
                isOpen={dropdownOpen}
                onClose={() => setDropdownOpen(false)}
                messageType={message.type}
                onDownload={handleDownload}
                onSave={handleSaveImages}
                onViewDetails={handleViewDetails}
              />
            </div>
          )}

          <div className="flex flex-col">
            {message.sender === 'staff' && (
              <motion.div
                className="flex items-center mb-1"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <p className="text-sm text-gray-800 mr-auto">Tư vấn viên Thiên Ngân</p>
                <span className="text-xs text-gray-500 ml-2">{message.time}</span>
              </motion.div>
            )}

            <motion.div
              className={`px-3 sm:px-4 py-2 sm:py-3 shadow-sm break-words ${
                message.sender === 'user'
                  ? 'bg-dexin-light-20 text-dark rounded-tl-xl sm:rounded-tl-2xl rounded-tr-xl sm:rounded-tr-2xl rounded-bl-xl sm:rounded-bl-2xl'
                  : 'bg-white border-2 border-dexin-light-50 rounded-tr-xl sm:rounded-tr-2xl rounded-tl-xl sm:rounded-tl-2xl rounded-br-xl sm:rounded-br-2xl text-gray-800'
              }`}
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              {/* Text content */}
              {message.text && (
                <p className="text-sm sm:text-sm leading-relaxed whitespace-pre-wrap">
                  {typeof message.text === 'string' ? message.text : 'Tin nhắn không hợp lệ'}
                </p>
              )}

              {/* Image content */}
              {renderImageContent()}

              {/* File content */}
              {renderFileContent()}

              {message.sender === 'user' && (
                <p className="text-xs sm:text-xs text-gray-500 text-right mt-1">
                  {message.time}
                </p>
              )}
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Image Preview Modal */}
      {message.images && (
        <ImagePreview
          images={message.images}
          isOpen={previewOpen}
          onClose={() => setPreviewOpen(false)}
          onImageClick={handleImageClick}
        />
      )}

      {/* Full Size Image Viewer */}
      {message.images && (
        <FullSizeImageViewer
          images={message.images}
          isOpen={fullSizeViewerOpen}
          onClose={() => setFullSizeViewerOpen(false)}
          initialIndex={selectedImageIndex}
        />
      )}
    </>
  );
};

const EnhancedMessagesList = React.memo(({ messages, shouldScrollToBottom = false }) => {
  const messagesContainerRef = useRef(null);

  // Auto-scroll to bottom when new message is added
  useEffect(() => {
    if (shouldScrollToBottom && messagesContainerRef.current) {
      // Longer delay for messages with images/files to ensure they're fully loaded
      const lastMessage = messages[messages.length - 1];
      const delay = lastMessage && (lastMessage.type === 'images' || lastMessage.type === 'files') ? 200 : 50;

      const timer = setTimeout(() => {
        if (messagesContainerRef.current) {
          // Scroll to bottom with smooth behavior
          messagesContainerRef.current.scrollTo({
            top: messagesContainerRef.current.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [messages.length, shouldScrollToBottom, messages]);

  return (
    <div
      ref={messagesContainerRef}
      className="flex-grow overflow-y-auto p-6 bg-white"
    >
      {messages.map((message, index) => (
        <MessageItem key={message.id} message={message} index={index} />
      ))}
    </div>
  );
});

export default EnhancedMessagesList;
