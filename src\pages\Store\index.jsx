import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Link } from 'react-router-dom';
import StoreSidebar from './components/StoreSidebar';
import StoreContent from './components/StoreContent';
import { PRICE_RANGE, SORT_OPTIONS } from '../../constants';
import { getAllProducts } from '../../data/products';
import SEOHead from '../../components/common/SEOHead';
import { pagesSEO } from '../../utils/seoUtils';

// Danh sách danh mục
const categories = [
  { id: 'all', name: 'Tất cả' },
  { id: 'ban', name: '<PERSON><PERSON><PERSON>' },
  { id: 'ghe', name: '<PERSON><PERSON><PERSON>' },
  { id: 'giuong', name: 'Gi<PERSON>ờng' },
  { id: 'tu', name: '<PERSON><PERSON>' },
  { id: 'ke', name: '<PERSON><PERSON>' },
  { id: 'den', name: 'Đ<PERSON>n' },
];

// Hàm kiểm tra hiệu năng thiết bị để điều chỉnh animations
const usePerformanceMode = () => {
  const [isLowPerformance, setIsLowPerformance] = useState(false);

  useEffect(() => {
    // Kiểm tra số lượng CPU cores (ít cores thường đồng nghĩa với thiết bị yếu hơn)
    const checkPerformance = () => {
      // Kiểm tra nếu là thiết bị di động
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // Kiểm tra số lượng CPU cores nếu có thể
      const cpuCores = navigator.hardwareConcurrency || 4;

      // Thiết bị được coi là hiệu năng thấp nếu là mobile hoặc có ít hơn 4 cores
      const lowPerformance = isMobile || cpuCores < 4;

      setIsLowPerformance(lowPerformance);
    };

    checkPerformance();

    // Thêm event listener để kiểm tra lại khi cửa sổ thay đổi kích thước
    // (có thể là do xoay thiết bị hoặc thay đổi cửa sổ)
    window.addEventListener('resize', checkPerformance);

    return () => {
      window.removeEventListener('resize', checkPerformance);
    };
  }, []);

  return isLowPerformance;
};

// Component trang Store
const Store = () => {
  // Kiểm tra hiệu năng thiết bị
  const isLowPerformance = usePerformanceMode();

  // Sử dụng useMemo để tránh tạo lại mảng sản phẩm mỗi lần render
  const products = useMemo(() => getAllProducts(), []);

  const [filteredProducts, setFilteredProducts] = useState(products);

  // State chính thức được áp dụng cho việc lọc sản phẩm
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [priceRange, setPriceRange] = useState([PRICE_RANGE.MIN, PRICE_RANGE.MAX]); // Sử dụng giá trị từ constants
  const [sortOption, setSortOption] = useState(SORT_OPTIONS.NEWEST); // Tùy chọn sắp xếp mặc định

  const [showMobileSidebar, setShowMobileSidebar] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage] = useState(9); // Số sản phẩm hiển thị trên mỗi trang - cập nhật thành 9

  // Lọc sản phẩm khi thay đổi bộ lọc chính thức
  useEffect(() => {
    // Reset trang về 1 khi thay đổi bộ lọc
    setCurrentPage(1);

    // Tính toán sản phẩm đã lọc
    const filterProducts = () => {
      let result = products;

      // Lọc theo danh mục
      if (activeCategory !== 'all') {
        result = result.filter(product => product.category === activeCategory);
      }

      // Lọc theo từ khóa tìm kiếm
      if (searchTerm) {
        result = result.filter(product =>
          product.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      // Lọc theo khoảng giá
      result = result.filter(product =>
        product.price >= priceRange[0] && product.price <= priceRange[1]
      );

      // Sắp xếp sản phẩm theo tùy chọn
      switch (sortOption) {
        case SORT_OPTIONS.PRICE_LOW_TO_HIGH:
          result = [...result].sort((a, b) => a.price - b.price);
          break;
        case SORT_OPTIONS.PRICE_HIGH_TO_LOW:
          result = [...result].sort((a, b) => b.price - a.price);
          break;
        case SORT_OPTIONS.NEWEST:
        default:
          // Giả sử id cao hơn là sản phẩm mới hơn
          result = [...result].sort((a, b) => b.id - a.id);
          break;
      }

      return result;
    };

    // Thực hiện lọc và cập nhật state
    setFilteredProducts(filterProducts());
  }, [activeCategory, searchTerm, priceRange, sortOption, products]);

  // Áp dụng bộ lọc - sử dụng useCallback để tránh tạo lại hàm mỗi lần render
  const applyFilters = useCallback(() => {
    // Các giá trị đã được cập nhật từ StoreSidebar component
    // Chỉ cần đóng sidebar mobile nếu đang mở
    if (showMobileSidebar) {
      setShowMobileSidebar(false);
    }

    console.log('Áp dụng bộ lọc với:', {
      category: activeCategory,
      searchTerm: searchTerm,
      priceRange: priceRange,
      sortOption: sortOption
    });
  }, [activeCategory, searchTerm, priceRange, sortOption, showMobileSidebar]);

  // Toggle mobile sidebar - sử dụng useCallback để tránh tạo lại hàm mỗi lần render
  const toggleMobileSidebar = useCallback(() => {
    setShowMobileSidebar(prev => !prev);
  }, []);

  // Tạo component con cho Hero Section để tránh re-render không cần thiết
  const HeroSection = React.memo(({ isLowPerformance }) => (
    <>
      <div className="relative bg-dexin-bg min-h-[400px] md:min-h-[500px] overflow-hidden">
        <div className="absolute inset-0 flex flex-col md:flex-row max-w-fit mx-auto px-4 lg:px-8">
          <motion.div
            className="w-full md:w-1/2 p-6 md:p-12 lg:p-16 flex flex-col justify-center"
            initial={{ opacity: 0, x: isLowPerformance ? -10 : -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: isLowPerformance ? 0.3 : 0.5,
              ease: "easeOut"
            }}
          >
            <motion.p
              className="text-dexin-primary text-sm md:text-base mb-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: isLowPerformance ? 0.1 : 0.2 }}
            >
              Rất vui được đồng hành cùng bạn!
            </motion.p>
            <motion.h1
              className="text-2xl md:text-4xl lg:text-5xl font-bold text-dexin-primary leading-tight md:leading-tight lg:leading-tight"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: isLowPerformance ? 0.2 : 0.3 }}
            >
              Dạo một vòng, <br/>
              <span className="whitespace-nowrap mt-3 md:mt-2 inline-block">sắm sửa những điều bạn yêu!</span>
            </motion.h1>
          </motion.div>
          <motion.div
            className="w-full md:w-1/2 flex items-center justify-center mt-4 md:mt-0"
            initial={{ opacity: 0, scale: isLowPerformance ? 0.95 : 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{
              duration: isLowPerformance ? 0.3 : 0.5,
              delay: isLowPerformance ? 0.1 : 0.2
            }}
          >
            <img
              src="/images/jogging.png"
              alt="Shopping Hero"
              className="w-auto h-auto max-w-full max-h-[300px] md:max-h-full object-contain content-visibility-auto"
              loading="lazy" // Thêm lazy loading cho hình ảnh
              fetchPriority="high" // Ưu tiên tải hình ảnh này
              decoding="async" // Giải mã hình ảnh không đồng bộ
            />
          </motion.div>
        </div>
      </div>
      <div className="w-full ">
          <img
            src="/images/Mask group.png"
            alt="DEXIN Thiết kế nội thất"
            className="w-full h-auto"
            loading="lazy" // Thêm lazy loading cho hình ảnh
            decoding="async" // Giải mã hình ảnh không đồng bộ
          />

      </div>
    </>
  ));

  // Tối ưu hiệu năng bằng cách sử dụng React.memo cho toàn bộ component
  const MemoizedStoreSidebar = useMemo(() => (
    <StoreSidebar
      // Truyền các state chính thức để khởi tạo state nội bộ
      searchTerm={searchTerm}
      setSearchTerm={setSearchTerm}
      activeCategory={activeCategory}
      setActiveCategory={setActiveCategory}
      priceRange={priceRange}
      setPriceRange={setPriceRange}
      sortOption={sortOption}
      setSortOption={setSortOption}
      // Giữ nguyên hàm applyFilters
      applyFilters={applyFilters}
    />
  ), [searchTerm, setSearchTerm, activeCategory, setActiveCategory, priceRange, setPriceRange, sortOption, setSortOption, applyFilters]);

  const MemoizedStoreContent = useMemo(() => (
    <StoreContent
      filteredProducts={filteredProducts}
      setSearchTerm={setSearchTerm}
      setActiveCategory={setActiveCategory}
      currentPage={currentPage}
      setCurrentPage={setCurrentPage}
      productsPerPage={productsPerPage}
    />
  ), [filteredProducts, currentPage, productsPerPage, setSearchTerm, setActiveCategory, setCurrentPage]);

  // Thêm một số tối ưu hiệu năng bổ sung
  useEffect(() => {
    // Preload hình ảnh quan trọng
    const preloadImages = () => {
      const imagesToPreload = [
        '/images/jogging.png',
        '/images/Mask group.png'
      ];

      imagesToPreload.forEach(src => {
        const img = new Image();
        img.src = src;
      });
    };

    // Chỉ preload hình ảnh nếu thiết bị có hiệu năng cao
    if (!isLowPerformance) {
      preloadImages();
    }

    // Thêm class cho body
    document.body.classList.add('store-page');

    return () => {
      // Xóa class khi unmount
      document.body.classList.remove('store-page');
    };
  }, [isLowPerformance]);

  return (
    <>
      <SEOHead
        title={pagesSEO.store.title}
        description={pagesSEO.store.description}
        keywords={pagesSEO.store.keywords}
        url={pagesSEO.store.url}
      />
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <HeroSection isLowPerformance={isLowPerformance} />

      {/* Main Content */}
      <div className="container mx-auto px-4 py-4 max-w-7xl" id="products">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar - Desktop - Sử dụng React.memo để tránh re-render không cần thiết */}
          <div className="hidden md:block w-full md:w-1/4 lg:w-1/5">
            {MemoizedStoreSidebar}
          </div>

          {/* Mobile Sidebar Toggle - Tạo component con để tránh re-render không cần thiết */}
          {useMemo(() => {
            // Tạo component con cho nút toggle mobile sidebar
            const MobileSidebarToggle = memo(() => (
              <div className="md:hidden mb-4">
                <motion.button
                  onClick={toggleMobileSidebar}
                  className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full border border-gray-300"
                  whileHover={isLowPerformance ? { backgroundColor: '#f9fafb' } : { scale: 1.05, backgroundColor: '#f9fafb' }}
                  whileTap={isLowPerformance ? { backgroundColor: '#f3f4f6' } : { scale: 0.95 }}
                  initial={{ opacity: 0, y: isLowPerformance ? -5 : -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: isLowPerformance ? 0.2 : 0.3 }}
                >
                  <motion.svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    animate={isLowPerformance ? {} : { rotate: [0, 15, 0] }}
                    transition={isLowPerformance ? {} : { duration: 1, repeat: Infinity, repeatDelay: 3 }}
                  >
                    <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                  </motion.svg>
                  <span>Bộ lọc</span>
                </motion.button>
              </div>
            ));

            return <MobileSidebarToggle />;
          }, [toggleMobileSidebar, isLowPerformance])}

          {/* Mobile Sidebar - Sử dụng AnimatePresence để tạo animation mượt mà */}
          <AnimatePresence>
            {showMobileSidebar && (
              <motion.div
                className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: isLowPerformance ? 0.1 : 0.2 }}
              >
                <motion.div
                  className="bg-white rounded-lg w-full max-w-sm max-h-[90vh] overflow-y-auto"
                  initial={{ scale: isLowPerformance ? 0.98 : 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: isLowPerformance ? 0.98 : 0.95, opacity: 0 }}
                  transition={{ duration: isLowPerformance ? 0.1 : 0.2 }}
                >
                  <div className="flex justify-between items-center p-4 border-b">
                    <h2 className="text-lg font-bold">Bộ lọc</h2>
                    <button onClick={toggleMobileSidebar}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>
                  <div className="p-4">
                    {MemoizedStoreSidebar}
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Content */}
          <div className="w-full md:w-3/4 lg:w-4/5">
            {MemoizedStoreContent}
          </div>
        </div>
      </div>
      </div>
    </>
  );
};

export default Store;
