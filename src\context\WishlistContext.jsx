import React, { createContext, useState, useContext, useEffect } from 'react';

// Tạo context
const WishlistContext = createContext();

// Hook tùy chỉnh để sử dụng context
export const useWishlist = () => useContext(WishlistContext);

export const WishlistProvider = ({ children }) => {
  // State cho danh sách sản phẩm yêu thích
  const [wishlistItems, setWishlistItems] = useState([]);
  
  // Tải danh sách sản phẩm yêu thích từ localStorage khi component được mount
  useEffect(() => {
    const storedWishlist = localStorage.getItem('wishlist');
    if (storedWishlist) {
      try {
        setWishlistItems(JSON.parse(storedWishlist));
      } catch (error) {
        console.error('Lỗi khi đọc danh sách yêu thích từ localStorage:', error);
        setWishlistItems([]);
      }
    }
  }, []);

  // <PERSON><PERSON><PERSON> danh sách sản phẩm yêu thích vào localStorage khi có thay đổi
  useEffect(() => {
    localStorage.setItem('wishlist', JSON.stringify(wishlistItems));
  }, [wishlistItems]);

  // Thêm sản phẩm vào danh sách yêu thích
  const addToWishlist = (product) => {
    setWishlistItems(prevItems => {
      // Kiểm tra xem sản phẩm đã có trong danh sách chưa
      if (!prevItems.some(item => item.id === product.id)) {
        return [...prevItems, product];
      }
      return prevItems;
    });
  };

  // Xóa sản phẩm khỏi danh sách yêu thích
  const removeFromWishlist = (productId) => {
    setWishlistItems(prevItems => 
      prevItems.filter(item => item.id !== productId)
    );
  };

  // Kiểm tra xem sản phẩm có trong danh sách yêu thích không
  const isInWishlist = (productId) => {
    return wishlistItems.some(item => item.id === productId);
  };

  // Xóa tất cả sản phẩm khỏi danh sách yêu thích
  const clearWishlist = () => {
    setWishlistItems([]);
  };

  // Cung cấp context cho các component con
  return (
    <WishlistContext.Provider
      value={{
        wishlistItems,
        addToWishlist,
        removeFromWishlist,
        isInWishlist,
        clearWishlist
      }}
    >
      {children}
    </WishlistContext.Provider>
  );
};

export default WishlistContext;
