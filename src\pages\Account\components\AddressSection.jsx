import React, { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { 
  MapPin, 
  Plus, 
  Edit2, 
  Trash2, 
  Star,
  X,
  Save,
  Home,
  Building,
  Phone
} from 'lucide-react';
import { toast } from 'react-toastify';

const AddressSection = () => {
  const [addresses, setAddresses] = useState([
    {
      id: 1,
      name: 'Nhà riêng',
      fullName: 'Nguyễn Văn <PERSON>',
      phone: '0123456789',
      address: '123 <PERSON><PERSON><PERSON><PERSON>, Phường 4',
      ward: 'Phường 4',
      district: 'Quận 5',
      city: 'TP. Hồ Chí Minh',
      isDefault: true,
      type: 'home'
    },
    {
      id: 2,
      name: '<PERSON>ăn phòng',
      fullName: 'Nguyễn Văn A',
      phone: '0987654321',
      address: '456 Lê Văn Sỹ, Phường 12',
      ward: 'Phường 12',
      district: 'Quận 3',
      city: 'TP. <PERSON><PERSON>',
      isDefault: false,
      type: 'office'
    }
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    fullName: '',
    phone: '',
    address: '',
    ward: '',
    district: '',
    city: 'TP. Hồ Chí Minh',
    type: 'home',
    isDefault: false
  });

  const addressTypes = [
    { value: 'home', label: 'Nhà riêng', icon: Home },
    { value: 'office', label: 'Văn phòng', icon: Building }
  ];

  const cities = [
    'TP. Hồ Chí Minh',
    'Hà Nội',
    'Đà Nẵng',
    'Cần Thơ',
    'Hải Phòng'
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validation
    if (!formData.name || !formData.fullName || !formData.phone || !formData.address) {
      toast.error('Vui lòng điền đầy đủ thông tin');
      return;
    }

    if (editingAddress) {
      // Cập nhật địa chỉ
      setAddresses(prev => prev.map(addr => 
        addr.id === editingAddress.id 
          ? { ...formData, id: editingAddress.id }
          : formData.isDefault ? { ...addr, isDefault: false } : addr
      ));
      toast.success('Cập nhật địa chỉ thành công!');
      setEditingAddress(null);
    } else {
      // Thêm địa chỉ mới
      const newAddress = {
        ...formData,
        id: Date.now()
      };
      
      setAddresses(prev => {
        if (formData.isDefault) {
          return [...prev.map(addr => ({ ...addr, isDefault: false })), newAddress];
        }
        return [...prev, newAddress];
      });
      toast.success('Thêm địa chỉ thành công!');
    }

    // Reset form
    setFormData({
      name: '',
      fullName: '',
      phone: '',
      address: '',
      ward: '',
      district: '',
      city: 'TP. Hồ Chí Minh',
      type: 'home',
      isDefault: false
    });
    setShowAddForm(false);
  };

  const handleEdit = (address) => {
    setFormData(address);
    setEditingAddress(address);
    setShowAddForm(true);
  };

  const handleDelete = (addressId) => {
    if (addresses.find(addr => addr.id === addressId)?.isDefault) {
      toast.error('Không thể xóa địa chỉ mặc định');
      return;
    }
    
    setAddresses(prev => prev.filter(addr => addr.id !== addressId));
    toast.success('Xóa địa chỉ thành công!');
  };

  const handleSetDefault = (addressId) => {
    setAddresses(prev => prev.map(addr => ({
      ...addr,
      isDefault: addr.id === addressId
    })));
    toast.success('Đã đặt làm địa chỉ mặc định!');
  };

  const handleCancel = () => {
    setShowAddForm(false);
    setEditingAddress(null);
    setFormData({
      name: '',
      fullName: '',
      phone: '',
      address: '',
      ward: '',
      district: '',
      city: 'TP. Hồ Chí Minh',
      type: 'home',
      isDefault: false
    });
  };

  const getTypeIcon = (type) => {
    const typeConfig = addressTypes.find(t => t.value === type);
    const Icon = typeConfig?.icon || Home;
    return <Icon className="w-4 h-4" />;
  };

  const getTypeLabel = (type) => {
    return addressTypes.find(t => t.value === type)?.label || 'Nhà riêng';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-lg shadow-lg overflow-hidden"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-dexin-primary to-dexin-light px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-white">Địa chỉ giao hàng</h2>
            <p className="text-dexin-bg text-sm mt-1">Quản lý địa chỉ nhận hàng</p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-white text-dexin-primary rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span className="font-medium">Thêm địa chỉ</span>
          </button>
        </div>
      </div>

      <div className="p-6">
        {/* Address list */}
        <div className="space-y-4 mb-6">
          {addresses.map((address) => (
            <motion.div
              key={address.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    {getTypeIcon(address.type)}
                    <span className="font-semibold text-gray-900">{address.name}</span>
                    {address.isDefault && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-dexin-light-20 text-dexin-primary">
                        <Star className="w-3 h-3 mr-1" />
                        Mặc định
                      </span>
                    )}
                  </div>
                  
                  <div className="text-gray-700 space-y-1">
                    <p className="font-medium">{address.fullName}</p>
                    <p className="flex items-center space-x-1">
                      <Phone className="w-3 h-3" />
                      <span>{address.phone}</span>
                    </p>
                    <p className="flex items-start space-x-1">
                      <MapPin className="w-3 h-3 mt-1 flex-shrink-0" />
                      <span>
                        {address.address}, {address.ward}, {address.district}, {address.city}
                      </span>
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  {!address.isDefault && (
                    <button
                      onClick={() => handleSetDefault(address.id)}
                      className="p-2 text-gray-400 hover:text-dexin-primary hover:bg-dexin-light-10 rounded-lg transition-colors"
                      title="Đặt làm mặc định"
                    >
                      <Star className="w-4 h-4" />
                    </button>
                  )}
                  <button
                    onClick={() => handleEdit(address)}
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Chỉnh sửa"
                  >
                    <Edit2 className="w-4 h-4" />
                  </button>
                  {!address.isDefault && (
                    <button
                      onClick={() => handleDelete(address.id)}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Xóa"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Add/Edit form */}
        <AnimatePresence>
          {showAddForm && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border border-gray-200 rounded-lg p-6 bg-gray-50"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  {editingAddress ? 'Chỉnh sửa địa chỉ' : 'Thêm địa chỉ mới'}
                </h3>
                <button
                  onClick={handleCancel}
                  className="p-1 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tên địa chỉ *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="VD: Nhà riêng, Văn phòng"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Loại địa chỉ
                    </label>
                    <select
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"
                    >
                      {addressTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Họ và tên *
                    </label>
                    <input
                      type="text"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Số điện thoại *
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Địa chỉ cụ thể *
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    placeholder="Số nhà, tên đường"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phường/Xã
                    </label>
                    <input
                      type="text"
                      name="ward"
                      value={formData.ward}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Quận/Huyện
                    </label>
                    <input
                      type="text"
                      name="district"
                      value={formData.district}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tỉnh/Thành phố
                    </label>
                    <select
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary"
                    >
                      {cities.map(city => (
                        <option key={city} value={city}>{city}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isDefault"
                    name="isDefault"
                    checked={formData.isDefault}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-dexin-primary focus:ring-dexin-primary border-gray-300 rounded"
                  />
                  <label htmlFor="isDefault" className="ml-2 text-sm text-gray-700">
                    Đặt làm địa chỉ mặc định
                  </label>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex items-center space-x-2 px-4 py-2 bg-dexin-primary text-white rounded-lg hover:bg-dexin-light-90 transition-colors"
                  >
                    <Save className="w-4 h-4" />
                    <span>{editingAddress ? 'Cập nhật' : 'Lưu địa chỉ'}</span>
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Hủy
                  </button>
                </div>
              </form>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default AddressSection;
