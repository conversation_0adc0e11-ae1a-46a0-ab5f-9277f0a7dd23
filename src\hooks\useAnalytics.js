import { useCallback } from 'react';
import { trackUserAction } from '../components/common/Analytics';

/**
 * Custom hook để tracking các user actions với Vercel Analytics
 * Cung cấp các method thuận tiện để track events
 */
const useAnalytics = () => {
  
  // Track khi user thêm sản phẩm vào giỏ hàng
  const trackAddToCart = useCallback((product) => {
    trackUserAction.addToCart(
      product.id,
      product.name,
      product.price
    );
  }, []);

  // Track khi user thêm vào wishlist
  const trackAddToWishlist = useCallback((product) => {
    trackUserAction.addToWishlist(
      product.id,
      product.name
    );
  }, []);

  // Track khi user đăng ký thành công
  const trackSignUp = useCallback((method = 'email') => {
    trackUserAction.signUp(method);
  }, []);

  // Track khi user đăng nhập thành công
  const trackSignIn = useCallback((method = 'email') => {
    trackUserAction.signIn(method);
  }, []);

  // Track khi user hoàn thành đơn hàng
  const trackPurchase = useCallback((orderData) => {
    trackUserAction.purchase(
      orderData.orderId,
      orderData.total,
      orderData.items
    );
  }, []);

  // Track khi user tương tác với AI Chat
  const trackChatInteraction = useCallback((messageType = 'user') => {
    trackUserAction.chatInteraction(messageType);
  }, []);

  // Track khi user sử dụng design tools
  const trackDesignTool = useCallback((action, toolType) => {
    trackUserAction.designToolUsage(action, toolType);
  }, []);

  // Track khi user search
  const trackSearch = useCallback((query, category = 'general') => {
    trackUserAction.search(query, category);
  }, []);

  return {
    trackAddToCart,
    trackAddToWishlist,
    trackSignUp,
    trackSignIn,
    trackPurchase,
    trackChatInteraction,
    trackDesignTool,
    trackSearch
  };
};

export default useAnalytics; 