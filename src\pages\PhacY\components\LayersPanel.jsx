import React, { useState } from 'react';
import { motion } from 'motion/react';
import { ChevronUp, ChevronDown, Eye, EyeOff, Edit2, Trash2 } from 'lucide-react';
import { classNames } from '../../../utils/classNames';

const LayersPanel = ({
  items,
  selectedId,
  setSelectedId,
  onItemChange,
  onItemDelete,
  isLocked
}) => {
  // State cho item đang được chỉnh sửa tên
  const [editingItemId, setEditingItemId] = useState(null);
  const [editingName, setEditingName] = useState('');

  // Xử lý khi click vào một item trong danh sách
  const handleItemClick = (itemId) => {
    if (isLocked) return;
    setSelectedId(itemId);
  };

  // Xử lý khi di chuyển item lên trên (tăng zIndex)
  const handleMoveUp = (itemId, reversedIndex) => {
    if (isLocked || reversedIndex === items.length - 1) return; // Không thể di chuyển lên nếu đã ở trên cùng

    // Tìm item cần di chuyển
    const itemToMoveIndex = items.findIndex(item => item.id === itemId);
    if (itemToMoveIndex === -1) return;

    // Tạo một bản sao của mảng items
    const newItems = [...items];

    // Lấy item cần di chuyển
    const itemToMove = {...newItems[itemToMoveIndex]};

    // Xóa item khỏi vị trí hiện tại
    newItems.splice(itemToMoveIndex, 1);

    // Chèn item vào vị trí mới (trên một bậc trong giao diện = xuống một bậc trong mảng)
    // Vì chúng ta đã xóa một phần tử, nên cần điều chỉnh vị trí chèn
    const insertIndex = Math.min(itemToMoveIndex + 1, newItems.length);
    newItems.splice(insertIndex, 0, itemToMove);

    // Cập nhật danh sách items
    onItemChange(newItems);
  };

  // Xử lý khi di chuyển item xuống dưới (giảm zIndex)
  const handleMoveDown = (itemId, reversedIndex) => {
    if (isLocked || reversedIndex === 0) return; // Không thể di chuyển xuống nếu đã ở dưới cùng

    // Tìm item cần di chuyển
    const itemToMoveIndex = items.findIndex(item => item.id === itemId);
    if (itemToMoveIndex === -1) return;

    // Tạo một bản sao của mảng items
    const newItems = [...items];

    // Lấy item cần di chuyển
    const itemToMove = {...newItems[itemToMoveIndex]};

    // Xóa item khỏi vị trí hiện tại
    newItems.splice(itemToMoveIndex, 1);

    // Chèn item vào vị trí mới (xuống một bậc trong giao diện = lên một bậc trong mảng)
    // Vì chúng ta đã xóa một phần tử, nên cần điều chỉnh vị trí chèn
    const insertIndex = Math.max(itemToMoveIndex - 1, 0);
    newItems.splice(insertIndex, 0, itemToMove);

    // Cập nhật danh sách items
    onItemChange(newItems);
  };

  // Xử lý khi ẩn/hiện một item
  const handleToggleVisibility = (itemId) => {
    if (isLocked) return;

    // Tìm item cần thay đổi
    const itemIndex = items.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    // Tạo bản sao của mảng items
    const newItems = [...items];
    // Tạo bản sao của item cần thay đổi
    const updatedItem = { ...newItems[itemIndex] };
    // Đảo ngược trạng thái hiển thị
    updatedItem.isHidden = !updatedItem.isHidden;
    // Cập nhật item trong mảng
    newItems[itemIndex] = updatedItem;

    // Cập nhật danh sách items
    onItemChange(newItems);
  };

  // Xử lý khi bắt đầu chỉnh sửa tên item
  const handleStartEditing = (itemId, currentName) => {
    if (isLocked) return;
    setEditingItemId(itemId);
    setEditingName(currentName || 'Đồ nội thất');
  };

  // Xử lý khi kết thúc chỉnh sửa tên item
  const handleFinishEditing = (itemId) => {
    if (isLocked) return;

    // Tìm item cần thay đổi
    const itemIndex = items.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    // Tạo bản sao của mảng items
    const newItems = [...items];
    // Tạo bản sao của item cần thay đổi
    const updatedItem = { ...newItems[itemIndex] };
    // Cập nhật tên mới
    updatedItem.name = editingName || 'Đồ nội thất';
    // Cập nhật item trong mảng
    newItems[itemIndex] = updatedItem;

    // Cập nhật danh sách items
    onItemChange(newItems);

    // Reset state chỉnh sửa
    setEditingItemId(null);
    setEditingName('');
  };

  // Xử lý khi nhấn Enter trong input chỉnh sửa tên
  const handleKeyDown = (e, itemId) => {
    if (e.key === 'Enter') {
      handleFinishEditing(itemId);
    }
  };

  return (
    <motion.div
      className="fixed right-6 top-24 w-80 bg-white rounded-xl shadow-xl overflow-hidden z-50"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ duration: 0.3 }}
      style={{ maxHeight: 'calc(100vh - 200px)' }}
    >
      <div className="p-4 bg-dexin-light text-white">
        <h3 className="text-lg font-semibold">Quản lý lớp</h3>
        <p className="text-sm opacity-80">Sắp xếp thứ tự hiển thị các đối tượng</p>
      </div>

      <div className="max-h-[400px] overflow-y-auto p-2" style={{
        scrollbarWidth: 'thin',
        scrollbarColor: 'var(--dexin-light) #f3f4f6'
      }}>
        {items.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            Chưa có đối tượng nào trên canvas
          </div>
        ) : (
          <ul className="space-y-2">
            {/* Hiển thị danh sách items theo thứ tự ngược lại (item cuối cùng sẽ hiển thị trên cùng) */}
            {[...items].reverse().map((item, index) => {
              const reversedIndex = items.length - 1 - index;
              return (
                <li
                  key={item.id}
                  className={classNames(
                    "p-3 rounded-lg border transition-all duration-200",
                    item.id === selectedId
                      ? "border-dexin-light bg-dexin-light bg-opacity-10"
                      : "border-gray-200 hover:border-gray-300",
                    item.isHidden && "opacity-50",
                    isLocked ? "cursor-not-allowed" : "cursor-pointer"
                  )}
                  onClick={() => handleItemClick(item.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      {/* Icon hiển thị loại đồ nội thất */}
                      <div className="w-10 h-10 rounded-md bg-gray-100 flex-shrink-0 overflow-hidden">
                        <img
                          src={item.src}
                          alt={item.name || 'Đồ nội thất'}
                          className="w-full h-full object-contain"
                          style={{ opacity: item.isHidden ? 0.5 : 1 }}
                        />
                      </div>

                      {/* Tên đồ nội thất */}
                      <div className="flex-1 min-w-0">
                        {editingItemId === item.id ? (
                          <input
                            type="text"
                            className="w-full px-2 py-1 border border-dexin-light rounded focus:outline-none"
                            value={editingName}
                            onChange={(e) => setEditingName(e.target.value)}
                            onBlur={() => handleFinishEditing(item.id)}
                            onKeyDown={(e) => handleKeyDown(e, item.id)}
                            autoFocus
                          />
                        ) : (
                          <div className="truncate font-medium">
                            {item.name || 'Đồ nội thất'}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Các nút điều khiển */}
                    <div className="flex items-center space-x-1">
                      {/* Nút ẩn/hiện */}
                      <button
                        className={classNames(
                          "p-1 rounded-full hover:bg-gray-100 transition-colors",
                          isLocked && "opacity-50 cursor-not-allowed"
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!isLocked) handleToggleVisibility(item.id);
                        }}
                        disabled={isLocked}
                        title={item.isHidden ? "Hiện" : "Ẩn"}
                      >
                        {item.isHidden ? <EyeOff size={16} /> : <Eye size={16} />}
                      </button>

                      {/* Nút chỉnh sửa tên */}
                      <button
                        className={classNames(
                          "p-1 rounded-full hover:bg-gray-100 transition-colors",
                          isLocked && "opacity-50 cursor-not-allowed"
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!isLocked) handleStartEditing(item.id, item.name);
                        }}
                        disabled={isLocked}
                        title="Đổi tên"
                      >
                        <Edit2 size={16} />
                      </button>

                      {/* Nút di chuyển lên */}
                      <button
                        className={classNames(
                          "p-1 rounded-full hover:bg-gray-100 transition-colors",
                          (isLocked || reversedIndex === items.length - 1) && "opacity-50 cursor-not-allowed"
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!isLocked) handleMoveUp(item.id, reversedIndex);
                        }}
                        disabled={isLocked || reversedIndex === items.length - 1}
                        title="Di chuyển lên"
                      >
                        <ChevronUp size={16} />
                      </button>

                      {/* Nút di chuyển xuống */}
                      <button
                        className={classNames(
                          "p-1 rounded-full hover:bg-gray-100 transition-colors",
                          (isLocked || reversedIndex === 0) && "opacity-50 cursor-not-allowed"
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!isLocked) handleMoveDown(item.id, reversedIndex);
                        }}
                        disabled={isLocked || reversedIndex === 0}
                        title="Di chuyển xuống"
                      >
                        <ChevronDown size={16} />
                      </button>

                      {/* Nút xóa */}
                      <button
                        className={classNames(
                          "p-1 rounded-full hover:bg-red-100 text-red-500 transition-colors",
                          isLocked && "opacity-50 cursor-not-allowed"
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!isLocked) onItemDelete(item.id);
                        }}
                        disabled={isLocked}
                        title="Xóa"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        )}
      </div>
    </motion.div>
  );
};

export default LayersPanel;
