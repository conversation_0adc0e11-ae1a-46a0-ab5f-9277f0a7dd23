import React, { useEffect, useState } from 'react';
import { useLocation, Routes, Route, useNavigate } from 'react-router-dom';
import Loading from '../sections/Loading';
import { useLoading } from '../../context/LoadingContext';

const LoadingRoute = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { startLoading, stopLoading } = useLoading();
  const [showLoader, setShowLoader] = useState(false);
  const [prevLoc, setPrevLoc] = useState('');
  const [content, setContent] = useState(children);
  
  useEffect(() => {
    // Khởi tạo vị trí ban đầu
    if (!prevLoc) {
      setPrevLoc(location.pathname);
      setContent(children);
      return;
    }
    
    // Chỉ thực hiện khi đường dẫn thay đổi
    if (prevLoc !== location.pathname) {
      // Hiển thị màn hình loading
      startLoading();
      setShowLoader(true);
      
      // Giữ nội dung hiện tại cho đến khi loading hoàn tất
      const timer = setTimeout(() => {
        // Sau khi loading hoàn tất, cập nhật nội dung và ẩn loading
        stopLoading();
        setShowLoader(false);
        setPrevLoc(location.pathname);
        setContent(children);
      }, 800);
      
      return () => {
        clearTimeout(timer);
      };
    } else {
      // Cập nhật nội dung nếu children thay đổi nhưng không đổi đường dẫn
      setContent(children);
    }
  }, [location.pathname, children, startLoading, stopLoading, prevLoc]);
  
  return (
    <>
      {showLoader ? (
        <div className="fixed inset-0 bg-dexin-bg bg-opacity-100 flex items-center justify-center z-50">
          <Loading />
        </div>
      ) : (
        content
      )}
    </>
  );
};

export default LoadingRoute; 