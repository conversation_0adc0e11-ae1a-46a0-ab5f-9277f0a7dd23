import React from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'motion/react';
import { ChevronRight } from 'lucide-react';
import { useCart } from '../../context/CartContext';
import CartItem from './components/CartItem';
import CartSummary from './components/CartSummary';
import EmptyCart from './components/EmptyCart';

const CartPage = () => {
  const { cartItems, cartTotal, updateQuantity, removeFromCart } = useCart();

  // Cart steps
  const cartSteps = [
    { id: 1, name: 'Giỏ hàng của bạn', isCurrent: true },
    { id: 2, name: 'Thông tin thanh toán', isCurrent: false },
    { id: 3, name: '<PERSON>àn tất đơn hàng', isCurrent: false },
  ];

  return (
    <div className="bg-gray-50 min-h-screen py-8">
      <div className="container mx-auto px-4">
        <div className="mb-8">
          <motion.h1 
            className="text-3xl font-bold text-center text-dexin-primary mb-6"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            Giỏ Hàng
          </motion.h1>
          
          {/* Breadcrumb */}
          <motion.nav 
            className="flex justify-center mb-8"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <ol className="flex items-center space-x-2 md:space-x-4">
              {cartSteps.map((step, index) => (
                <li key={step.id} className="flex items-center">
                  {index > 0 && (
                    <ChevronRight className="h-4 w-4 text-gray-400 mx-2" />
                  )}
                  <div 
                    className={`flex items-center ${
                      step.isCurrent ? 'text-dexin-primary font-medium' : 'text-gray-500'
                    }`}
                  >
                    <span className={`h-6 w-6 flex items-center justify-center rounded-full mr-2 ${
                      step.isCurrent ? 'bg-dexin-light text-white' : 'bg-gray-200 text-gray-600'
                    }`}>
                      {step.id}
                    </span>
                    <span>{step.name}</span>
                  </div>
                </li>
              ))}
            </ol>
          </motion.nav>
        </div>

        {cartItems.length === 0 ? (
          <EmptyCart />
        ) : (
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Danh sách sản phẩm */}
            <motion.div 
              className="lg:w-2/3"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="bg-white rounded-lg shadow-sm p-6">
                {/* Tiêu đề */}
                <div className="hidden md:flex md:items-center text-gray-600 font-medium pb-4 border-b border-gray-200">
                  <div className="md:w-1/6 text-center">Sản phẩm</div>
                  <div className="md:w-2/6 md:px-4">Thông tin</div>
                  <div className="md:w-1/6 text-center">Đơn giá</div>
                  <div className="md:w-1/6 text-center">Số lượng</div>
                  <div className="md:w-1/6 text-right">Tổng tiền</div>
                  <div className="w-8"></div>
                </div>

                {/* Danh sách sản phẩm */}
                <AnimatePresence>
                  {cartItems.map((item, index) => (
                    <CartItem
                      key={item.cartKey || item.id}
                      item={item}
                      updateQuantity={updateQuantity}
                      removeItem={removeFromCart}
                    />
                  ))}
                </AnimatePresence>

                {/* Tiếp tục mua sắm */}
                <div className="mt-6">
                  <Link to="/store" className="text-dexin-primary hover:underline flex items-center">
                    <ChevronRight className="h-4 w-4 mr-1 rotate-180" />
                    <span>Tiếp tục mua sắm</span>
                  </Link>
                </div>
              </div>
            </motion.div>

            {/* Tóm tắt đơn hàng */}
            <motion.div 
              className="lg:w-1/3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <CartSummary cartTotal={cartTotal} />
            </motion.div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CartPage; 