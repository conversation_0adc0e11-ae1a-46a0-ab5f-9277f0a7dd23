import{o as e,q as t,r as a,s as n,h as r,j as i,m as s,A as o,b as c,B as l,a as d,u,y as h,L as m,H as p,X as g,t as f,E as x,R as v,S as b,p as y}from"./index-DdBL2cja.js";import{C as w}from"./chevron-up-A3k7AcMu.js";import{C as j}from"./check-DdKjhOwX.js";import{S as k}from"./sliders-vertical-DIr8HYFG.js";import{z as N,L as T,S as M,P as C}from"./ProductVariantModal-Btj1bdW7.js";import{g as O,a as S}from"./products-C2yRfJkG.js";import"./minus-Fx_j7jOv.js";import"./plus-66Jg-RVc.js";function R(){!e.current&&t();const[r]=a.useState(n.current);return r}var D,E,L,I,P={},A={},F={},z={};function B(){return D||(D=1,Object.defineProperty(z,"__esModule",{value:!0}),z.Direction=void 0,(t=e||(z.Direction=e={})).Right="to right",t.Left="to left",t.Down="to bottom",t.Up="to top"),z;var e,t}function _(){return E||(E=1,function(e){var t=F&&F.__spreadArray||function(e,t,a){if(a||2===arguments.length)for(var n,r=0,i=t.length;r<i;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(e,"__esModule",{value:!0}),e.isIOS=e.useThumbOverlap=e.assertUnreachable=e.voidFn=e.getTrackBackground=e.replaceAt=e.schd=e.translate=e.getClosestThumbIndex=e.translateThumbs=e.getPaddingAndBorder=e.getMargin=e.checkInitialOverlap=e.checkValuesAgainstBoundaries=e.checkBoundaries=e.isVertical=e.relativeValue=e.normalizeValue=e.isStepDivisible=e.isTouchEvent=e.getStepDecimals=void 0;var a=r(),n=B();function i(e){return e===n.Direction.Up||e===n.Direction.Down}function s(e,t,a){e.style.transform="translate(".concat(t,"px, ").concat(a,"px)")}e.getStepDecimals=function(e){var t=e.toString().split(".")[1];return t?t.length:0},e.isTouchEvent=function(e){return e.touches&&e.touches.length||e.changedTouches&&e.changedTouches.length},e.isStepDivisible=function(e,t,a){var n=Number(((t-e)/a).toFixed(8));return parseInt(n.toString(),10)===n},e.normalizeValue=function(t,a,n,r,i,s,o){var c=1e11;if(t=Math.round(t*c)/c,!s){var l=o[a-1],d=o[a+1];if(l&&l>t)return l;if(d&&d<t)return d}if(t>r)return r;if(t<n)return n;var u=Math.floor(t*c-n*c)%Math.floor(i*c),h=Math.floor(t*c-Math.abs(u)),m=0===u?t:h/c,p=Math.abs(u/c)<i/2?m:m+i,g=(0,e.getStepDecimals)(i);return parseFloat(p.toFixed(g))},e.relativeValue=function(e,t,a){return(e-t)/(a-t)},e.isVertical=i,e.checkBoundaries=function(e,t,a){if(t>=a)throw new RangeError("min (".concat(t,") is equal/bigger than max (").concat(a,")"));if(e<t)throw new RangeError("value (".concat(e,") is smaller than min (").concat(t,")"));if(e>a)throw new RangeError("value (".concat(e,") is bigger than max (").concat(a,")"))},e.checkValuesAgainstBoundaries=function(e,t,a){return e<t?t:e>a?a:e},e.checkInitialOverlap=function(e){if(!(e.length<2||e.slice(1).every((function(t,a){return e[a]<=t}))))throw new RangeError("values={[".concat(e,"]} needs to be sorted when allowOverlap={false}"))},e.getMargin=function(e){var t=window.getComputedStyle(e);return{top:parseInt(t["margin-top"],10),bottom:parseInt(t["margin-bottom"],10),left:parseInt(t["margin-left"],10),right:parseInt(t["margin-right"],10)}},e.getPaddingAndBorder=function(e){var t=window.getComputedStyle(e);return{top:parseInt(t["padding-top"],10)+parseInt(t["border-top-width"],10),bottom:parseInt(t["padding-bottom"],10)+parseInt(t["border-bottom-width"],10),left:parseInt(t["padding-left"],10)+parseInt(t["border-left-width"],10),right:parseInt(t["padding-right"],10)+parseInt(t["border-right-width"],10)}},e.translateThumbs=function(e,t,a){var n=a?-1:1;e.forEach((function(e,a){return s(e,n*t[a].x,t[a].y)}))},e.getClosestThumbIndex=function(e,t,a,n){for(var r=0,i=c(e[0],t,a,n),s=1;s<e.length;s++){var o=c(e[s],t,a,n);o<i&&(i=o,r=s)}return r},e.translate=s,e.schd=function(e){var t=[],a=null;return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];t=n,a||(a=requestAnimationFrame((function(){a=null,e.apply(void 0,t)})))}},e.replaceAt=function(e,t,a){var n=e.slice(0);return n[t]=a,n},e.getTrackBackground=function(e){var t=e.values,a=e.colors,r=e.min,i=e.max,s=e.direction,o=void 0===s?n.Direction.Right:s,c=e.rtl,l=void 0!==c&&c;l&&o===n.Direction.Right?o=n.Direction.Left:l&&n.Direction.Left&&(o=n.Direction.Right);var d=t.slice(0).sort((function(e,t){return e-t})).map((function(e){return(e-r)/(i-r)*100})).reduce((function(e,t,n){return"".concat(e,", ").concat(a[n]," ").concat(t,"%, ").concat(a[n+1]," ").concat(t,"%")}),"");return"linear-gradient(".concat(o,", ").concat(a[0]," 0%").concat(d,", ").concat(a[a.length-1]," 100%)")},e.voidFn=function(){},e.assertUnreachable=function(e){throw new Error("Didn't expect to get here")};var o=function(e,a,n,r,i){return void 0===i&&(i=function(e){return e}),Math.ceil(t([e],Array.from(e.children),!0).reduce((function(e,t){var s=Math.ceil(t.getBoundingClientRect().width);if(t.innerText&&t.innerText.includes(n)&&0===t.childElementCount){var o=t.cloneNode(!0);o.innerHTML=i(a.toFixed(r)),o.style.visibility="hidden",document.body.appendChild(o),s=Math.ceil(o.getBoundingClientRect().width),document.body.removeChild(o)}return s>e?s:e}),e.getBoundingClientRect().width))};function c(e,t,a,n){var r=e.getBoundingClientRect(),s=r.left,o=r.top,c=r.width,l=r.height;return i(n)?Math.abs(a-(o+l/2)):Math.abs(t-(s+c/2))}e.useThumbOverlap=function(n,r,i,s,c,l){void 0===s&&(s=.1),void 0===c&&(c=" - "),void 0===l&&(l=function(e){return e});var d=(0,e.getStepDecimals)(s),u=(0,a.useState)({}),h=u[0],m=u[1],p=(0,a.useState)(l(r[i].toFixed(d))),g=p[0],f=p[1];return(0,a.useEffect)((function(){if(n){var e=n.getThumbs();if(e.length<1)return;var a={},s=n.getOffsets(),u=function(e,a,n,r,i,s,c){void 0===c&&(c=function(e){return e});var l=[],d=function(e){var u=o(n[e],r[e],i,s,c),h=a[e].x;a.forEach((function(a,m){var p=a.x,g=o(n[m],r[m],i,s,c);e!==m&&(h>=p&&h<=p+g||h+u>=p&&h+u<=p+g)&&(l.includes(m)||(l.push(e),l.push(m),l=t(t([],l,!0),[e,m],!1),d(m)))}))};return d(e),Array.from(new Set(l.sort()))}(i,s,e,r,c,d,l),h=l(r[i].toFixed(d));if(u.length){var p=u.reduce((function(e,a,n,r){return e.length?t(t([],e,!0),[s[r[n]].x],!1):[s[r[n]].x]}),[]);if(Math.min.apply(Math,p)===s[i].x){var g=[];u.forEach((function(e){g.push(r[e].toFixed(d))})),h=Array.from(new Set(g.sort((function(e,t){return parseFloat(e)-parseFloat(t)})))).map(l).join(c);var x=Math.min.apply(Math,p),v=Math.max.apply(Math,p),b=e[u[p.indexOf(v)]].getBoundingClientRect().width;a.left="".concat(Math.abs(x-(v+b))/2,"px"),a.transform="translate(-50%, 0)"}else a.visibility="hidden"}f(h),m(a)}}),[n,r]),[g,h]},e.isIOS=function(){var e,t=(null===(e=navigator.userAgentData)||void 0===e?void 0:e.platform)||navigator.platform;return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(t)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}}(F)),F}function V(){if(L)return A;L=1;var e=A&&A.__extends||function(){var e=function(t,a){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])})(t,a)};return function(t,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function n(){this.constructor=t}e(t,a),t.prototype=null===a?Object.create(a):(n.prototype=a.prototype,new n)}}(),t=A&&A.__createBinding||(Object.create?function(e,t,a,n){void 0===n&&(n=a);var r=Object.getOwnPropertyDescriptor(t,a);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[a]}}),Object.defineProperty(e,n,r)}:function(e,t,a,n){void 0===n&&(n=a),e[n]=t[a]}),a=A&&A.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),n=A&&A.__importStar||function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&t(n,e,r);return a(n,e),n},i=A&&A.__spreadArray||function(e,t,a){if(a||2===arguments.length)for(var n,r=0,i=t.length;r<i;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(A,"__esModule",{value:!0});var s=n(r()),o=_(),c=B(),l=["ArrowRight","ArrowUp","k","PageUp"],d=["ArrowLeft","ArrowDown","j","PageDown"],u=function(t){function a(e){var a=t.call(this,e)||this;if(a.trackRef=s.createRef(),a.thumbRefs=[],a.state={draggedTrackPos:[-1,-1],draggedThumbIndex:-1,thumbZIndexes:new Array(a.props.values.length).fill(0).map((function(e,t){return t})),isChanged:!1,markOffsets:[]},a.getOffsets=function(){var e=a.props,t=e.direction,n=e.values,r=e.min,i=e.max,s=a.trackRef.current;if(!s)return[];var l=s.getBoundingClientRect(),d=(0,o.getPaddingAndBorder)(s);return a.getThumbs().map((function(e,a){var s={x:0,y:0},u=e.getBoundingClientRect(),h=(0,o.getMargin)(e);switch(t){case c.Direction.Right:return s.x=-1*(h.left+d.left),s.y=-1*((u.height-l.height)/2+d.top),s.x+=l.width*(0,o.relativeValue)(n[a],r,i)-u.width/2,s;case c.Direction.Left:return s.x=-1*(h.right+d.right),s.y=-1*((u.height-l.height)/2+d.top),s.x+=l.width-l.width*(0,o.relativeValue)(n[a],r,i)-u.width/2,s;case c.Direction.Up:return s.x=-1*((u.width-l.width)/2+h.left+d.left),s.y=-d.left,s.y+=l.height-l.height*(0,o.relativeValue)(n[a],r,i)-u.height/2,s;case c.Direction.Down:return s.x=-1*((u.width-l.width)/2+h.left+d.left),s.y=-d.left,s.y+=l.height*(0,o.relativeValue)(n[a],r,i)-u.height/2,s;default:return(0,o.assertUnreachable)(t)}}))},a.getThumbs=function(){return a.trackRef&&a.trackRef.current?Array.from(a.trackRef.current.children).filter((function(e){return e.hasAttribute("aria-valuenow")})):[]},a.getTargetIndex=function(e){return a.getThumbs().findIndex((function(t){return t===e.target||t.contains(e.target)}))},a.addTouchEvents=function(e){document.addEventListener("touchmove",a.schdOnTouchMove,{passive:!1}),document.addEventListener("touchend",a.schdOnEnd,{passive:!1}),document.addEventListener("touchcancel",a.schdOnEnd,{passive:!1})},a.addMouseEvents=function(e){document.addEventListener("mousemove",a.schdOnMouseMove),document.addEventListener("mouseup",a.schdOnEnd)},a.onMouseDownTrack=function(e){var t;if(0===e.button&&!(0,o.isIOS)())if(e.persist(),e.preventDefault(),a.addMouseEvents(e.nativeEvent),a.props.values.length>1&&a.props.draggableTrack){if(a.thumbRefs.some((function(t){var a;return null===(a=t.current)||void 0===a?void 0:a.contains(e.target)})))return;a.setState({draggedTrackPos:[e.clientX,e.clientY]},(function(){return a.onMove(e.clientX,e.clientY)}))}else{var n=(0,o.getClosestThumbIndex)(a.thumbRefs.map((function(e){return e.current})),e.clientX,e.clientY,a.props.direction);null===(t=a.thumbRefs[n].current)||void 0===t||t.focus(),a.setState({draggedThumbIndex:n},(function(){return a.onMove(e.clientX,e.clientY)}))}},a.onResize=function(){(0,o.translateThumbs)(a.getThumbs(),a.getOffsets(),a.props.rtl),a.calculateMarkOffsets()},a.onTouchStartTrack=function(e){var t;if(e.persist(),a.addTouchEvents(e.nativeEvent),a.props.values.length>1&&a.props.draggableTrack){if(a.thumbRefs.some((function(t){var a;return null===(a=t.current)||void 0===a?void 0:a.contains(e.target)})))return;a.setState({draggedTrackPos:[e.touches[0].clientX,e.touches[0].clientY]},(function(){return a.onMove(e.touches[0].clientX,e.touches[0].clientY)}))}else{var n=(0,o.getClosestThumbIndex)(a.thumbRefs.map((function(e){return e.current})),e.touches[0].clientX,e.touches[0].clientY,a.props.direction);null===(t=a.thumbRefs[n].current)||void 0===t||t.focus(),a.setState({draggedThumbIndex:n},(function(){return a.onMove(e.touches[0].clientX,e.touches[0].clientY)}))}},a.onMouseOrTouchStart=function(e){if(!a.props.disabled){var t=(0,o.isTouchEvent)(e);if(t||0===e.button){var n=a.getTargetIndex(e);-1!==n&&(t?a.addTouchEvents(e):a.addMouseEvents(e),a.setState({draggedThumbIndex:n,thumbZIndexes:a.state.thumbZIndexes.map((function(e,t){return t===n?Math.max.apply(Math,a.state.thumbZIndexes):e<=a.state.thumbZIndexes[n]?e:e-1}))}))}}},a.onMouseMove=function(e){e.preventDefault(),a.onMove(e.clientX,e.clientY)},a.onTouchMove=function(e){e.preventDefault(),a.onMove(e.touches[0].clientX,e.touches[0].clientY)},a.onKeyDown=function(e){var t=a.props,n=t.values,r=t.onChange,i=t.step,s=t.rtl,u=t.direction,h=a.state.isChanged,m=a.getTargetIndex(e.nativeEvent),p=s||u===c.Direction.Left||u===c.Direction.Down?-1:1;-1!==m&&(l.includes(e.key)?(e.preventDefault(),a.setState({draggedThumbIndex:m,isChanged:!0}),r((0,o.replaceAt)(n,m,a.normalizeValue(n[m]+p*("PageUp"===e.key?10*i:i),m)))):d.includes(e.key)?(e.preventDefault(),a.setState({draggedThumbIndex:m,isChanged:!0}),r((0,o.replaceAt)(n,m,a.normalizeValue(n[m]-p*("PageDown"===e.key?10*i:i),m)))):"Tab"===e.key?a.setState({draggedThumbIndex:-1},(function(){h&&a.fireOnFinalChange()})):h&&a.fireOnFinalChange())},a.onKeyUp=function(e){var t=a.state.isChanged;a.setState({draggedThumbIndex:-1},(function(){t&&a.fireOnFinalChange()}))},a.onMove=function(e,t){var n=a.state,r=n.draggedThumbIndex,i=n.draggedTrackPos,s=a.props,l=s.direction,d=s.min,u=s.max,h=s.onChange,m=s.values,p=s.step,g=s.rtl;if(-1===r&&-1===i[0]&&-1===i[1])return null;var f=a.trackRef.current;if(!f)return null;var x=f.getBoundingClientRect(),v=(0,o.isVertical)(l)?x.height:x.width;if(-1!==i[0]&&-1!==i[1]){var b=e-i[0],y=t-i[1],w=0;switch(l){case c.Direction.Right:case c.Direction.Left:w=b/v*(u-d);break;case c.Direction.Down:case c.Direction.Up:w=y/v*(u-d);break;default:(0,o.assertUnreachable)(l)}if(g&&(w*=-1),Math.abs(w)>=p/2){for(var j=0;j<a.thumbRefs.length;j++){if(m[j]===u&&1===Math.sign(w)||m[j]===d&&-1===Math.sign(w))return;var k=m[j]+w;k>u?w=u-m[j]:k<d&&(w=d-m[j])}var N=m.slice(0);for(j=0;j<a.thumbRefs.length;j++)N=(0,o.replaceAt)(N,j,a.normalizeValue(m[j]+w,j));a.setState({draggedTrackPos:[e,t]}),h(N)}}else{var T=0;switch(l){case c.Direction.Right:T=(e-x.left)/v*(u-d)+d;break;case c.Direction.Left:T=(v-(e-x.left))/v*(u-d)+d;break;case c.Direction.Down:T=(t-x.top)/v*(u-d)+d;break;case c.Direction.Up:T=(v-(t-x.top))/v*(u-d)+d;break;default:(0,o.assertUnreachable)(l)}g&&(T=u+d-T),Math.abs(m[r]-T)>=p/2&&h((0,o.replaceAt)(m,r,a.normalizeValue(T,r)))}},a.normalizeValue=function(e,t){var n=a.props,r=n.min,i=n.max,s=n.step,c=n.allowOverlap,l=n.values;return(0,o.normalizeValue)(e,t,r,i,s,c,l)},a.onEnd=function(e){if(e.preventDefault(),document.removeEventListener("mousemove",a.schdOnMouseMove),document.removeEventListener("touchmove",a.schdOnTouchMove),document.removeEventListener("mouseup",a.schdOnEnd),document.removeEventListener("touchend",a.schdOnEnd),document.removeEventListener("touchcancel",a.schdOnEnd),-1===a.state.draggedThumbIndex&&-1===a.state.draggedTrackPos[0]&&-1===a.state.draggedTrackPos[1])return null;a.setState({draggedThumbIndex:-1,draggedTrackPos:[-1,-1]},(function(){a.fireOnFinalChange()}))},a.fireOnFinalChange=function(){a.setState({isChanged:!1});var e=a.props,t=e.onFinalChange,n=e.values;t&&t(n)},a.updateMarkRefs=function(e){if(!e.renderMark)return a.numOfMarks=void 0,void(a.markRefs=void 0);a.numOfMarks=(e.max-e.min)/a.props.step,a.markRefs=[];for(var t=0;t<a.numOfMarks+1;t++)a.markRefs[t]=s.createRef()},a.calculateMarkOffsets=function(){if(a.props.renderMark&&a.trackRef&&a.numOfMarks&&a.markRefs&&null!==a.trackRef.current){for(var e=window.getComputedStyle(a.trackRef.current),t=parseInt(e.width,10),n=parseInt(e.height,10),r=parseInt(e.paddingLeft,10),i=parseInt(e.paddingTop,10),s=[],o=0;o<a.numOfMarks+1;o++){var l=9999,d=9999;if(a.markRefs[o].current){var u=a.markRefs[o].current.getBoundingClientRect();l=u.height,d=u.width}a.props.direction===c.Direction.Left||a.props.direction===c.Direction.Right?s.push([Math.round(t/a.numOfMarks*o+r-d/2),-Math.round((l-n)/2)]):s.push([Math.round(n/a.numOfMarks*o+i-l/2),-Math.round((d-t)/2)])}a.setState({markOffsets:s})}},0===e.step)throw new Error('"step" property should be a positive number');return a.schdOnMouseMove=(0,o.schd)(a.onMouseMove),a.schdOnTouchMove=(0,o.schd)(a.onTouchMove),a.schdOnEnd=(0,o.schd)(a.onEnd),a.thumbRefs=e.values.map((function(){return s.createRef()})),a.updateMarkRefs(e),a}return e(a,t),a.prototype.componentDidMount=function(){var e=this,t=this.props,a=t.values,n=t.min,r=t.step;this.resizeObserver=window.ResizeObserver?new window.ResizeObserver(this.onResize):{observe:function(){return window.addEventListener("resize",e.onResize)},unobserve:function(){return window.removeEventListener("resize",e.onResize)}},document.addEventListener("touchstart",this.onMouseOrTouchStart,{passive:!1}),document.addEventListener("mousedown",this.onMouseOrTouchStart,{passive:!1}),!this.props.allowOverlap&&(0,o.checkInitialOverlap)(this.props.values),this.props.values.forEach((function(t){return(0,o.checkBoundaries)(t,e.props.min,e.props.max)})),this.resizeObserver.observe(this.trackRef.current),(0,o.translateThumbs)(this.getThumbs(),this.getOffsets(),this.props.rtl),this.calculateMarkOffsets(),a.forEach((function(e){(0,o.isStepDivisible)(n,e,r)}))},a.prototype.componentDidUpdate=function(e,t){var a=this.props,n=a.max,r=a.min,i=a.step,s=a.values,c=a.rtl;e.max===n&&e.min===r&&e.step===i||this.updateMarkRefs(this.props),(0,o.translateThumbs)(this.getThumbs(),this.getOffsets(),c),e.max===n&&e.min===r&&e.step===i&&t.markOffsets.length===this.state.markOffsets.length||(this.calculateMarkOffsets(),s.forEach((function(e){(0,o.isStepDivisible)(r,e,i)})))},a.prototype.componentWillUnmount=function(){document.removeEventListener("mousedown",this.onMouseOrTouchStart,{passive:!1}),document.removeEventListener("mousemove",this.schdOnMouseMove),document.removeEventListener("touchmove",this.schdOnTouchMove),document.removeEventListener("touchstart",this.onMouseOrTouchStart),document.removeEventListener("mouseup",this.schdOnEnd),document.removeEventListener("touchend",this.schdOnEnd),this.resizeObserver.unobserve(this.trackRef.current)},a.prototype.render=function(){var e=this,t=this.props,a=t.label,n=t.labelledBy,r=t.renderTrack,s=t.renderThumb,l=t.renderMark,d=void 0===l?function(){return null}:l,u=t.values,h=t.min,m=t.max,p=t.allowOverlap,g=t.disabled,f=this.state,x=f.draggedThumbIndex,v=f.thumbZIndexes,b=f.markOffsets;return r({props:{style:{transform:"scale(1)",cursor:x>-1?"grabbing":this.props.draggableTrack?(0,o.isVertical)(this.props.direction)?"ns-resize":"ew-resize":1!==u.length||g?"inherit":"pointer"},onMouseDown:g?o.voidFn:this.onMouseDownTrack,onTouchStart:g?o.voidFn:this.onTouchStartTrack,ref:this.trackRef},isDragged:this.state.draggedThumbIndex>-1,disabled:g,children:i(i([],b.map((function(t,a,n){return d({props:{style:e.props.direction===c.Direction.Left||e.props.direction===c.Direction.Right?{position:"absolute",left:"".concat(t[0],"px"),marginTop:"".concat(t[1],"px")}:{position:"absolute",top:"".concat(t[0],"px"),marginLeft:"".concat(t[1],"px")},key:"mark".concat(a),ref:e.markRefs[a]},index:a})})),!0),u.map((function(t,r){var i=e.state.draggedThumbIndex===r;return s({index:r,value:t,isDragged:i,props:{style:{position:"absolute",zIndex:v[r],cursor:g?"inherit":i?"grabbing":"grab",userSelect:"none",touchAction:"none",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none"},key:r,tabIndex:g?void 0:0,"aria-valuemax":p?m:u[r+1]||m,"aria-valuemin":p?h:u[r-1]||h,"aria-valuenow":t,draggable:!1,ref:e.thumbRefs[r],"aria-label":a,"aria-labelledby":n,role:"slider",onKeyDown:g?o.voidFn:e.onKeyDown,onKeyUp:g?o.voidFn:e.onKeyUp}})})),!0)})},a.defaultProps={label:"Accessibility label",labelledBy:null,step:1,direction:c.Direction.Right,rtl:!1,disabled:!1,allowOverlap:!1,draggableTrack:!1,min:0,max:100},a}(s.Component);return A.default=u,A}function U(){return I||(I=1,function(e){var t=P&&P.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0}),e.checkValuesAgainstBoundaries=e.relativeValue=e.useThumbOverlap=e.Direction=e.getTrackBackground=e.Range=void 0;var a=t(V());e.Range=a.default;var n=_();Object.defineProperty(e,"getTrackBackground",{enumerable:!0,get:function(){return n.getTrackBackground}}),Object.defineProperty(e,"useThumbOverlap",{enumerable:!0,get:function(){return n.useThumbOverlap}}),Object.defineProperty(e,"relativeValue",{enumerable:!0,get:function(){return n.relativeValue}}),Object.defineProperty(e,"checkValuesAgainstBoundaries",{enumerable:!0,get:function(){return n.checkValuesAgainstBoundaries}});var r=B();Object.defineProperty(e,"Direction",{enumerable:!0,get:function(){return r.Direction}})}(P)),P}var $=U();const K=1e7,H="newest",X="price_low_to_high",Y="price_high_to_low",W={[H]:"Mới nhất",[X]:"Giá: Thấp đến cao",[Y]:"Giá: Cao đến thấp"};a.createContext({prefersReducedMotion:!1,localSearchTerm:"",setLocalSearchTerm:()=>{}});const q=e=>{const t=Math.round(e);return new Intl.NumberFormat("vi-VN").format(t)},G=a.memo((({category:e,selectedCategory:t,setSelectedCategory:a})=>{const n=R();return i.jsxs(s.div,{className:"flex justify-between items-center py-2 border-b border-gray-100 cursor-pointer hover:text-dexin-primary",onClick:()=>{a(e.id)},whileHover:n?{}:{x:5},whileTap:n?{}:{scale:.98},initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{duration:.2},children:[i.jsx("span",{className:t===e.id?"text-dexin-primary font-medium":"",children:e.name}),i.jsx(s.span,{animate:t!==e.id||n?{}:{x:[0,3,0]},transition:{duration:.3},children:e.icon})]})})),Z=a.memo((({priceRange:e,localPriceRange:t,setLocalPriceRange:n})=>{const r=K;return a.useEffect((()=>{e&&2===e.length&&n(e)}),[e,n]),i.jsxs(s.div,{className:"mt-4 px-4 py-2",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:[i.jsx("div",{className:"h-10",children:i.jsx($.Range,{step:1e4,min:0,max:r,values:t,onChange:e=>{n(e)},onFinalChange:e=>{n(e)},renderTrack:({props:e,children:a})=>i.jsxs("div",{className:"w-full h-2 rounded-full bg-gray-200 flex",style:{...e.style},ref:e.ref,children:[i.jsx("div",{className:"h-2 rounded-full absolute",style:{background:$.getTrackBackground({values:t,colors:["#F3F4F6","#FE7CAB","#F3F4F6"],min:0,max:r}),width:"100%",height:"100%"}}),a]}),renderThumb:({props:e,isDragged:t})=>i.jsx("div",{className:"w-5 h-5 bg-dexin-light rounded-full focus:outline-none shadow-md flex items-center justify-center cursor-grab active:cursor-grabbing",style:{...e.style,boxShadow:t?"0 0 0 5px rgba(255, 107, 107, 0.2)":void 0,zIndex:10},tabIndex:e.tabIndex,"aria-valuemax":e["aria-valuemax"],"aria-valuemin":e["aria-valuemin"],"aria-valuenow":e["aria-valuenow"],draggable:e.draggable,ref:e.ref,role:e.role,"aria-label":e["aria-label"],"aria-labelledby":e["aria-labelledby"],onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onKeyPress:e.onKeyPress,onClick:e.onClick,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onTouchStart:e.onTouchStart,onTouchEnd:e.onTouchEnd},`thumb-${e["aria-valuenow"]}`)})}),i.jsxs("div",{className:"flex justify-between mt-2",children:[i.jsx("span",{className:"text-sm",children:q(t[0])}),i.jsx("span",{className:"text-sm",children:q(t[1])})]})]})})),Q=a.memo((({group:e})=>{const t=R();return i.jsx(s.button,{className:"px-3 py-1 bg-gray-100 rounded-full text-sm hover:bg-gray-200",whileHover:t?{}:{scale:1.05,backgroundColor:"#f3f4f6"},whileTap:t?{}:{scale:.95},initial:{opacity:0,y:5},animate:{opacity:1,y:0},transition:{duration:.2},children:e.name})})),J=a.memo((({localSearchTerm:e,setLocalSearchTerm:t})=>{const n=R(),r=a.useCallback((e=>{t(e.target.value)}),[t]);return i.jsx(s.div,{className:"mb-6",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:i.jsxs("div",{className:"relative",children:[i.jsx(s.input,{type:"text",placeholder:"Tìm kiếm...",className:"w-full pl-10 pr-4 py-2 bg-pink-100 rounded-full focus:outline-none focus:ring-2 focus:ring-dexin-light focus:border-transparent",value:e,onChange:r,whileFocus:n?{}:{scale:1.01},transition:{duration:.2}}),i.jsx(s.div,{className:"absolute left-3 top-2.5 text-gray-400",animate:n?{}:{rotate:e?[0,-10,10,-5,5,0]:0},transition:{duration:.5,delay:.2},children:i.jsx(d,{className:"h-5 w-5"})})]})})})),ee=a.memo((()=>{const e=R();return i.jsxs(s.div,{className:"flex justify-between items-center mb-4",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.1},children:[i.jsx(s.h2,{className:"text-xl font-bold",initial:{x:-10},animate:{x:0},transition:{duration:.3,delay:.2},children:"Bộ Lọc"}),i.jsx(s.button,{className:"text-gray-400",whileHover:e?{}:{rotate:180},transition:{duration:.3},children:i.jsx(k,{className:"h-5 w-5"})})]})})),te=a.memo((({sortOption:e,setSortOption:t})=>{const n=R(),[r,l]=a.useState(!1),d=a.useCallback((e=>{t(e),l(!1)}),[t]);return a.useEffect((()=>{const e=e=>{r&&!e.target.closest(".sort-dropdown")&&l(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[r]),i.jsxs(s.div,{className:"mb-6 relative sort-dropdown",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.2},children:[i.jsx("div",{className:"flex flex-col space-y-2 w-full",children:i.jsxs("div",{className:"flex items-center justify-between w-full",children:[i.jsx("span",{className:"text-gray-600 whitespace-nowrap",children:"Lọc theo:"}),i.jsxs(s.button,{className:"flex items-center justify-between text-dexin-light min-w-[160px] px-3 py-1.5 border border-transparent hover:border-gray-200 rounded-md",whileHover:n?{}:{scale:1.02},whileTap:n?{}:{scale:.98},onClick:()=>l(!r),"aria-expanded":r,"aria-haspopup":"true",children:[i.jsx("span",{className:"truncate",children:W[e]}),i.jsx(s.div,{animate:n?{}:{y:r?0:[0,2,0],rotate:r?180:0},transition:{y:{repeat:r?0:1/0,repeatDelay:1.5,duration:.5},rotate:{duration:.3}},className:"flex-shrink-0",children:i.jsx(c,{className:"h-4 w-4 ml-1"})})]})]})}),i.jsx(o,{children:r&&i.jsx(s.div,{className:"absolute z-10 mt-1 right-0 w-64 bg-white rounded-md shadow-lg overflow-hidden ring-1 ring-black ring-opacity-5 focus:outline-none",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2},role:"menu","aria-orientation":"vertical","aria-labelledby":"sort-menu",children:Object.entries(W).map((([t,a])=>i.jsx(s.button,{className:"w-full px-3 py-2 text-left hover:bg-gray-50 "+(e===t?"text-dexin-primary":"text-gray-700"),onClick:()=>d(t),whileHover:n?{}:{x:2},whileTap:n?{}:{scale:.98},children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("span",{children:a}),e===t&&i.jsx(j,{className:"h-4 w-4"})]})},`sort-option-${t}`)))})})]})})),ae=({searchTerm:e,setSearchTerm:t,activeCategory:n,setActiveCategory:r,priceRange:d,setPriceRange:u,sortOption:h,setSortOption:m,applyFilters:p})=>{const[g,f]=a.useState(""),[x,v]=a.useState("all"),[b,y]=a.useState([0,K]),[j,k]=a.useState(H),[N,T]=a.useState({price:!0,group:!0});a.useEffect((()=>{f(e),v(n),y(d),k(h)}),[e,n,d,h]);const M=a.useMemo((()=>[{id:"all",name:"Tất cả",icon:">"},{id:"ban",name:"Bàn",icon:">"},{id:"ghe",name:"Ghế",icon:">"},{id:"tu",name:"Tủ",icon:">"},{id:"den",name:"Đèn",icon:">"},{id:"giuong",name:"Giường",icon:">"},{id:"ke",name:"Kệ",icon:">"}]),[]),C=a.useMemo((()=>[{id:"dongho",name:"Đồng hồ"},{id:"tham",name:"Thảm"},{id:"guong",name:"Gương"},{id:"rem",name:"Rèm"},{id:"giaydan",name:"Giấy dán tường"},{id:"khac",name:"Khác"}]),[]),O=a.useCallback((e=>{T((t=>({...t,[e]:!t[e]})))}),[]),S=a.useCallback((()=>{t(g),r(x),u(b),m(j),p()}),[g,x,b,j,t,r,u,m,p]),D=R(),E=a.useMemo((()=>i.jsx(J,{localSearchTerm:g,setLocalSearchTerm:f})),[g,f]),L=a.useMemo((()=>i.jsx(te,{sortOption:j,setSortOption:k})),[j,k]),I=a.useMemo((()=>i.jsx(ee,{})),[]),P=a.useMemo((()=>i.jsx(s.div,{className:"mb-6",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.25},children:M.map(((e,t)=>i.jsx(s.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1+.05*t},children:i.jsx(G,{category:e,selectedCategory:x,setSelectedCategory:v})},`category-${e.id}`)))})),[M,x,v]),A=a.useMemo((()=>i.jsxs(s.div,{className:"mb-6",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.3},children:[i.jsxs(s.div,{className:"flex justify-between items-center mb-2 cursor-pointer",onClick:()=>O("price"),whileHover:D?{}:{x:3},whileTap:D?{}:{scale:.98},children:[i.jsx("h3",{className:"font-bold",children:"Khoảng giá (VND)"}),i.jsx(o,{mode:"wait",children:N.price?i.jsx(s.div,{initial:{opacity:0,rotate:180},animate:{opacity:1,rotate:0},exit:{opacity:0,rotate:180},transition:{duration:.2},children:i.jsx(w,{className:"h-5 w-5"})},"up"):i.jsx(s.div,{initial:{opacity:0,rotate:-180},animate:{opacity:1,rotate:0},exit:{opacity:0,rotate:-180},transition:{duration:.2},children:i.jsx(c,{className:"h-5 w-5"})},"down")})]}),i.jsx(o,{children:N.price&&i.jsx(Z,{priceRange:d,localPriceRange:b,setLocalPriceRange:y})})]})),[N.price,b,d,D,y,O]),F=a.useMemo((()=>i.jsxs(s.div,{className:"mb-6",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.4},children:[i.jsxs(s.div,{className:"flex justify-between items-center mb-2 cursor-pointer",onClick:()=>O("group"),whileHover:D?{}:{x:3},whileTap:D?{}:{scale:.98},children:[i.jsx("h3",{className:"font-bold",children:"Nhóm"}),i.jsx(o,{mode:"wait",children:N.group?i.jsx(s.div,{initial:{opacity:0,rotate:180},animate:{opacity:1,rotate:0},exit:{opacity:0,rotate:180},transition:{duration:.2},children:i.jsx(w,{className:"h-5 w-5"})},"up"):i.jsx(s.div,{initial:{opacity:0,rotate:-180},animate:{opacity:1,rotate:0},exit:{opacity:0,rotate:-180},transition:{duration:.2},children:i.jsx(c,{className:"h-5 w-5"})},"down")})]}),i.jsx(o,{children:N.group&&i.jsx(s.div,{className:"mt-4 flex flex-wrap gap-2",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:C.map(((e,t)=>i.jsx(Q,{group:e},`group-${e.id}`)))})})]})),[N.group,C,D,O]),z=a.useMemo((()=>i.jsx(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.5},className:"w-full",children:i.jsx(l,{variant:"click",size:"md",rounded:!0,onClick:S,className:"w-full shadow-md",children:"Áp Dụng Bộ Lọc"})})),[S]);return i.jsxs(s.div,{className:"bg-white rounded-lg shadow-md p-5 w-full max-w-xl",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.3},layout:!0,children:[E,L,i.jsx(s.div,{className:"border-t border-gray-200 my-5",initial:{scaleX:0},animate:{scaleX:1},transition:{duration:.5,delay:.2}}),I,P,A,F,z]})},ne=e=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(e),re=a.memo((({rating:e,reviews:t})=>i.jsx("div",{className:"flex items-center mt-2",children:i.jsxs("div",{className:"flex items-center",children:[[...Array(5)].map(((t,a)=>i.jsx("svg",{className:"w-4 h-4 "+(a<Math.floor(e)?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",children:i.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},`star-rating-${a}`))),i.jsxs("span",{className:"ml-1 text-xs text-gray-600",children:["(",t,")"]})]})}))),ie=a.memo((()=>i.jsx(x,{className:"h-5 w-5"}))),se=a.memo((({isFilled:e})=>e?i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"transition-transform duration-300 transform scale-110",children:i.jsx("path",{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"})}):i.jsx(p,{className:"h-5 w-5 transition-transform duration-300"}))),oe=a.memo((()=>i.jsx(M,{className:"h-5 w-5"}))),ce=a.memo((({product:e,onQuickView:t,handleAddToCart:n})=>{const{addToWishlist:r,removeFromWishlist:s,isInWishlist:o}=f(),c=a.useMemo((()=>ne(e.price)),[e.price]),[l,d]=a.useState(!1),[u,p]=a.useState(!1),g=o(e.id),x=a.useCallback((()=>{d(!0)}),[]),v=a.useCallback((()=>{p(!0)}),[]),b=a.useCallback((()=>{p(!1)}),[]),y=a.useCallback((t=>{t.stopPropagation(),t.preventDefault(),g?(s(e.id),h.info("Đã xóa khỏi mục yêu thích",{icon:()=>i.jsx("span",{style:{fontSize:"1.5rem",marginRight:"10px"},children:"💔"}),className:"toast-message"})):(r(e),h.success("Đã thêm vào mục yêu thích",{icon:()=>i.jsx("span",{style:{fontSize:"1.5rem",marginRight:"10px"},children:"❤️"}),className:"toast-message"}))}),[e,g,r,s]),w=a.useCallback((a=>{a.stopPropagation(),a.preventDefault(),t(e)}),[e,t]);return i.jsx(m,{to:`/product/${e.id}`,className:"block",children:i.jsxs("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md will-change-transform will-change-opacity group",onMouseEnter:v,onMouseLeave:b,children:[i.jsxs("div",{className:"relative",children:[!l&&i.jsx("div",{className:"absolute inset-0 bg-gray-50 animate-pulse flex items-center justify-center",children:i.jsx("svg",{className:"w-10 h-10 text-gray-200",fill:"currentColor",viewBox:"0 0 20 20",children:i.jsx("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})})}),i.jsx("div",{className:"overflow-hidden h-64 flex items-center justify-center bg-gray-50",children:i.jsx("img",{src:e.image,alt:e.name,className:`w-auto h-auto max-h-60 max-w-[90%] object-contain content-visibility-auto transition-all duration-300 ease-in-out ${l?"opacity-100":"opacity-0"} ${u?"scale-105":"scale-100"}`,loading:"lazy",decoding:"async",onLoad:x})}),l&&i.jsx("button",{className:"absolute top-2 right-2 p-2 bg-white rounded-full hover:bg-gray-100 transition-all duration-200 opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100","aria-label":"Quick view",onClick:w,children:i.jsx(ie,{})})]}),i.jsx("div",{className:"w-full bg-dexin-light text-white py-3 text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300",onClick:t=>n(t,e),children:i.jsxs("button",{className:"flex items-center justify-center w-full",children:[i.jsx(oe,{className:"mr-2"}),i.jsx("span",{children:"Thêm vào giỏ hàng"})]})}),i.jsxs("div",{className:"p-4",children:[i.jsx("h3",{className:"text-base font-medium text-gray-800 line-clamp-1",children:e.name}),i.jsx("div",{className:"mt-2 text-base font-bold text-dexin-primary",children:c}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx(re,{rating:e.rating,reviews:e.reviews}),i.jsx("button",{className:`p-1.5 ${g?"text-dexin-primary":"text-gray-400 hover:text-dexin-primary"} transition-colors duration-200 relative group`,"aria-label":g?"Remove from wishlist":"Add to wishlist",onClick:y,children:i.jsx("div",{className:"transition-all duration-300 "+(g?"animate-heartBeat":""),children:i.jsx(se,{isFilled:g})})})]})]})]})})})),le=({filteredProducts:e,setSearchTerm:t,setActiveCategory:n,currentPage:r,setCurrentPage:s,productsPerPage:o})=>{const c=R(),{addToCart:l}=u(),[d,f]=a.useState(!1),[x,v]=a.useState(null),b=a.useCallback(((e,t)=>{e&&(e.stopPropagation(),e.preventDefault());const a=O(t.id);a&&a.variants&&a.variants.length>0?(v(a),f(!0)):(l(t),h.success(`Đã thêm "${t.name}" vào giỏ hàng`,{icon:()=>i.jsx("span",{style:{fontSize:"1.2rem",marginRight:"10px"},children:"🛒"}),className:"toast-message",toastId:`cart-${t.id}`,autoClose:2e3}))}),[l]),y=a.useCallback((e=>{l(e,e.quantity);const t=e.variantName?`${e.name} (${e.variantName})`:e.name;h.success(`Đã thêm "${t}" vào giỏ hàng`,{icon:()=>i.jsx("span",{style:{fontSize:"1.2rem",marginRight:"10px"},children:"🛒"}),className:"toast-message",toastId:`variant-cart-${e.id}-${e.variantId}`,autoClose:2e3})}),[l]),w=a.useCallback((()=>{f(!1),setTimeout((()=>{v(null)}),300)}),[]);a.useEffect((()=>(Object.entries({heart:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z",shoppingCart:"M9 20a1 1 0 1 0 0 2 1 1 0 0 0 0-2zm0 0h8a1 1 0 1 0 0 2H9M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6",eye:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z M12 9a3 3 0 1 0 0 6 3 3 0 0 0 0-6z"}).forEach((([e,t])=>{const a=document.createElementNS("http://www.w3.org/2000/svg","svg");a.setAttribute("width","0"),a.setAttribute("height","0"),a.setAttribute("class","absolute invisible");const n=document.createElementNS("http://www.w3.org/2000/svg","path");n.setAttribute("d",t),a.appendChild(n),document.body.appendChild(a)})),(()=>{const e=()=>{requestAnimationFrame((()=>{document.body.classList.add("scrolling"),clearTimeout(window.scrollTimer),window.scrollTimer=setTimeout((()=>{document.body.classList.remove("scrolling")}),150)}))};return window.addEventListener("scroll",e,{passive:!0}),()=>{window.removeEventListener("scroll",e),clearTimeout(window.scrollTimer)}})())),[]);const j=a.useMemo((()=>{const t=r*o,a=t-o;return e.slice(a,t)}),[e,r,o]),k=a.useMemo((()=>Math.ceil(e.length/o)),[e.length,o]),S=a.useCallback((e=>{document.body.classList.add("page-transitioning"),setTimeout((()=>{var t;s(e),window.scrollTo({top:(null==(t=document.getElementById("products"))?void 0:t.offsetTop)-100||0,behavior:c?"auto":"smooth"}),setTimeout((()=>{document.body.classList.remove("page-transitioning")}),100)}),0)}),[s,c]),D=a.memo((()=>i.jsxs("div",{className:"flex justify-between items-center mb-6",children:[i.jsx("h2",{className:"text-xl font-semibold",children:"Sản phẩm nổi bật"}),i.jsx(m,{to:"/wishlist",children:i.jsxs("button",{className:"text-dexin-primary border border-dexin-primary rounded-full px-4 py-2 hover:text-dexin-light transition-colors duration-200 flex items-center",children:[i.jsx(p,{className:"h-5 w-5 mr-1"}),i.jsx("span",{children:"Mục yêu thích"})]})})]},"favorite-banner"))),E=a.memo((({onClearFilter:e})=>i.jsxs("div",{className:"text-center py-12 bg-white rounded-lg shadow-sm",children:[i.jsx("p",{className:"text-gray-500 text-lg",children:"Không tìm thấy sản phẩm phù hợp"}),i.jsx("button",{className:"mt-4 px-4 py-2 bg-dexin-light text-white rounded-md hover:bg-dexin-primary transition-colors duration-200",onClick:e,children:"Xóa bộ lọc"})]},"empty-state"))),L=a.memo((({pageNumber:e,isActive:t,onClick:a,children:n})=>i.jsx("button",{onClick:()=>a(e),className:"px-3 py-1 rounded-md transition-colors duration-150 "+(t?"bg-dexin-primary text-white border border-dexin-primary":"bg-white border border-gray-300 text-gray-600 hover:bg-dexin-light hover:text-white hover:border-dexin-light"),children:n},`page-${e}`))),I=a.useCallback((()=>{n("all"),t("")}),[n,t]),P=a.useMemo((()=>{if(k<=1)return[];const e=[];e.push(i.jsx(L,{pageNumber:1,isActive:1===r,onClick:S,children:"1"},"page-1")),r>3&&e.push(i.jsx("span",{className:"px-2 text-gray-600",children:"..."},"ellipsis-start"));for(let t=Math.max(2,r-1);t<=Math.min(k-1,r+1);t++)1!==t&&t!==k&&e.push(i.jsx(L,{pageNumber:t,isActive:r===t,onClick:S,children:t},`page-${t}`));return r<k-2&&e.push(i.jsx("span",{className:"px-2 text-gray-600",children:"..."},"ellipsis-end")),k>1&&e.push(i.jsx(L,{pageNumber:k,isActive:r===k,onClick:S,children:k},`page-${k}`)),e}),[k,r,S]),[A,F]=a.useState(null),[z,B]=a.useState(!1),_=a.useCallback((e=>{const t=O(e.id);F(t),B(!0)}),[]),V=a.useCallback((()=>{B(!1),setTimeout((()=>{F(null)}),300)}),[]),U=a.useCallback((e=>{const t=O(e.id);t&&t.variants&&t.variants.length>0?(V(),v(t),f(!0)):(l(e),h.success(`Đã thêm "${e.name}" vào giỏ hàng`,{icon:()=>i.jsx("span",{style:{fontSize:"1.2rem",marginRight:"10px"},children:"🛒"}),className:"toast-message",toastId:`quickview-cart-${e.id}`,autoClose:2e3}),V())}),[l,V]);return i.jsxs("div",{className:"flex-1",children:[i.jsx(D,{}),i.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:j.map(((e,t)=>{const a=c?0:1===r?Math.min(.03*t,.2):Math.min(.02*t,.1);return i.jsx("div",{className:`opacity-0 animate-scaleIn ${c?"animation-duration-200":"animation-duration-300"} animation-fill-forwards transform-gpu backface-hidden`,"data-delay":a,onAnimationStart:e=>{e.currentTarget.style.animationDelay=`${e.currentTarget.dataset.delay}s`},children:i.jsx(ce,{product:e,onQuickView:_,handleAddToCart:b})},e.id)}))}),0===e.length&&i.jsx(E,{onClearFilter:I}),e.length>0&&k>1&&i.jsx("div",{className:"flex justify-center mt-12",children:i.jsxs("nav",{className:"flex items-center space-x-2",children:[r>1&&i.jsx("button",{onClick:()=>S(r-1),className:"px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-600 hover:bg-dexin-light hover:text-white hover:border-dexin-light transition-colors duration-150",children:"«"}),P,r<k&&i.jsx("button",{onClick:()=>S(r+1),className:"px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-600 hover:bg-dexin-light hover:text-white hover:border-dexin-light transition-colors duration-150",children:"»"})]})}),i.jsx(N,{appear:!0,show:z,children:i.jsx(T,{as:"div",className:"fixed inset-0 z-50 overflow-y-auto",onClose:V,children:i.jsxs("div",{className:"min-h-screen px-4 flex items-center justify-center",children:[i.jsx(N.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:i.jsx("div",{className:"fixed inset-0 bg-black/30","aria-hidden":"true"})}),i.jsx(N.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:i.jsxs(T.Panel,{className:"relative w-full max-w-4xl mx-auto bg-white rounded-2xl shadow-xl p-8",children:[i.jsx("button",{onClick:V,className:"absolute top-4 right-4 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:i.jsx(g,{className:"w-6 h-6 text-gray-600"})}),A&&i.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[i.jsx("div",{className:"flex items-center justify-center",children:i.jsx("img",{src:A.image,alt:A.name,className:"max-h-96 object-contain"})}),i.jsxs("div",{children:[i.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:A.name}),i.jsxs("div",{className:"flex items-center mb-4",children:[i.jsx("div",{className:"flex",children:[...Array(5)].map(((e,t)=>i.jsx("svg",{className:"w-5 h-5 "+(t<Math.floor(A.rating)?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",children:i.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},`quick-view-star-${t}`)))}),i.jsxs("span",{className:"ml-2 text-sm text-gray-600",children:["(",A.reviews,")"]})]}),i.jsx("div",{className:"text-3xl font-bold text-dexin-primary mb-4",children:ne(A.price)}),i.jsx("p",{className:"text-gray-600 mb-6",children:A.description}),i.jsxs("div",{className:"flex space-x-4",children:[i.jsx(m,{to:`/product/${A.id}`,className:"flex-1 bg-dexin-light text-white py-3 rounded-lg text-center hover:bg-pink-600 transition-colors",children:"Xem chi tiết"}),i.jsx("button",{onClick:()=>U(A),className:"flex-1 bg-white border border-dexin-light text-dexin-light py-3 rounded-lg hover:bg-gray-50 transition-colors",children:i.jsxs("div",{className:"flex items-center justify-center",children:[i.jsx(M,{className:"h-5 w-5 mr-2"}),i.jsx("span",{children:"Thêm vào giỏ hàng"})]})})]})]})]})]})})]})})}),i.jsx(C,{isOpen:d,onClose:w,product:x,onAddToCart:y})]})},de=()=>{const e=(()=>{const[e,t]=a.useState(!1);return a.useEffect((()=>{const e=()=>{const e=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),a=navigator.hardwareConcurrency||4;t(e||a<4)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[]),e})(),t=a.useMemo((()=>S()),[]),[n,r]=a.useState(t),[c,l]=a.useState("all"),[d,u]=a.useState(""),[h,m]=a.useState([0,K]),[p,g]=a.useState(H),[f,x]=a.useState(!1),[w,j]=a.useState(1),[k]=a.useState(9);a.useEffect((()=>{j(1),r((()=>{let e=t;switch("all"!==c&&(e=e.filter((e=>e.category===c))),d&&(e=e.filter((e=>e.name.toLowerCase().includes(d.toLowerCase())))),e=e.filter((e=>e.price>=h[0]&&e.price<=h[1])),p){case X:e=[...e].sort(((e,t)=>e.price-t.price));break;case Y:e=[...e].sort(((e,t)=>t.price-e.price));break;default:e=[...e].sort(((e,t)=>t.id-e.id))}return e})())}),[c,d,h,p,t]);const N=a.useCallback((()=>{f&&x(!1)}),[c,d,h,p,f]),T=a.useCallback((()=>{x((e=>!e))}),[]),M=v.memo((({isLowPerformance:e})=>i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"relative bg-dexin-bg min-h-[400px] md:min-h-[500px] overflow-hidden",children:i.jsxs("div",{className:"absolute inset-0 flex flex-col md:flex-row max-w-fit mx-auto px-4 lg:px-8",children:[i.jsxs(s.div,{className:"w-full md:w-1/2 p-6 md:p-12 lg:p-16 flex flex-col justify-center",initial:{opacity:0,x:e?-10:-30},animate:{opacity:1,x:0},transition:{duration:e?.3:.5,ease:"easeOut"},children:[i.jsx(s.p,{className:"text-dexin-primary text-sm md:text-base mb-2",initial:{opacity:0},animate:{opacity:1},transition:{delay:e?.1:.2},children:"Rất vui được đồng hành cùng bạn!"}),i.jsxs(s.h1,{className:"text-2xl md:text-4xl lg:text-5xl font-bold text-dexin-primary leading-tight md:leading-tight lg:leading-tight",initial:{opacity:0},animate:{opacity:1},transition:{delay:e?.2:.3},children:["Dạo một vòng, ",i.jsx("br",{}),i.jsx("span",{className:"whitespace-nowrap mt-3 md:mt-2 inline-block",children:"sắm sửa những điều bạn yêu!"})]})]}),i.jsx(s.div,{className:"w-full md:w-1/2 flex items-center justify-center mt-4 md:mt-0",initial:{opacity:0,scale:e?.95:.9},animate:{opacity:1,scale:1},transition:{duration:e?.3:.5,delay:e?.1:.2},children:i.jsx("img",{src:"/images/jogging.png",alt:"Shopping Hero",className:"w-auto h-auto max-w-full max-h-[300px] md:max-h-full object-contain content-visibility-auto",loading:"lazy",fetchPriority:"high",decoding:"async"})})]})}),i.jsx("div",{className:"w-full ",children:i.jsx("img",{src:"/images/Mask group.png",alt:"DEXIN Thiết kế nội thất",className:"w-full h-auto",loading:"lazy",decoding:"async"})})]}))),C=a.useMemo((()=>i.jsx(ae,{searchTerm:d,setSearchTerm:u,activeCategory:c,setActiveCategory:l,priceRange:h,setPriceRange:m,sortOption:p,setSortOption:g,applyFilters:N})),[d,u,c,l,h,m,p,g,N]),O=a.useMemo((()=>i.jsx(le,{filteredProducts:n,setSearchTerm:u,setActiveCategory:l,currentPage:w,setCurrentPage:j,productsPerPage:k})),[n,w,k,u,l,j]);return a.useEffect((()=>(e||["/images/jogging.png","/images/Mask group.png"].forEach((e=>{(new Image).src=e})),document.body.classList.add("store-page"),()=>{document.body.classList.remove("store-page")})),[e]),i.jsxs(i.Fragment,{children:[i.jsx(b,{title:y.store.title,description:y.store.description,keywords:y.store.keywords,url:y.store.url}),i.jsxs("div",{className:"min-h-screen bg-gray-50",children:[i.jsx(M,{isLowPerformance:e}),i.jsx("div",{className:"container mx-auto px-4 py-4 max-w-7xl",id:"products",children:i.jsxs("div",{className:"flex flex-col md:flex-row gap-6",children:[i.jsx("div",{className:"hidden md:block w-full md:w-1/4 lg:w-1/5",children:C}),a.useMemo((()=>{const t=a.memo((()=>i.jsx("div",{className:"md:hidden mb-4",children:i.jsxs(s.button,{onClick:T,className:"flex items-center space-x-2 bg-white px-4 py-2 rounded-full border border-gray-300",whileHover:e?{backgroundColor:"#f9fafb"}:{scale:1.05,backgroundColor:"#f9fafb"},whileTap:e?{backgroundColor:"#f3f4f6"}:{scale:.95},initial:{opacity:0,y:e?-5:-10},animate:{opacity:1,y:0},transition:{duration:e?.2:.3},children:[i.jsx(s.svg,{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",animate:e?{}:{rotate:[0,15,0]},transition:e?{}:{duration:1,repeat:1/0,repeatDelay:3},children:i.jsx("polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"})}),i.jsx("span",{children:"Bộ lọc"})]})})));return i.jsx(t,{})}),[T,e]),i.jsx(o,{children:f&&i.jsx(s.div,{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:e?.1:.2},children:i.jsxs(s.div,{className:"bg-white rounded-lg w-full max-w-sm max-h-[90vh] overflow-y-auto",initial:{scale:e?.98:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:e?.98:.95,opacity:0},transition:{duration:e?.1:.2},children:[i.jsxs("div",{className:"flex justify-between items-center p-4 border-b",children:[i.jsx("h2",{className:"text-lg font-bold",children:"Bộ lọc"}),i.jsx("button",{onClick:T,children:i.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[i.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),i.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),i.jsx("div",{className:"p-4",children:C})]})})}),i.jsx("div",{className:"w-full md:w-3/4 lg:w-4/5",children:O})]})})]})]})};export{de as default};
