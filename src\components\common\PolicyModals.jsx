import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faShieldAlt, faFileContract, faCheck } from '@fortawesome/free-solid-svg-icons';

export const PrivacyPolicyModal = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden bg-black bg-opacity-60 flex items-center justify-center p-2 sm:p-4">
      <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] sm:max-h-[85vh] md:max-h-[80vh] overflow-hidden my-2 sm:my-4 animate-fadeIn">
        <div className="sticky top-0 bg-white border-b border-gray-200 p-3 sm:p-4 flex justify-between items-center z-10">
          <div className="flex items-center">
            <div className="bg-pink-100 rounded-full p-2 mr-3">
              <FontAwesomeIcon icon={faShieldAlt} className="text-dexin-primary h-4 w-4" />
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-gray-800">Chính Sách Bảo Mật</h2>
          </div>
          <button 
            onClick={onClose}
            className="bg-gray-100 hover:bg-gray-200 rounded-full p-2 transition-colors"
          >
            <FontAwesomeIcon icon={faTimes} className="text-gray-600 h-4 w-4" />
          </button>
        </div>

        <div className="overflow-y-auto p-3 sm:p-5 space-y-4 sm:space-y-5" style={{maxHeight: "calc(90vh - 120px)"}}>
          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">1. Thông tin chúng tôi thu thập</h3>
            <p className="text-gray-700 text-sm">
              Chúng tôi thu thập các thông tin sau đây để cung cấp và cải thiện dịch vụ của mình:
            </p>
            <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
              <li><strong>Thông tin cá nhân:</strong> Tên, địa chỉ email, số điện thoại, và địa chỉ của bạn.</li>
              <li><strong>Thông tin đăng nhập:</strong> Dữ liệu đăng nhập, chi tiết thiết bị, địa chỉ IP.</li>
              <li><strong>Nội dung người dùng:</strong> Hình ảnh, bình luận, và các nội dung khác bạn chia sẻ.</li>
              <li><strong>Dữ liệu sử dụng:</strong> Cách bạn tương tác với dịch vụ của chúng tôi.</li>
            </ul>
          </section>

          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">2. Cách chúng tôi sử dụng thông tin</h3>
            <p className="text-gray-700 text-sm">
              Chúng tôi sử dụng thông tin thu thập được để:
            </p>
            <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
              <li>Cung cấp, duy trì và cải thiện dịch vụ của chúng tôi</li>
              <li>Cá nhân hóa trải nghiệm của bạn</li>
              <li>Giao tiếp với bạn, bao gồm hỗ trợ khách hàng</li>
              <li>Gửi thông tin cập nhật, thông báo và tin tức marketing</li>
              <li>Đảm bảo an toàn và bảo mật</li>
              <li>Phát hiện, ngăn chặn và giải quyết các vấn đề kỹ thuật</li>
            </ul>
          </section>

          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">3. Bảo mật thông tin</h3>
            <p className="text-gray-700 text-sm">
              Chúng tôi triển khai các biện pháp bảo mật phù hợp để bảo vệ thông tin cá nhân của bạn. 
              Chúng tôi sử dụng các phương pháp mã hóa, tường lửa và các biện pháp kỹ thuật khác để 
              bảo vệ dữ liệu của bạn. Tuy nhiên, không có phương thức truyền dẫn qua internet hoặc 
              phương pháp lưu trữ điện tử nào là an toàn 100%.
            </p>
          </section>

          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">4. Chia sẻ thông tin</h3>
            <p className="text-gray-700 text-sm">
              Chúng tôi không bán thông tin cá nhân của bạn cho bên thứ ba. Chúng tôi có thể chia sẻ 
              thông tin trong các trường hợp sau:
            </p>
            <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
              <li>Với sự đồng ý của bạn</li>
              <li>Với đối tác dịch vụ để cung cấp dịch vụ cho bạn</li>
              <li>Khi luật pháp yêu cầu</li>
              <li>Để bảo vệ quyền và an toàn của chúng tôi hoặc người khác</li>
            </ul>
          </section>

          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">5. Quyền của bạn</h3>
            <p className="text-gray-700 text-sm">
              Bạn có các quyền sau đối với dữ liệu cá nhân của mình:
            </p>
            <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
              <li>Quyền truy cập thông tin của bạn</li>
              <li>Quyền chỉnh sửa thông tin không chính xác</li>
              <li>Quyền xóa thông tin trong một số trường hợp</li>
              <li>Quyền hạn chế hoặc phản đối việc xử lý</li>
              <li>Quyền rút lại sự đồng ý</li>
            </ul>
          </section>
        </div>

        <div className="sticky bottom-0 bg-white border-t border-gray-200 p-3 sm:p-4 flex justify-between items-center">
          <span className="text-xs text-gray-500">Cập nhật lần cuối: 27/03/2025</span>
          <button 
            onClick={onClose}
            className="px-4 sm:px-5 py-1.5 sm:py-2 bg-dexin-primary text-white rounded-full text-sm font-medium hover:bg-dexin-primary/90 transition-colors"
          >
            Đã hiểu
          </button>
        </div>
      </div>
    </div>
  );
};

export const TermsOfServiceModal = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden bg-black bg-opacity-60 flex items-center justify-center p-2 sm:p-4">
      <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] sm:max-h-[85vh] md:max-h-[80vh] overflow-hidden my-2 sm:my-4 animate-fadeIn">
        <div className="sticky top-0 bg-white border-b border-gray-200 p-3 sm:p-4 flex justify-between items-center z-10">
          <div className="flex items-center">
            <div className="bg-pink-100 rounded-full p-2 mr-3">
              <FontAwesomeIcon icon={faFileContract} className="text-dexin-primary h-4 w-4" />
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-gray-800">Điều Khoản Sử Dụng</h2>
          </div>
          <button 
            onClick={onClose}
            className="bg-gray-100 hover:bg-gray-200 rounded-full p-2 transition-colors"
          >
            <FontAwesomeIcon icon={faTimes} className="text-gray-600 h-4 w-4" />
          </button>
        </div>

        <div className="overflow-y-auto p-3 sm:p-5 space-y-4 sm:space-y-5" style={{maxHeight: "calc(90vh - 120px)"}}>
          <div className="p-3 sm:p-4 bg-dexin-bg/30 rounded-lg">
            <p className="text-gray-700 text-sm">
              Bằng cách truy cập hoặc sử dụng dịch vụ DEXIN, bạn đồng ý tuân thủ và chịu sự ràng buộc bởi các điều khoản 
              và điều kiện được nêu dưới đây. Vui lòng đọc kỹ trước khi sử dụng dịch vụ của chúng tôi.
            </p>
          </div>

          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">1. Chấp nhận điều khoản</h3>
            <p className="text-gray-700 text-sm">
              Bằng cách truy cập và sử dụng dịch vụ của chúng tôi, bạn xác nhận rằng bạn đã đọc, hiểu và 
              đồng ý tuân thủ các điều khoản này. Nếu bạn không đồng ý với bất kỳ phần nào của các điều khoản này, 
              vui lòng không sử dụng dịch vụ của chúng tôi.
            </p>
          </section>

          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">2. Tài khoản người dùng</h3>
            <p className="text-gray-700 text-sm">
              Khi tạo tài khoản với chúng tôi, bạn phải cung cấp thông tin chính xác, đầy đủ và cập nhật. 
              Bạn hoàn toàn chịu trách nhiệm về việc:
            </p>
            <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
              <li>Duy trì tính bảo mật của mật khẩu và tài khoản</li>
              <li>Tất cả hoạt động diễn ra dưới tài khoản của bạn</li>
              <li>Thông báo ngay cho chúng tôi về bất kỳ vi phạm bảo mật nào</li>
            </ul>
          </section>

          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">3. Quyền và giới hạn sử dụng</h3>
            <p className="text-gray-700 text-sm">
              Chúng tôi cấp cho bạn quyền sử dụng dịch vụ của chúng tôi cho mục đích cá nhân, phi thương mại. 
              Bạn không được:
            </p>
            <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
              <li>Sao chép, sửa đổi hoặc tạo các sản phẩm phái sinh từ dịch vụ của chúng tôi</li>
              <li>Sử dụng dịch vụ cho bất kỳ mục đích bất hợp pháp hoặc trái phép nào</li>
              <li>Thu thập hoặc khai thác dữ liệu từ dịch vụ của chúng tôi</li>
              <li>Gây hại đến hệ thống hoặc làm gián đoạn dịch vụ</li>
            </ul>
          </section>
          
          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">4. Nội dung người dùng</h3>
            <p className="text-gray-700 text-sm">
              Bạn giữ quyền sở hữu đối với nội dung bạn tạo và đăng trên dịch vụ của chúng tôi. 
              Tuy nhiên, bằng cách đăng nội dung, bạn cấp cho chúng tôi quyền sử dụng, sao chép, chỉnh sửa, 
              và hiển thị nội dung đó liên quan đến dịch vụ của chúng tôi.
            </p>
            <p className="text-gray-700 text-sm">
              Bạn đảm bảo rằng bạn có tất cả các quyền cần thiết để cấp quyền này và rằng nội dung của bạn 
              không vi phạm quyền của bất kỳ bên thứ ba nào.
            </p>
          </section>

          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">5. Chấm dứt</h3>
            <p className="text-gray-700 text-sm">
              Chúng tôi có thể chấm dứt hoặc đình chỉ quyền truy cập của bạn vào dịch vụ ngay lập tức, mà không 
              cần thông báo trước, nếu bạn vi phạm các Điều khoản này. Sau khi chấm dứt, quyền của bạn 
              để sử dụng dịch vụ sẽ ngay lập tức chấm dứt.
            </p>
          </section>
          
          <section className="space-y-2 sm:space-y-3">
            <h3 className="text-base sm:text-lg font-semibold text-dexin-primary">6. Thay đổi điều khoản</h3>
            <p className="text-gray-700 text-sm">
              Chúng tôi có thể sửa đổi hoặc cập nhật các Điều khoản này theo thời gian. Phiên bản mới nhất sẽ 
              được đăng trên trang web của chúng tôi với ngày có hiệu lực cập nhật. Việc bạn tiếp tục sử dụng 
              dịch vụ sau khi thay đổi có hiệu lực đồng nghĩa với việc bạn chấp nhận các điều khoản đã sửa đổi.
            </p>
          </section>
        </div>

        <div className="sticky bottom-0 bg-white border-t border-gray-200 p-3 sm:p-4 flex justify-between items-center">
          <span className="text-xs text-gray-500">Cập nhật lần cuối: 27/03/2025</span>
          <div className="flex items-center space-x-3">
            <button 
              onClick={onClose}
              className="px-4 sm:px-5 py-1.5 sm:py-2 bg-dexin-primary text-white rounded-full text-sm font-medium hover:bg-dexin-primary/90 transition-colors flex items-center"
            >
              <FontAwesomeIcon icon={faCheck} className="mr-2 h-3 w-3" />
              Tôi đồng ý
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}; 