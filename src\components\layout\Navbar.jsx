import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '../common';
import { Store, ShoppingBag, Menu, X, User, ChevronDown, MapPin, Package, LogOut, Palette, Heart } from 'lucide-react';
import { Avatar, AvatarImage, AvatarFallback } from '../ui/avatar';
import { useCart } from '../../context/CartContext';
import { useAuth } from '../../context/AuthContext';
import { motion, AnimatePresence } from 'motion/react';

// Component UserDropdown
const UserDropdown = ({ user, onLogout, getDisplayName, getAvatarUrl }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const navigate = useNavigate();

  // Tạo initials từ tên user để làm fallback
  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLogout = () => {
    onLogout();
    setIsOpen(false);
    navigate('/');
  };

  const menuItems = [
    { icon: User, label: 'Thông tin cá nhân', path: '/account', section: 'profile' },
    { icon: MapPin, label: 'Địa chỉ', path: '/account', section: 'address' },
    { icon: Package, label: 'Đơn hàng', path: '/account', section: 'orders' },
    { icon: Palette, label: 'Bản thiết kế', path: '/account', section: 'designs' },
    { icon: Heart, label: 'Danh sách yêu thích', path: '/wishlist' },
  ];

  return (
    <div className="relative" ref={dropdownRef}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-50 transition-colors"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-dexin-light">
          <img 
            src={getAvatarUrl()} 
            alt={getDisplayName()}
            className="w-full h-full object-cover"
            onError={(e) => {
              e.target.src = user?.gender === 'female' 
                ? 'https://via.placeholder.com/32/FE7CAB/FFFFFF?text=F'
                : 'https://via.placeholder.com/32/B90E56/FFFFFF?text=M';
            }}
          />
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-100 py-2 z-50"
          >
            {/* User Info */}
            <div className="px-4 py-3 border-b border-gray-100 bg-gradient-to-r from-dexin-light-10 to-dexin-light-5">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className="w-10 h-10 border-2 border-white shadow-sm">
                    <AvatarImage
                      src={user?.avatar && user.avatar !== 'default_avatar.png' ? user.avatar : undefined}
                      alt={getDisplayName() || 'User Avatar'}
                    />
                    <AvatarFallback className="bg-dexin-primary text-white text-sm font-medium">
                      {getInitials(getDisplayName())}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-semibold text-gray-900 truncate">
                    {getDisplayName()}
                  </p>
                  <p className="text-xs text-dexin-primary font-medium truncate">
                    @{user.userName}
                  </p>
                  {user?.email && (
                    <p className="text-xs text-gray-600 truncate">
                      {user.email}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-1">
              {menuItems.map((item, index) => {
                const handleClick = () => {
                  setIsOpen(false);
                  if (item.section && item.path === '/account') {
                    // Nếu có section, navigate với state để Account component biết section nào cần hiển thị
                    navigate(item.path, { state: { activeSection: item.section } });
                  } else {
                    navigate(item.path);
                  }
                };

                return (
                  <button
                    key={index}
                    onClick={handleClick}
                    className="flex items-center space-x-3 w-full px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-dexin-primary transition-colors text-left"
                  >
                    <item.icon className="w-4 h-4" />
                    <span className="text-sm">{item.label}</span>
                  </button>
                );
              })}
            </div>

            {/* Logout */}
            <div className="border-t border-gray-100 py-1">
              <button
                onClick={handleLogout}
                className="flex items-center space-x-3 w-full px-4 py-2 text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                <span className="text-sm">Đăng Xuất</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [shouldFixNav, setShouldFixNav] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  const { cartCount } = useCart();
  const { user, logout, isAuthenticated, getDisplayName, getAvatarUrl } = useAuth();

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Chỉ cố định navbar nếu scroll xuống quá 100px
      setShouldFixNav(currentScrollY > 100);

      // Hiện thanh màu hồng scroll xuống quá 50px
      setIsScrolled(currentScrollY > 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleMobileLogout = () => {
    logout();
    setMobileMenuOpen(false);
  };

  return (
    <nav className={`w-full z-50 transition-all duration-300 bg-white ${
      shouldFixNav
        ? 'fixed top-0'
        : 'relative'
    }`}>
      {/* Banner giảm giá */}
      <div className={`bg-pink-100 text-center transition-all duration-300 ${
        isScrolled && shouldFixNav ? 'h-0 overflow-hidden' : 'h-8 py-1.5'
      }`}>
        <div className="text-sm font-dexin">DEXIN giảm giá 10% gói tân trang nhà trong dịp ra mắt</div>
      </div>

      <div className={`border-b border-gray-100 transition-all duration-300 ${
        isScrolled && shouldFixNav ? 'shadow-md shadow-dexin-light-20' : ''
      }`}>
        <div className="container mx-auto py-3 px-4 sm:px-6 flex justify-between items-center h-auto sm:h-16 md:h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/">
              <img src="/images/Logo DEXIN finall 2.png" alt="DEXIN" className={`transition-all duration-300 ${
                isScrolled && shouldFixNav ? 'h-7 sm:h-8' : 'h-8 sm:h-9'
              }`} />
            </Link>
          </div>

          {/* Menu chính - Desktop */}
          <div className="hidden lg:flex items-center space-x-6 xl:space-x-12">
            <Link to="/phac-y" className="px-3 py-6 pb-4 text-gray-800 hover:text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all relative">Phác ý</Link>
            <Link to="/mo-loi" className="px-3 py-6 pb-4 text-gray-800 hover:text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all relative">Mở lối</Link>
            <Link to="/ngo-loi" className="px-3 py-6 pb-4 text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all font-bold relative">Ngõ lời</Link>
            <Link to="/chuyen-nha" className="px-3 py-6 pb-4 text-gray-800 hover:text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all relative">Chuyện nhà</Link>
            <Link to="/chung-nhip" className="px-3 py-6 pb-4 text-gray-800 hover:text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all relative">Chung nhịp</Link>
          </div>

          {/* Buttons - Desktop */}
          <div className="hidden md:flex items-center space-x-3 lg:space-x-6">
            {isAuthenticated ? (
              // Hiển thị khi đã đăng nhập
              <>
                <Link
                  to="/store"
                  className="ml-2 hover:text-dexin-light transition-colors relative group"
                  title="Cửa hàng"
                >
                  <Store className="h-5 w-5 lg:h-6 lg:w-6" />
                  <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Cửa hàng
                  </span>
                </Link>
                <Link
                  to="/cart"
                  className="ml-2 hover:text-dexin-light transition-colors relative group"
                  title="Giỏ hàng"
                >
                  <ShoppingBag className="h-5 w-5 lg:h-6 lg:w-6" />
                  {cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-dexin-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                      {cartCount > 99 ? '99+' : cartCount}
                    </span>
                  )}
                  <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Giỏ hàng {cartCount > 0 && `(${cartCount})`}
                  </span>
                </Link>
                <UserDropdown
                  user={user}
                  onLogout={logout}
                  getDisplayName={getDisplayName}
                  getAvatarUrl={getAvatarUrl}
                />
              </>
            ) : (
              // Hiển thị khi chưa đăng nhập
              <>
                <Link to="/login" className="ml-2">
                  <Button variant="outline" size="sm" className="lg:text-base">
                    Về nhà
                  </Button>
                </Link>
                <Link to="/signup" className="ml-2">
                  <Button variant="primary" size="sm" className="lg:text-base">
                    Gõ cửa
                  </Button>
                </Link>
                <Link to="/store" className="ml-2 hover:text-dexin-light transition-colors">
                  <Store className="h-5 w-5 lg:h-6 lg:w-6" />
                </Link>
                <Link to="/cart" className="ml-2 hover:text-dexin-light transition-colors relative">
                  <ShoppingBag className="h-5 w-5 lg:h-6 lg:w-6" />
                  {cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-dexin-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                      {cartCount > 99 ? '99+' : cartCount}
                    </span>
                  )}
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="flex md:hidden items-center">
            <button
              onClick={toggleMobileMenu}
              className="p-2 rounded-md text-gray-600 hover:text-dexin-primary focus:outline-none"
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-white py-4 px-6 shadow-lg border-t border-gray-100 animate-fadeIn">
            <div className="flex flex-col space-y-4">
              <Link to="/phac-y" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Phác ý</Link>
              <Link to="/mo-loi" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Mở lối</Link>
              <Link to="/ngo-loi" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Ngõ lời</Link>
              <Link to="/chuyen-nha" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Chuyện nhà</Link>
              <Link to="/chung-nhip" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Chung nhịp</Link>

              {isAuthenticated ? (
                // Mobile menu khi đã đăng nhập
                <div className="pt-4 border-t border-gray-100">
                  {/* User info */}
                  <div className="flex items-center space-x-3 mb-4 p-3 bg-gradient-to-r from-dexin-light-10 to-dexin-light-5 rounded-lg">
                    <div className="relative">
                      <Avatar className="w-12 h-12 border-2 border-white shadow-sm">
                        <AvatarImage
                          src={user?.avatar && user.avatar !== 'default_avatar.png' ? user.avatar : undefined}
                          alt={getDisplayName() || 'User Avatar'}
                        />
                        <AvatarFallback className="bg-dexin-primary text-white text-sm font-medium">
                          {(() => {
                            const name = getDisplayName();
                            if (!name) return 'U';
                            return name
                              .split(' ')
                              .map(word => word.charAt(0))
                              .join('')
                              .toUpperCase()
                              .slice(0, 2);
                          })()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-gray-900 truncate">
                        {getDisplayName()}
                      </p>
                      <p className="text-xs text-dexin-primary font-medium truncate">
                        @{user.userName}
                      </p>
                      {user?.email && (
                        <p className="text-xs text-gray-600 truncate">
                          {user.email}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Mobile user menu items */}
                  <div className="space-y-2 mb-4">
                    <button
                      onClick={() => {
                        setMobileMenuOpen(false);
                        navigate('/account', { state: { activeSection: 'profile' } });
                      }}
                      className="flex items-center space-x-3 py-2 text-gray-700 hover:text-dexin-primary w-full text-left"
                    >
                      <User className="w-4 h-4" />
                      <span>Thông tin cá nhân</span>
                    </button>
                    <button
                      onClick={() => {
                        setMobileMenuOpen(false);
                        navigate('/account', { state: { activeSection: 'address' } });
                      }}
                      className="flex items-center space-x-3 py-2 text-gray-700 hover:text-dexin-primary w-full text-left"
                    >
                      <MapPin className="w-4 h-4" />
                      <span>Địa chỉ</span>
                    </button>
                    <button
                      onClick={() => {
                        setMobileMenuOpen(false);
                        navigate('/account', { state: { activeSection: 'orders' } });
                      }}
                      className="flex items-center space-x-3 py-2 text-gray-700 hover:text-dexin-primary w-full text-left"
                    >
                      <Package className="w-4 h-4" />
                      <span>Đơn hàng</span>
                    </button>
                    <button
                      onClick={() => {
                        setMobileMenuOpen(false);
                        navigate('/account', { state: { activeSection: 'designs' } });
                      }}
                      className="flex items-center space-x-3 py-2 text-gray-700 hover:text-dexin-primary w-full text-left"
                    >
                      <Palette className="w-4 h-4" />
                      <span>Bản thiết kế</span>
                    </button>
                    <Link to="/wishlist" className="flex items-center space-x-3 py-2 text-gray-700 hover:text-dexin-primary" onClick={() => setMobileMenuOpen(false)}>
                      <Heart className="w-4 h-4" />
                      <span>Danh sách yêu thích</span>
                    </Link>
                  </div>

                  <div className="flex items-center space-x-4 pt-2 border-t border-gray-100">
                    <Link to="/store" className="hover:text-dexin-light transition-colors" onClick={() => setMobileMenuOpen(false)}>
                      <Store className="h-5 w-5" />
                    </Link>
                    <Link to="/cart" className="hover:text-dexin-light transition-colors relative" onClick={() => setMobileMenuOpen(false)}>
                      <ShoppingBag className="h-5 w-5" />
                      {cartCount > 0 && (
                        <span className="absolute -top-1 -right-1 bg-dexin-primary text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                          {cartCount > 9 ? '9+' : cartCount}
                        </span>
                      )}
                    </Link>
                    <button
                      onClick={handleMobileLogout}
                      className="flex items-center space-x-2 text-red-600 hover:text-red-700 ml-auto"
                    >
                      <LogOut className="w-4 h-4" />
                      <span className="text-sm">Đăng xuất</span>
                    </button>
                  </div>
                </div>
              ) : (
                // Mobile menu khi chưa đăng nhập
                <div className="flex items-center space-x-4 pt-2 border-t border-gray-100">
                  <Link to="/login" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="outline" size="sm" className="border-dexin-light text-dexin-light">
                      Về nhà
                    </Button>
                  </Link>
                  <Link to="/signup" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="primary" size="sm">
                      Gõ cửa
                    </Button>
                  </Link>
                  <Link to="/store" className="ml-auto hover:text-dexin-light transition-colors" onClick={() => setMobileMenuOpen(false)}>
                    <Store className="h-5 w-5" />
                  </Link>
                  <Link to="/cart" className="hover:text-dexin-light transition-colors relative" onClick={() => setMobileMenuOpen(false)}>
                    <ShoppingBag className="h-5 w-5" />
                    {cartCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-dexin-primary text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                        {cartCount > 9 ? '9+' : cartCount}
                      </span>
                    )}
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;