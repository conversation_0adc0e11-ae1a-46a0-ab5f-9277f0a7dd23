import React, { useRef, useState, useEffect } from 'react';
import { Stage, Layer, Line, Circle } from 'react-konva';
import Konva from 'konva';
import { ZoomIn, ZoomOut, Maximize } from 'lucide-react';
import { AnimatePresence } from 'motion/react';
import FurnitureImage from './FurnitureImage';
import LayersPanel from './LayersPanel';

const Canvas = ({
  items,
  selectedId,
  setSelectedId,
  handleItemChange,
  handleDrop,
  isLocked,
  viewMode,
  stageRef // Nhận stageRef từ component cha
}) => {
  // Nếu không có stageRef từ component cha, tạo một ref mới
  const internalStageRef = useRef();
  const layerRef = useRef();

  // Sử dụng stageRef từ props nếu có, nếu không thì sử dụng ref nội bộ
  const actualStageRef = stageRef || internalStageRef;
  const [stageSize, setStageSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });

  // State cho tỷ lệ zoom và vị trí
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const SCALE_BY = 1.1; // Hệ số zoom

  // Xử lý khi thay đổi kích thước cửa sổ
  useEffect(() => {
    const handleResize = () => {
      setStageSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    // Gọi handleResize ngay lập tức để thiết lập kích thước ban đầu
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Xử lý zoom bằng chuột
  useEffect(() => {
    const handleWheel = (e) => {
      if (!actualStageRef.current) return;

      // Ngăn chặn hành vi mặc định của trình duyệt
      e.preventDefault();

      const stage = actualStageRef.current;
      const oldScale = scale;

      // Xác định hướng cuộn
      const direction = e.deltaY > 0 ? -1 : 1;

      // Tính toán scale mới
      let newScale = direction > 0 ? oldScale * SCALE_BY : oldScale / SCALE_BY;

      // Giới hạn scale tối thiểu và tối đa
      newScale = Math.max(0.3, Math.min(newScale, 5));

      // Nếu scale không thay đổi, không làm gì cả
      if (newScale === oldScale) return;

      // Lấy vị trí con trỏ chuột
      const pointer = stage.getPointerPosition();

      if (!pointer) return;

      // Tính toán vị trí mới
      const mousePointTo = {
        x: (pointer.x - position.x) / oldScale,
        y: (pointer.y - position.y) / oldScale,
      };

      const newPos = {
        x: pointer.x - mousePointTo.x * newScale,
        y: pointer.y - mousePointTo.y * newScale,
      };

      // Cập nhật state
      setScale(newScale);
      setPosition(newPos);

      // Áp dụng scale và position mới vào stage
      stage.scale({ x: newScale, y: newScale });
      stage.position(newPos);
      stage.batchDraw();
    };

    // Thêm event listener cho wheel event
    const container = actualStageRef.current?.container();
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
    }

    // Cleanup
    return () => {
      const container = actualStageRef.current?.container();
      if (container) {
        container.removeEventListener('wheel', handleWheel);
      }
    };
  }, [scale, position, SCALE_BY, actualStageRef]);

  // Xử lý khi click vào canvas để bỏ chọn
  const checkDeselect = (e) => {
    if (isLocked) return;

    // Nếu đang ở chế độ hand, bỏ chọn tất cả và không xử lý thêm
    if (viewMode === 'hand') {
      if (selectedId) {
        setSelectedId(null);
      }
      return;
    }

    // Lấy đối tượng được click và tên/loại của nó
    const clickedTarget = e.target;
    const targetName = clickedTarget.name();
    const targetType = clickedTarget.getType();

    const clickedOnEmpty =
      clickedTarget === e.target.getStage() ||
      targetName === 'background-grid' ||
      (targetType !== 'Image' &&
       !targetName?.includes('transformer') &&
       !targetName?.includes('anchor'));

    // Xử lý bỏ chọn khi click vào khoảng trống
    if (clickedOnEmpty && selectedId) {
      // Chỉ bỏ chọn nếu đã có phần tử được chọn trước đó
      setSelectedId(null);

      // Tùy chọn: Thêm hiệu ứng phản hồi trực quan khi bỏ chọn
      if (actualStageRef.current) {
        // Tạo hiệu ứng gợn sóng nhỏ tại vị trí click
        const stage = actualStageRef.current;
        const pointerPosition = stage.getPointerPosition();

        if (pointerPosition) {
          // Tạo một layer cho hiệu ứng gợn sóng
          const layer = layerRef.current;

          // Tạo một component Circle cho hiệu ứng gợn sóng
          const circle = new Konva.Circle({
            x: pointerPosition.x,
            y: pointerPosition.y,
            radius: 0,
            fill: 'rgba(200, 200, 200, 0.3)',
            stroke: 'rgba(200, 200, 200, 0.5)',
            strokeWidth: 1,
          });

          // Thêm circle vào layer
          layer.add(circle);

          // Tạo animation cho circle
          circle.to({
            duration: 0.3,
            radius: 30,
            opacity: 0,
            onFinish: () => {
              circle.destroy();
              layer.batchDraw();
            }
          });

          layer.batchDraw();
        }
      }
    }
  };

  // Đảm bảo stage có kích thước phù hợp sau khi mount
  useEffect(() => {
    if (actualStageRef.current) {
      actualStageRef.current.batchDraw();
    }
  }, [stageSize]);

  // Hàm xử lý zoom in
  const handleZoomIn = () => {
    if (actualStageRef.current) {
      const oldScale = scale;
      const newScale = oldScale * SCALE_BY;

      setScale(newScale);

      const stage = actualStageRef.current;
      const pointer = {
        x: stage.width() / 2,
        y: stage.height() / 2,
      };

      const mousePointTo = {
        x: (pointer.x - position.x) / oldScale,
        y: (pointer.y - position.y) / oldScale,
      };

      const newPos = {
        x: pointer.x - mousePointTo.x * newScale,
        y: pointer.y - mousePointTo.y * newScale,
      };

      setPosition(newPos);

      // Áp dụng scale và position mới vào stage
      stage.scale({ x: newScale, y: newScale });
      stage.position(newPos);
      stage.batchDraw();
    }
  };

  // Hàm xử lý zoom out
  const handleZoomOut = () => {
    if (actualStageRef.current) {
      const oldScale = scale;
      const newScale = oldScale / SCALE_BY;

      // Giới hạn scale tối thiểu
      if (newScale < 0.3) return;

      setScale(newScale);

      const stage = actualStageRef.current;
      const pointer = {
        x: stage.width() / 2,
        y: stage.height() / 2,
      };

      const mousePointTo = {
        x: (pointer.x - position.x) / oldScale,
        y: (pointer.y - position.y) / oldScale,
      };

      const newPos = {
        x: pointer.x - mousePointTo.x * newScale,
        y: pointer.y - mousePointTo.y * newScale,
      };

      setPosition(newPos);

      // Áp dụng scale và position mới vào stage
      stage.scale({ x: newScale, y: newScale });
      stage.position(newPos);
      stage.batchDraw();
    }
  };

  // Hàm xử lý reset zoom
  const handleResetZoom = () => {
    if (actualStageRef.current) {
      setScale(1);
      setPosition({ x: 0, y: 0 });

      const stage = actualStageRef.current;
      stage.scale({ x: 1, y: 1 });
      stage.position({ x: 0, y: 0 });
      stage.batchDraw();
    }
  };

  // State để theo dõi hiển thị tooltip
  const [showKeyboardTip, setShowKeyboardTip] = useState(false);

  // Hiển thị gợi ý phím khi một item được chọn
  useEffect(() => {
    if (selectedId && !isLocked) {
      setShowKeyboardTip(true);

      // Ẩn gợi ý sau 3 giây
      const timer = setTimeout(() => {
        setShowKeyboardTip(false);
      }, 3000);

      return () => clearTimeout(timer);
    } else {
      setShowKeyboardTip(false);
    }
  }, [selectedId, isLocked]);

  // Cập nhật cursor dựa trên viewMode và isLocked
  useEffect(() => {
    const stage = actualStageRef.current;
    if (!stage) return;

    const container = stage.container();

    if (isLocked) {
      // Khi bị khóa: cursor là not-allowed (không cho phép)
      container.style.cursor = 'not-allowed';
    } else if (viewMode === 'hand') {
      // Chế độ hand: cursor là grab (bàn tay)
      container.style.cursor = 'grab';

      // Đảm bảo không có item nào được chọn khi chuyển sang chế độ hand
      if (selectedId) {
        setSelectedId(null);
      }
    } else {
      // Chế độ khác: cursor mặc định
      container.style.cursor = 'default';
    }
  }, [viewMode, isLocked, selectedId, actualStageRef]);



  // Hàm xử lý thay đổi danh sách items
  const handleItemsChange = (newItems) => {
    // Gọi hàm handleItemChange từ component cha
    // Đây là hàm được truyền từ PhacY component để cập nhật state items
    handleItemChange(newItems);
  };

  // Hàm xử lý xóa item từ LayersPanel
  const handleItemDelete = (itemId) => {
    if (isLocked) return;

    // Xóa item khỏi danh sách
    const newItems = items.filter(item => item.id !== itemId);

    // Nếu item đang được chọn, bỏ chọn
    if (selectedId === itemId) {
      setSelectedId(null);
    }

    // Cập nhật danh sách items
    handleItemsChange(newItems);
  };

  // Mobile drop event handler
  const handleMobileDrop = (e) => {
    if (isLocked) {
      return;
    }

    const { item, x, y } = e.detail;

    // Convert screen coordinates to stage coordinates
    const stage = actualStageRef.current;
    if (!stage) return;

    // Account for stage position and scale
    const stagePos = stage.position();
    const stageScale = stage.scaleX(); // Assuming uniform scaling

    const stageX = (x - stagePos.x) / stageScale;
    const stageY = (y - stagePos.y) / stageScale;

    // Tạo synthetic event tương tự như desktop drop
    const syntheticEvent = {
      preventDefault: () => {},
      currentTarget: e.currentTarget,
      clientX: x,
      clientY: y,
      stageX: stageX,
      stageY: stageY,
      dataTransfer: {
        getData: () => JSON.stringify(item)
      }
    };

    // Gọi handleDrop với synthetic event
    handleDrop(syntheticEvent);
  };

  // Setup mobile drop event listener
  useEffect(() => {
    const canvasContainer = document.querySelector('[data-canvas-container="true"]');

    if (canvasContainer) {
      canvasContainer.addEventListener('mobileDrop', handleMobileDrop);

      return () => {
        canvasContainer.removeEventListener('mobileDrop', handleMobileDrop);
      };
    }
  }, []); // Empty dependency array để chỉ setup một lần

  return (
    <div
      className={`w-full h-full bg-gray-50 absolute inset-0 overflow-hidden ${isLocked ? 'cursor-not-allowed' : ''}`}
      onDrop={isLocked ? undefined : handleDrop}
      onDragOver={(e) => e.preventDefault()}
      data-canvas-container="true"
      style={{
        touchAction: 'none', // Prevent default touch behaviors
        userSelect: 'none',  // Prevent text selection
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none'
      }}
    >
      {/* Tooltip phím tắt */}
      {showKeyboardTip && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg shadow-lg z-50 opacity-90 transition-opacity duration-300 flex items-center max-w-[90vw] sm:max-w-md text-xs sm:text-sm">
          <span className="mr-1 sm:mr-2">Nhấn</span>
          <span className="bg-gray-700 text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded font-mono text-xs sm:text-sm">Delete</span>
          <span className="ml-1 sm:ml-2">để xóa đối tượng đã chọn</span>
        </div>
      )}

      {/* Hiển thị LayersPanel khi ở chế độ layers */}
      <AnimatePresence>
        {viewMode === 'layers' && (
          <LayersPanel
            items={items}
            selectedId={selectedId}
            setSelectedId={setSelectedId}
            onItemChange={handleItemsChange}
            onItemDelete={handleItemDelete}
            isLocked={isLocked}
          />
        )}
      </AnimatePresence>

      <Stage
        width={stageSize.width}
        height={stageSize.height}
        onMouseDown={checkDeselect}
        onTouchStart={checkDeselect} // Thêm touch support cho deselect
        ref={actualStageRef}
        style={{
          backgroundImage: 'linear-gradient(to right, #eee 1px, transparent 1px), linear-gradient(to bottom, #eee 1px, transparent 1px)',
          backgroundSize: '20px 20px'
        }}
        perfectDrawEnabled={false}
        listening={!isLocked} // Vô hiệu hóa tất cả các sự kiện khi isLocked là true
        scaleX={scale}
        scaleY={scale}
        x={position.x}
        y={position.y}
        draggable={viewMode === 'hand' && !isLocked} // Chỉ cho phép kéo khi không bị khóa
        onDragStart={(e) => {
          if (viewMode === 'hand' && !isLocked) {
            // Thay đổi cursor khi bắt đầu kéo
            const container = e.target.getStage().container();
            container.style.cursor = 'grabbing';

            // Đảm bảo không có item nào được chọn khi bắt đầu kéo canvas
            if (selectedId) {
              setSelectedId(null);
            }

            // Ngăn chặn sự kiện lan truyền đến các đối tượng con
            e.cancelBubble = true;
          }
        }}
        onDragEnd={(e) => {
          if (viewMode === 'hand' && !isLocked) {
            // Cập nhật position state sau khi kéo xong
            setPosition({
              x: e.target.x(),
              y: e.target.y()
            });

            // Khôi phục cursor
            const container = e.target.getStage().container();
            container.style.cursor = 'grab';

            // Ngăn chặn sự kiện lan truyền đến các đối tượng con
            e.cancelBubble = true;
          }
        }}
        onDragMove={(e) => {
          if (viewMode === 'hand' && !isLocked) {
            // Cập nhật position state trong quá trình kéo (nếu cần)
            setPosition({
              x: e.target.x(),
              y: e.target.y()
            });

            // Ngăn chặn sự kiện lan truyền đến các đối tượng con
            e.cancelBubble = true;
          }
        }}
      >
        <Layer ref={layerRef}>
          {/* Lưới nền */}
          <Line
            name="background-grid"
            points={[
              0, 0,
              stageSize.width, 0,
              stageSize.width, stageSize.height,
              0, stageSize.height,
              0, 0
            ]}
            stroke="#ddd"
            strokeWidth={1}
            closed
          />

          {/* Sắp xếp items để background phòng hiển thị ở dưới cùng */}
          {items
            .sort((a, b) => {
              // Đặt các item background phòng xuống dưới cùng (render trước)
              if (a.isRoomBackground && !b.isRoomBackground) return -1;
              if (!a.isRoomBackground && b.isRoomBackground) return 1;
              return 0;
            })
            .map((item) => (
              <FurnitureImage
                key={item.id}
                src={item.src}
                x={item.x}
                y={item.y}
                width={item.width}
                height={item.height}
                rotation={item.rotation || 0}
                isSelected={item.id === selectedId}
                onSelect={() => !isLocked && viewMode !== 'hand' && setSelectedId(item.id)}
                onChange={(newAttrs) => {
                  // Tạo một item đã cập nhật với tất cả thuộc tính cần thiết
                  const updatedItem = {
                    ...item, // Giữ tất cả thuộc tính ban đầu
                    ...newAttrs, // Áp dụng các cập nhật
                    id: item.id, // Đảm bảo giữ nguyên ID ban đầu
                    src: item.src, // Đảm bảo giữ nguyên hình ảnh nguồn
                    name: item.name // Giữ nguyên tên nếu có
                  };
                  handleItemChange(updatedItem);
                }}
                draggable={!isLocked && viewMode !== 'hand'}
                viewMode={viewMode}
                item={item} // Pass the entire item object
              />
            ))}
        </Layer>
      </Stage>

      {/* Khối điều chỉnh kích cỡ view ở góc phải dưới - nằm ngang */}
      <div className="fixed bottom-[72px] sm:bottom-6 right-4 sm:right-6 flex items-center bg-white rounded-full shadow-lg overflow-hidden z-50 px-1.5 sm:px-2 py-1 sm:py-1.5">
        <button
          className={`p-1.5 sm:p-2 rounded-full transition-colors duration-200 flex items-center justify-center ${
            isLocked ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-100'
          }`}
          onClick={isLocked ? undefined : handleZoomOut}
          title={isLocked ? "Đã khóa" : "Thu nhỏ"}
          disabled={isLocked}
        >
          <ZoomOut size={16} className="text-gray-700 sm:hidden" />
          <ZoomOut size={18} className="text-gray-700 hidden sm:block" />
        </button>

        <div className="px-2 sm:px-3 py-0.5 sm:py-1 mx-0.5 sm:mx-1 bg-white text-center text-xs sm:text-sm font-medium text-gray-700">
          {Math.round(scale * 100)}%
        </div>

        <button
          className={`p-1.5 sm:p-2 rounded-full transition-colors duration-200 flex items-center justify-center ${
            isLocked ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-100'
          }`}
          onClick={isLocked ? undefined : handleZoomIn}
          title={isLocked ? "Đã khóa" : "Phóng to"}
          disabled={isLocked}
        >
          <ZoomIn size={16} className="text-gray-700 sm:hidden" />
          <ZoomIn size={18} className="text-gray-700 hidden sm:block" />
        </button>

        <div className="h-3 sm:h-4 mx-0.5 sm:mx-1 w-px bg-gray-200"></div>

        <button
          className={`p-1.5 sm:p-2 rounded-full transition-colors duration-200 flex items-center justify-center ${
            isLocked ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-100'
          }`}
          onClick={isLocked ? undefined : handleResetZoom}
          title={isLocked ? "Đã khóa" : "Khôi phục kích thước"}
          disabled={isLocked}
        >
          <Maximize size={16} className="text-gray-700 sm:hidden" />
          <Maximize size={18} className="text-gray-700 hidden sm:block" />
        </button>
      </div>
    </div>
  );
};

export default Canvas;
