import React from 'react';
import { Button } from '../common';
import { Brush, MessageCircleMore } from 'lucide-react';
import { Link } from 'react-router-dom';

const Hero = () => {
  return (
    <>
      <section className="bg-dexin-bg py-8 sm:py-12 md:py-24">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <h1 className="text-3xl sm:text-5xl md:text-7xl font-bold leading-tight mb-6 sm:mb-8 font-dexin">
            <div className="mb-2">
              <span className="text-dexin-light">
                Mỗi <span className="text-dexin-primary">ý tưởng</span> đều có một nơi
              </span>
            </div>
            <span className="text-dexin-light">
              để <span className="text-dexin-primary">nương tựa</span>
            </span>
            <br className="hidden sm:block" />
            <div className="mt-4">
              <span className="text-dexin-light">
                <span className="text-dexin-primary">–</span> hãy{' '}
                <span className="text-dexin-primary">bắt đầu từ đây</span>
              </span>
            </div>
            <div className="flex justify-center">
              <img 
                src="/images/corner-right-down.png" 
                alt="Corner Right Down" 
                className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 mt-4" 
              />
            </div>
          </h1>

          <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4 mt-8">
            <Link to="/phac-y" className="w-full sm:w-auto">
              <Button
                variant="secondary"
                size="lg"
                className="w-full sm:w-auto px-6 sm:px-10 md:px-20 py-3 sm:py-4 md:py-5 text-xl sm:text-2xl md:text-3xl rounded-full"
              >
                Phác ý <Brush className="ml-2 h-5 w-5 sm:h-6 sm:w-6" />
              </Button>
            </Link>
            <Link to="/ngo-loi" className="w-full sm:w-auto">
              <Button
                variant="secondary"
                size="lg"
                className="w-full sm:w-auto px-6 sm:px-10 md:px-20 py-3 sm:py-4 md:py-5 text-xl sm:text-2xl md:text-3xl rounded-full"
              >
                Ngỏ lời <MessageCircleMore className="ml-2 h-5 w-5 sm:h-6 sm:w-6" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <div className="w-full">
        <img
          src="/images/Mask group.png"
          alt="DEXIN Thiết kế nội thất"
          className="w-full h-auto"
        />
      </div>
    </>
  );
};

export default Hero;